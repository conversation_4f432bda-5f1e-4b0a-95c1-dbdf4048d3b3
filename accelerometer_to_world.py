import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as R
from interpolation import sort_and_clean_data

def read_sensor_data(file_path):
    
    """读取传感器数据CSV文件"""
    try:
        # 尝试读取数据，处理可能的分隔符问题
        df = pd.read_csv(file_path, sep='\t')
        if df.shape[1] == 1:  # 如果只有一列，可能是逗号分隔
            df = pd.read_csv(file_path)
        return df
    except Exception as e:
        print(f"读取文件出错: {e}")
        return None

def match_data_by_timestamp(accel_df, quat_df):
    """按照传感器时间戳匹配加速度和四元数数据"""
    print(f"加速度数据: {len(accel_df)} 行")
    print(f"四元数数据: {len(quat_df)} 行")
    
    # 将传感器时间戳转换为数值类型
    accel_df['sensor_timestamp'] = pd.to_numeric(accel_df['sensor_timestamp'])
    quat_df['sensor_timestamp'] = pd.to_numeric(quat_df['sensor_timestamp'])
    
    # 创建结果DataFrame
    result = pd.DataFrame()
    
    # 复制时间列
    result['time'] = accel_df['time']
    result['sensor_timestamp'] = accel_df['sensor_timestamp']
    
    # 复制加速度数据
    result['accel_x'] = accel_df['x']
    result['accel_y'] = accel_df['y']
    result['accel_z'] = accel_df['z']
    
    # 为每个加速度数据找到最接近的四元数数据
    result['quat_x'] = np.nan
    result['quat_y'] = np.nan
    result['quat_z'] = np.nan
    result['quat_w'] = np.nan
    
    # 为了加快匹配速度，将四元数数据存储为字典
    quat_dict = {}
    for _, row in quat_df.iterrows():
        quat_dict[row['sensor_timestamp']] = (row[2], row[3], row[4], row[5])
    
    # 对每个加速度数据找最近的四元数
    for i, row in result.iterrows():
        accel_ts = row['sensor_timestamp']
        closest_ts = min(quat_dict.keys(), key=lambda x: abs(x - accel_ts))
        result.loc[i, 'quat_x'], result.loc[i, 'quat_y'], result.loc[i, 'quat_z'], result.loc[i, 'quat_w'] = quat_dict[closest_ts]
    
    print(f"匹配完成，结果数据: {len(result)} 行")
    return result

def convert_to_world_coordinates(matched_df):
    """使用四元数将设备坐标系中的加速度转换到世界坐标系"""
    # 创建转换结果列
    matched_df['world_accel_x'] = np.nan
    matched_df['world_accel_y'] = np.nan
    matched_df['world_accel_z'] = np.nan
    
    for i, row in matched_df.iterrows():
        # 获取四元数值并创建旋转对象
        qx, qy, qz, qw = row['quat_x'], row['quat_y'], row['quat_z'], row['quat_w']
        rotation = R.from_quat([qx, qy, qz, qw])
        
        # 获取当前加速度值
        accel = np.array([row['accel_x'], row['accel_y'], row['accel_z']])
        
        # 应用旋转将加速度从设备坐标系转换到世界坐标系
        world_accel = rotation.apply(accel)
        
        # 存储结果
        matched_df.loc[i, 'world_accel_x'] = world_accel[0]
        matched_df.loc[i, 'world_accel_y'] = world_accel[1]
        matched_df.loc[i, 'world_accel_z'] = world_accel[2]
    
    return matched_df

def plot_accelerometer_data(df, title="Accelerometer Data Comparison"):
    """绘制原始加速度和转换后的加速度数据"""
    plt.figure(figsize=(12, 8))
    
    # 设置时间轴
    if 'time' in df.columns:
        # 转换时间字符串为秒数（相对于第一个数据点）
        try:
            base_time = pd.to_datetime(df['time'].iloc[0], format='%H-%M-%S.%f')
            time_series = pd.to_datetime(df['time'], format='%H-%M-%S.%f')
            time_seconds = [(t - base_time).total_seconds() for t in time_series]
        except:
            # 如果时间格式转换失败，使用索引作为时间
            time_seconds = np.arange(len(df))
    else:
        time_seconds = np.arange(len(df))

    # 绘制原始加速度数据
    plt.subplot(2, 1, 1)
    plt.plot(time_seconds, df['accel_x'], 'r-', label='X-axis')
    plt.plot(time_seconds, df['accel_y'], 'g-', label='Y-axis')
    plt.plot(time_seconds, df['accel_z'], 'b-', label='Z-axis')
    plt.title('Original Accelerometer Data (Device Coordinates)')
    plt.xlabel('Time (seconds)')
    plt.ylabel('Acceleration (m/s²)')
    plt.grid(True)
    plt.legend()

    # 绘制转换后的世界坐标系加速度数据
    plt.subplot(2, 1, 2)
    plt.plot(time_seconds, df['world_accel_x'], 'r-', label='X-axis')
    plt.plot(time_seconds, df['world_accel_y'], 'g-', label='Y-axis')
    plt.plot(time_seconds, df['world_accel_z'], 'b-', label='Z-axis')
    plt.title('World Coordinate System Accelerometer Data')
    plt.xlabel('Time (seconds)')
    plt.ylabel('Acceleration (m/s²)')
    plt.grid(True)
    plt.legend()

    plt.tight_layout()
    plt.suptitle(title, fontsize=16)
    plt.subplots_adjust(top=0.9)
    
    return plt

def plot_accelerometer_data_separated(df, title="Accelerometer Data - Separated View"):
    """分别绘制三轴加速度数据，避免曲线重叠"""
    fig, axes = plt.subplots(3, 2, figsize=(14, 10), sharex=True)
    
    # 设置时间轴
    if 'time' in df.columns:
        # 转换时间字符串为秒数（相对于第一个数据点）
        try:
            base_time = pd.to_datetime(df['time'].iloc[0], format='%H-%M-%S.%f')
            time_series = pd.to_datetime(df['time'], format='%H-%M-%S.%f')
            time_seconds = [(t - base_time).total_seconds() for t in time_series]
        except:
            # 如果时间格式转换失败，使用索引作为时间
            time_seconds = np.arange(len(df))
    else:
        time_seconds = np.arange(len(df))
    
    # 原始加速度数据 - X轴
    axes[0, 0].plot(time_seconds, df['accel_x'], 'r-')
    axes[0, 0].set_title('Device X-axis Acceleration')
    axes[0, 0].set_ylabel('Acceleration (m/s²)')
    axes[0, 0].grid(True)
    
    # 原始加速度数据 - Y轴
    axes[1, 0].plot(time_seconds, df['accel_y'], 'g-')
    axes[1, 0].set_title('Device Y-axis Acceleration')
    axes[1, 0].set_ylabel('Acceleration (m/s²)')
    axes[1, 0].grid(True)
    
    # 原始加速度数据 - Z轴
    axes[2, 0].plot(time_seconds, df['accel_z'], 'b-')
    axes[2, 0].set_title('Device Z-axis Acceleration')
    axes[2, 0].set_xlabel('Time (seconds)')
    axes[2, 0].set_ylabel('Acceleration (m/s²)')
    axes[2, 0].grid(True)
    
    # 世界坐标系加速度数据 - X轴
    axes[0, 1].plot(time_seconds, df['world_accel_x'], 'r-')
    axes[0, 1].set_title('World X-axis Acceleration')
    axes[0, 1].grid(True)
    
    # 世界坐标系加速度数据 - Y轴
    axes[1, 1].plot(time_seconds, df['world_accel_y'], 'g-')
    axes[1, 1].set_title('World Y-axis Acceleration')
    axes[1, 1].grid(True)
    
    # 世界坐标系加速度数据 - Z轴
    axes[2, 1].plot(time_seconds, df['world_accel_z'], 'b-')
    axes[2, 1].set_title('World Z-axis Acceleration')
    axes[2, 1].set_xlabel('Time (seconds)')
    axes[2, 1].grid(True)
    
    plt.tight_layout()
    plt.suptitle(title, fontsize=16)
    plt.subplots_adjust(top=0.92)
    
    return plt

def process_sensor_data(accel_file, quat_file, output_dir=None):
    """处理传感器数据并将加速度转换到世界坐标系"""
    print(f"处理加速度文件: {accel_file}")
    print(f"处理四元数文件: {quat_file}")
    
    # 读取数据
    accel_df = read_sensor_data(accel_file)
    quat_df = read_sensor_data(quat_file)

    cleaned_df = sort_and_clean_data(accel_df)
    
    if accel_df is None or quat_df is None:
        print("数据读取失败")
        return None
    
    # 匹配时间戳
    matched_df = match_data_by_timestamp(cleaned_df, quat_df)
    
    # 转换到世界坐标系
    result_df = convert_to_world_coordinates(matched_df)
    
    # 如果指定了输出目录，保存结果
    if output_dir:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 创建原始文件的备份
        accel_basename = os.path.basename(accel_file)
        backup_filename = os.path.splitext(accel_basename)[0] + "_device_backup" + os.path.splitext(accel_basename)[1]
        backup_file = os.path.join(output_dir, backup_filename)
        
        # 复制原始文件作为备份
        original_df = read_sensor_data(accel_file)
        original_df.to_csv(backup_file, index=False)
        print(f"原始设备坐标系数据已备份至: {backup_file}")
        
        # 创建新的数据框，保持原始文件结构，但替换xyz数据为世界坐标系数据
        new_df = original_df.copy()
        
        # 获取原始文件中的列名 (通常为x, y, z)
        accel_columns = [col for col in new_df.columns if col.lower() in ['x', 'y', 'z']]
        
        if len(accel_columns) >= 3:
            # 用世界坐标系的数据替换原始的xyz数据
            new_df[accel_columns[0]] = result_df['world_accel_x']
            new_df[accel_columns[1]] = result_df['world_accel_y'] 
            new_df[accel_columns[2]] = result_df['world_accel_z']
            
            # 保存修改后的文件，覆盖原始文件
            new_df.to_csv(accel_file, index=False)
            print(f"已将世界坐标系数据写入原始文件: {accel_file}")
        else:
            print(f"警告：无法识别原始文件中的xyz列，无法替换数据")
        
        # 直接在指定目录输出转换结果，供参考
        output_file = os.path.join(output_dir, "world_coordinate_accelerometer.csv")
        result_df.to_csv(output_file, index=False)
        print(f"完整转换结果保存至: {output_file}")
        
        # 原始组合图
        plt_combined = plot_accelerometer_data(result_df)
        plt_combined.savefig(os.path.join(output_dir, "accelerometer_comparison_combined.png"), dpi=300, bbox_inches='tight')
        plt_combined.close()
        
        # 新增三轴分别可视化
        plt_separated = plot_accelerometer_data_separated(result_df)
        plt_separated.savefig(os.path.join(output_dir, "accelerometer_comparison_separated.png"), dpi=300, bbox_inches='tight')
        plt_separated.close()
    
    return result_df

if __name__ == "__main__":
    # 文件路径设置
    data_folder = r"E:\Document\user001\6.30\Session_20250630_221037"
    accel_file = os.path.join(data_folder, "acce_data_22-10-37.922.csv")
    quat_file = os.path.join(data_folder, "rotvec_data_22-10-37.922.csv")
    # 直接使用原始文件夹，不创建额外的子目录
    output_dir = data_folder

    # 处理数据
    result_df = process_sensor_data(accel_file, quat_file, output_dir)
    
    # 显示结果的前几行
    if result_df is not None:
        print("\nTransformation Results (first 5 rows):")
        print(result_df.head(5)[['time', 'accel_x', 'accel_y', 'accel_z', 
                                'world_accel_x', 'world_accel_y', 'world_accel_z']])
        
        # 显示图表（组合视图）
        plt_combined = plot_accelerometer_data(result_df)
        plt_combined.show()
        
        # 显示图表（分离视图）
        plt_separated = plot_accelerometer_data_separated(result_df)
        plt_separated.show()
