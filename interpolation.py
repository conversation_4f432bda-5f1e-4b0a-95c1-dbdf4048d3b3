import pandas as pd
import numpy as np
from scipy import interpolate
from datetime import datetime
import matplotlib.pyplot as plt

def time_to_seconds(time_str):
    """将HH-MM-SS.mmm格式转换为秒数"""
    dt = datetime.strptime(time_str, '%H-%M-%S.%f')
    return dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1e6

def sort_and_clean_data(df):
    """对数据按时间排序并删除重复时间戳"""
    # 转换时间为秒并临时保存
    df['time_seconds'] = df['time'].apply(time_to_seconds)
    
    # 按时间排序
    df_sorted = df.sort_values('time_seconds')
    
    # 移除重复的时间戳，保留第一个出现的数据点
    df_cleaned = df_sorted.drop_duplicates(subset='time_seconds', keep='first')
    
    # 重置索引
    df_cleaned = df_cleaned.reset_index(drop=True)
    
    print(f"原始数据点数: {len(df)}")
    print(f"清理后数据点数: {len(df_cleaned)}")
    print(f"移除了 {len(df) - len(df_cleaned)} 个重复点")
    
    return df_cleaned

def interpolate_acc_data(df, target_freq=500):
    """对加速度数据进行插值"""
    # 获取排序后的时间序列
    time_seconds = df['time_seconds'].values
    
    # 确定插值的时间点（500Hz）
    t_start = time_seconds[0]
    t_end = time_seconds[-1]
    t_interp = np.arange(t_start, t_end, 1/target_freq)
    
    # 对x, y, z三轴分别进行三次样条插值
    interpolated_data = {}
    for axis in ['x', 'y', 'z']:
        spline = interpolate.CubicSpline(time_seconds, df[axis])
        interpolated_data[axis] = spline(t_interp)
    
    # 将插值时间点转换回HH-MM-SS.mmm格式
    time_strings = []
    start_time = datetime.strptime(df['time'].iloc[0], '%H-%M-%S.%f')
    for t in t_interp:
        delta_seconds = t - t_start
        new_time = start_time + pd.Timedelta(seconds=delta_seconds)
        time_strings.append(new_time.strftime('%H-%M-%S.%f')[:-3])
    
    # 创建结果DataFrame
    result_df = pd.DataFrame({
        'time': time_strings,
        'x': interpolated_data['x'],
        'y': interpolated_data['y'],
        'z': interpolated_data['z']
    })
    
    return result_df

def plot_comparison(original_df, interpolated_df, axis='x'):
    """绘制原始数据和插值后数据的对比图"""
    plt.figure(figsize=(15, 6))
    
    # 将原始时间转换为秒
    original_time = np.array([time_to_seconds(t) for t in original_df['time']])
    
    # 绘制原始数据
    plt.plot(original_time, original_df[axis], 'r.', label='Original Data')
    
    # 绘制插值后的数据
    plt.plot(interpolated_df['time'], interpolated_df[axis], 'b-', label='Interpolated Data')
    
    plt.title(f'Acceleration {axis}-axis: Original vs Interpolated')
    plt.xlabel('Time (seconds)')
    plt.ylabel('Acceleration')
    plt.legend()
    plt.grid(True)
    plt.show()

if __name__ == "__main__":
    # 文件路径
    input_file = r"E:\Study\科研\data\实验2\acce_data_21-57-25.536.csv"
    
    # 读取并处理数据
    original_df = pd.read_csv(input_file)
    
    # 数据排序和清理
    cleaned_df = sort_and_clean_data(original_df)
    
    # 进行插值
    interpolated_df = interpolate_acc_data(cleaned_df)
    
    # 保存处理后的数据
    sorted_file = input_file.replace('.csv', '_sorted.csv')
    output_file = input_file.replace('.csv', '_500hz.csv')
    
    cleaned_df.to_csv(sorted_file, index=False)
    interpolated_df.to_csv(output_file, index=False)
    
    # 绘制对比图
    # for axis in ['x', 'y', 'z']:
        # plot_comparison(cleaned_df, interpolated_df, axis)
    

