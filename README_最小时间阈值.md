# 最小时间阈值改进

## 问题描述

在加速部分检测中，有时会受到干扰，把一开始小小的速度变化当作是最终的加速速度最大点，导致：

- **检测结果过短**: 加速阶段持续时间小于0.2秒
- **误检测干扰**: 早期的噪声或小幅波动被误认为是真正的加速峰值
- **不准确的边界**: 检测到的加速阶段不能反映真实的运动模式

## 解决方案

### 1. 最小时间阈值

添加了**0.2秒最小持续时间阈值**，确保检测到的加速阶段具有合理的持续时间：

```python
def _find_acceleration_end_by_velocity(self, velocity, start_idx, min_duration_sec=0.2):
    # 计算最小持续时间对应的样本数
    min_samples = int(min_duration_sec * self.sample_rate)
    
    # 只考虑时间阈值之后的峰值
    for peak_idx in target_peaks:
        if peak_idx < min_samples:
            continue  # 过滤掉时间太早的峰值
```

### 2. 多重过滤条件

除了时间阈值，还添加了多个过滤条件：

#### 条件1: 时间阈值
```python
if peak_idx < min_samples:
    print(f"峰值{peak_idx}被过滤: 时间太早")
    continue
```

#### 条件2: 显著性阈值
```python
if peak_value < 0.3 * max_abs_velocity:
    print(f"峰值{peak_idx}被过滤: 幅度太小")
    continue
```

#### 条件3: 趋势一致性
```python
trend_slope = (velocity[peak_idx] - velocity[trend_start]) / trend_window
if np.sign(trend_slope) != expected_slope_sign:
    print(f"峰值{peak_idx}被过滤: 趋势不一致")
    continue
```

#### 条件4: 峰值突出度
```python
if prominence < min_prominence:
    print(f"峰值{peak_idx}被过滤: 突出度不足")
    continue
```

### 3. 备用检测策略

当主要方法失败时，提供多层备用策略：

```python
def _fallback_velocity_detection(self, velocity, start_idx, min_samples, main_direction, max_abs_velocity):
    # 策略1: 时间阈值后的最大速度点
    # 策略2: 速度阈值方法
    # 策略3: 使用最小时间阈值作为结束点
```

## 技术实现

### 1. 参数配置

```python
# 在detect_motion_by_integration中调用
end_idx = self._find_acceleration_end_by_velocity(
    velocity, start_idx, min_duration_sec=0.2
)

# 同样适用于位移方法
end_idx = self._find_acceleration_end_by_displacement(
    displacement, start_idx, min_duration_sec=0.2
)
```

### 2. 峰值检测改进

```python
# 使用更严格的峰值检测参数
min_peak_distance = max(5, min_samples // 4)
pos_peaks, pos_properties = find_peaks(
    velocity, 
    height=0, 
    distance=min_peak_distance, 
    prominence=0.01  # 添加突出度要求
)
```

### 3. 调试信息

提供详细的调试输出，帮助理解过滤过程：

```
最小加速持续时间: 0.2秒 (20个样本)
检测到正向加速运动，寻找正向速度峰值
  峰值5被过滤: 时间太早 (0.050s < 0.200s)
  峰值8被过滤: 幅度太小 (0.120 < 0.450)
  有效峰值: 索引25, 时间0.250s, 速度1.234
选择最佳峰值: 索引25, 持续时间0.250s, 速度1.234
```

## 使用方法

### 1. 默认使用

新的时间阈值功能默认启用，无需额外配置：

```python
from motion_event_detector import MotionEventDetector

detector = MotionEventDetector(sample_rate=100)
start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='velocity'  # 自动应用0.2s阈值
)
```

### 2. 自定义阈值

如果需要调整时间阈值，可以修改源码中的默认值：

```python
# 在_find_acceleration_end_by_velocity方法中
def _find_acceleration_end_by_velocity(self, velocity, start_idx, min_duration_sec=0.3):
    # 使用0.3秒作为最小阈值
```

### 3. 测试验证

运行测试脚本验证改进效果：

```bash
python test_min_duration_threshold.py
```

## 效果对比

### 改进前
```
检测结果: 0.050s - 0.120s (持续0.070s)
问题: 检测到早期干扰，持续时间过短
```

### 改进后
```
检测结果: 0.300s - 1.200s (持续0.900s)
改进: 过滤了早期干扰，检测到真正的加速阶段
```

## 其他改进建议

除了最小时间阈值，还可以考虑以下改进方法：

### 1. 自适应阈值
根据信号特征动态调整时间阈值：
```python
# 基于信号长度的自适应阈值
adaptive_threshold = max(0.1, total_duration * 0.1)
```

### 2. 信号预处理增强
```python
# 更强的噪声过滤
filtered_data = self._apply_median_filter(data, window_size=5)
filtered_data = self._apply_gaussian_filter(filtered_data, sigma=1.0)
```

### 3. 多尺度分析
```python
# 在不同时间尺度上分析信号
for scale in [0.1, 0.2, 0.5]:
    features = self.extract_features_at_scale(data, scale)
```

### 4. 机器学习方法
```python
# 使用训练好的模型识别真实的加速模式
is_valid_acceleration = self.ml_classifier.predict(features)
```

## 参数调优建议

### 1. 时间阈值选择
- **0.1秒**: 适用于快速运动
- **0.2秒**: 通用设置（推荐）
- **0.3秒**: 适用于缓慢运动

### 2. 显著性阈值
- **0.2**: 宽松设置，保留更多峰值
- **0.3**: 标准设置（推荐）
- **0.5**: 严格设置，只保留最显著的峰值

### 3. 峰值距离
- **5个样本**: 最小距离
- **min_samples // 4**: 自适应距离（推荐）
- **min_samples // 2**: 保守设置

## 故障排除

### 1. 检测结果仍然过短
- 增加时间阈值到0.3秒或更高
- 检查采样率设置是否正确
- 验证输入数据的质量

### 2. 检测结果过长
- 减少时间阈值到0.1秒
- 增加显著性阈值
- 检查是否包含了减速阶段

### 3. 未检测到运动
- 降低显著性阈值
- 检查信号预处理是否过度
- 验证运动幅度是否足够大

## 更新日志

- **v1.2**: 添加最小时间阈值和多重过滤条件
- **v1.1**: 基础的速度和位移检测方法
- **v1.0**: 原始的零点检测方法

现在的检测方法更加鲁棒，能够有效过滤早期干扰，检测到真正的加速阶段！
