# -*- coding:utf-8 -*-
"""
处理单个文件，从每个文件中检测单一运动事件
"""

import os
import glob
import pandas as pd
import numpy as np
from datetime import datetime
from motion_event_detector import MotionEventDetector, time_to_seconds, convert_to_timestamp, process_file

def calculate_sample_rate(df):
    """计算采样率"""
    times = df['time'].values
    time_diffs = []
    
    for i in range(1, min(100, len(times))):
        time_diff = time_to_seconds(times[i]) - time_to_seconds(times[i-1])
        if time_diff > 0:
            time_diffs.append(time_diff)
    
    if len(time_diffs) > 0:
        avg_time_diff = np.mean(time_diffs)
        sample_rate = 1 / avg_time_diff
    else:
        sample_rate = 100  # 默认采样率
    
    return sample_rate

def batch_process(folder_path, file_pattern="*.csv", visualize=True):
    """批量处理文件夹中的文件"""
    # 查找所有匹配的文件
    csv_files = glob.glob(os.path.join(folder_path, "**", file_pattern), recursive=True)
    
    if len(csv_files) == 0:
        print(f"在 {folder_path} 下未找到任何匹配的文件!")
        return []
    
    print(f"找到 {len(csv_files)} 个文件，开始处理...")
    
    # 创建输出文件夹
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(folder_path, f"batch_detection_results_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建输出文本文件
    output_file = os.path.join(output_dir, "detection_results.txt")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("加速度数据事件检测结果\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"处理文件夹: {folder_path}\n")
        f.write("=" * 80 + "\n\n")
    
    # 处理结果
    results = []
    success_count = 0
    
    # 处理每个文件
    for i, file_path in enumerate(csv_files):
        print(f"\n({i+1}/{len(csv_files)}) 处理文件: {os.path.basename(file_path)}")
        
        try:
            # 调用单文件处理函数
            result = process_file(file_path, visualize=visualize)
            
            if result:
                results.append(result)
                success_count += 1
                
                # 将结果写入总结文件
                with open(output_file, 'a', encoding='utf-8') as f:
                    f.write(f"文件: {os.path.basename(file_path)}\n")
                    f.write(f"起始时间: {result['start_time']}\n")
                    f.write(f"结束时间: {result['end_time']}\n")
                    f.write(f"持续时间: {result['duration']:.3f} 秒\n")
                    f.write(f"置信度: {result['confidence']:.2f}\n")
                    f.write("-" * 50 + "\n\n")
        
        except Exception as e:
            print(f"处理文件出错: {str(e)}")
            # 记录错误
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write(f"文件: {os.path.basename(file_path)} - 处理失败\n")
                f.write(f"错误: {str(e)}\n")
                f.write("-" * 50 + "\n\n")
    
    # 写入统计信息
    with open(output_file, 'a', encoding='utf-8') as f:
        f.write("=" * 50 + "\n")
        f.write(f"处理统计: 共 {len(csv_files)} 个文件，成功 {success_count} 个，失败 {len(csv_files) - success_count} 个\n")
    
    print(f"\n处理完成，结果保存至: {output_file}")
    print(f"共 {len(csv_files)} 个文件，成功 {success_count} 个，失败 {len(csv_files) - success_count} 个")
    
    return results

def main():
    """主函数"""
    folder_path = input("请输入要处理的文件夹路径: ").strip()
    
    if not os.path.exists(folder_path):
        print(f"路径 {folder_path} 不存在!")
        return
    
    pattern_choice = input("请选择文件匹配模式 [1:所有CSV文件 2:仅segment开头的文件]: ")
    
    if pattern_choice == "2":
        file_pattern = "segment*_acce_*.csv"  # 仅匹配segment开头的加速度文件
    else:
        file_pattern = "*.csv"  # 默认匹配所有CSV文件
    
    vis_choice = input("是否生成可视化结果 [Y/N, 默认Y]: ").strip().lower()
    visualize = vis_choice != 'n'
    
    batch_process(folder_path, file_pattern, visualize)

if __name__ == "__main__":
    main()
