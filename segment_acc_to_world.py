# -*- coding:utf-8 -*-
"""
作者：${何志想}
日期：2025年05月14日
功能：使用加速度计、重力计和磁场数据将加速度转换到世界坐标系
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import re
import glob

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 


def load_segment_files(segment_folder, segment_number=None):
    """
    加载特定片段的所有传感器数据文件
    
    Args:
        segment_folder: 存放片段数据的文件夹路径
        segment_number: 指定片段编号，如果为None则加载所有片段
        
    Returns:
        字典，包含每个片段的数据，格式为 {片段编号: {'acce': df_acc, 'grav': df_grav, 'mag': df_mag}}
    """
    # 查找所有片段文件
    all_files = glob.glob(os.path.join(segment_folder, "segment*_*.csv"))
    
    segment_data = {}
      # 正则表达式匹配文件名 - 格式为 segment数字_传感器类型_时间戳.csv
    pattern = r"segment(\d+)_(acce|grav|mag|gyro)_(\d+-\d+-\d+\.\d+)\.csv"
    
    for file_path in all_files:
        file_name = os.path.basename(file_path)
        match = re.match(pattern, file_name)
        
        if match:
            seg_num = int(match.group(1))
            sensor_type = match.group(2)
            timestamp = match.group(3)
            
            # 如果指定了片段编号，只处理该片段
            if segment_number is not None and seg_num != segment_number:
                continue
            
            # 读取数据
            try:
                df = pd.read_csv(file_path)
                # 保存文件路径到DataFrame的元数据中，方便后续引用
                df.name = file_path
                
                # 初始化字典
                if seg_num not in segment_data:
                    segment_data[seg_num] = {}
                
                # 保存数据
                if sensor_type == 'acce':
                    segment_data[seg_num]['acce'] = df
                elif sensor_type == 'grav':
                    segment_data[seg_num]['grav'] = df
                elif sensor_type == 'mag':
                    segment_data[seg_num]['mag'] = df
                elif sensor_type == 'gyro':
                    segment_data[seg_num]['gyro'] = df
                
                print(f"加载文件: {file_name}, 形状: {df.shape}")
            except Exception as e:
                print(f"加载文件 {file_name} 错误: {e}")
    
    return segment_data

def normalize_vector(v):
    """标准化向量"""
    norm = np.linalg.norm(v)
    if norm > 0:
        return v / norm
    return v

def build_world_coordinate_system(gravity, magnetic_field):
    """
    使用重力和磁场数据构建世界坐标系
    
    Args:
        gravity: 重力向量 [gx, gy, gz]
        magnetic_field: 磁场向量 [mx, my, mz]
        
    Returns:
        3x3矩阵，每行是世界坐标系的一个单位向量
        [东向量(X轴), 北向量(Y轴), 上向量(Z轴)]
    """
    # 标准化重力向量 (指向上方，与重力方向相反)
    up = normalize_vector(gravity)  # 上方向，与重力方向相反
    
    # 计算东向量：磁场向量与上向量的叉积
    east = normalize_vector(np.cross(magnetic_field, up))
    
    # 计算北向量：上向量与东向量的叉积
    north = normalize_vector(np.cross(up, east))
    
    # 返回世界坐标系，每行是一个单位向量
    return np.array([east, north, up])\

def transform_acceleration_to_world(acc_device, world_coord_system):
    """
    将设备坐标系下的加速度转换到世界坐标系
    
    Args:
        acc_device: 设备坐标系下的加速度 [ax, ay, az]
        world_coord_system: 世界坐标系，3x3矩阵，每行是世界坐标系的一个单位向量
        
    Returns:
        世界坐标系下的加速度 [ax_world, ay_world, az_world]
    """
    # 投影到世界坐标系
    acc_world = np.dot(acc_device, world_coord_system.T)
    return acc_world

def smooth_data(data, window_size=10):
    """
    使用滑动窗口平均对数据进行平滑处理
    
    Args:
        data: 需要平滑的数据数组
        window_size: 滑动窗口大小
        
    Returns:
        平滑后的数据数组
    """
    if len(data) <= window_size:
        return data
    
    smoothed = np.zeros_like(data)
    for i in range(len(data)):
        start = max(0, i - window_size//2)
        end = min(len(data), i + window_size//2 + 1)
        smoothed[i] = np.mean(data[start:end], axis=0)
    
    return smoothed

def process_segment_data(segment_data, segment_num, output_folder, window_size=10):
    """
    处理一个片段的数据，转换加速度到世界坐标系
    
    Args:
        segment_data: 片段数据字典
        segment_num: 片段编号
        output_folder: 输出文件夹
        window_size: 平滑窗口大小
        
    Returns:
        转换后的加速度数据DataFrame
    """
    segment_info = segment_data[segment_num]
    
    # 检查必要的数据是否存在
    if not all(key in segment_info for key in ['acce', 'grav', 'mag']):
        print(f"片段 {segment_num} 缺少必要的数据 (加速度/重力/磁场)")
        return None
    
    acc_df = segment_info['acce']
    grav_df = segment_info['grav']
    mag_df = segment_info['mag']
      # 预处理时间数据 - 将时间字符串转换为秒数
    print("预处理时间数据...")
    print(f"加速度数据列: {list(acc_df.columns)}")
    print(f"重力数据列: {list(grav_df.columns)}")
    print(f"磁场数据列: {list(mag_df.columns)}")
    
    # 创建深拷贝以避免修改原始数据
    acc_df = acc_df.copy()
    grav_df = grav_df.copy()
    mag_df = mag_df.copy()
    
    # 检查是否存在sensor_timestamp列，如果有，将其转换为数值类型
    if 'sensor_timestamp' in acc_df.columns:
        print("检测到sensor_timestamp列，转换为数值...")
        try:
            acc_df['sensor_timestamp'] = pd.to_numeric(acc_df['sensor_timestamp'])
            if 'sensor_timestamp' in grav_df.columns:
                grav_df['sensor_timestamp'] = pd.to_numeric(grav_df['sensor_timestamp'])
            if 'sensor_timestamp' in mag_df.columns:
                mag_df['sensor_timestamp'] = pd.to_numeric(mag_df['sensor_timestamp'])
        except Exception as e:
            print(f"转换sensor_timestamp时出错: {e}")
    
    # 预处理时间字符串
    acc_df = preprocess_time_data(acc_df)
    grav_df = preprocess_time_data(grav_df)
    mag_df = preprocess_time_data(mag_df)
    
    # 确保所有数据的时间戳对齐
    # 使用加速度数据的时间戳作为参考
    timestamps = acc_df['time'].values
    timestamps_seconds = acc_df['time_seconds'].values
    
    # 创建结果DataFrame
    result_df = pd.DataFrame()
    result_df['time'] = timestamps
    
    # 转换过程中累积的世界坐标系加速度数据
    acc_world_all = []    # 遍历时间点
    for i, time in enumerate(timestamps):
        try:
            # 获取当前时刻的加速度数据
            acc_current = np.array([acc_df.iloc[i]['x'], acc_df.iloc[i]['y'], acc_df.iloc[i]['z']])
            time_seconds = timestamps_seconds[i]
            
            # 如果有sensor_timestamp列，尝试用它进行匹配（更精确）
            if 'sensor_timestamp' in acc_df.columns and 'sensor_timestamp' in grav_df.columns:
                acc_sensor_time = acc_df.iloc[i]['sensor_timestamp']
                grav_diffs = np.abs(grav_df['sensor_timestamp'].values - acc_sensor_time)
            else:
                # 否则使用预处理后的时间秒数进行比较
                grav_diffs = np.abs(grav_df['time_seconds'].values - time_seconds)
                
            grav_idx = np.argmin(grav_diffs)
            grav_current = np.array([grav_df.iloc[grav_idx]['x'], grav_df.iloc[grav_idx]['y'], grav_df.iloc[grav_idx]['z']])
            
            # 同样对磁场数据使用最合适的时间戳匹配方法
            if 'sensor_timestamp' in acc_df.columns and 'sensor_timestamp' in mag_df.columns:
                acc_sensor_time = acc_df.iloc[i]['sensor_timestamp']
                mag_diffs = np.abs(mag_df['sensor_timestamp'].values - acc_sensor_time)
            else:
                mag_diffs = np.abs(mag_df['time_seconds'].values - time_seconds)
                
            mag_idx = np.argmin(mag_diffs)
            mag_current = np.array([mag_df.iloc[mag_idx]['x'], mag_df.iloc[mag_idx]['y'], mag_df.iloc[mag_idx]['z']])
            
            # 输出匹配信息以便调试（仅在第一次和最后一次匹配时输出）
            if i == 0 or i == len(timestamps)-1:
                if 'sensor_timestamp' in acc_df.columns:
                    print(f"{'首个' if i==0 else '最后'}匹配点 - 加速度sensor_timestamp: {acc_df.iloc[i]['sensor_timestamp']}")
                    if 'sensor_timestamp' in grav_df.columns:
                        print(f"  对应重力sensor_timestamp: {grav_df.iloc[grav_idx]['sensor_timestamp']}")
                    if 'sensor_timestamp' in mag_df.columns:
                        print(f"  对应磁场sensor_timestamp: {mag_df.iloc[mag_idx]['sensor_timestamp']}")
              # 构建世界坐标系
            world_coords = build_world_coordinate_system(grav_current, mag_current)
            
            # 转换加速度
            acc_world = transform_acceleration_to_world(acc_current, world_coords)
            acc_world_all.append(acc_world)
            
        except Exception as e:
            print(f"处理时间点 {time} 错误: {e}")
            import traceback
            traceback.print_exc()
            
            # 打印当前处理的数据点的详细信息，帮助调试
            if i < len(acc_df):
                print(f"当前处理的加速度数据点:")
                for col in acc_df.columns:
                    print(f"  {col}: {acc_df.iloc[i][col]}")
                    
            # 如果出错，使用前一个有效的转换结果，如果没有则用零向量
            if acc_world_all:
                acc_world_all.append(acc_world_all[-1])
                print(f"使用前一个有效的转换结果继续")
            else:
                acc_world_all.append(np.zeros(3))
                print(f"使用零向量继续")
    
    # 将所有转换后的加速度数据转换为数组
    acc_world_all = np.array(acc_world_all)
    
    # 应用滑动窗口平滑
    if len(acc_world_all) > window_size:
        acc_world_all = smooth_data(acc_world_all, window_size)
      # 添加到结果DataFrame
    # 确保东向(east)对应x轴，北向(north)对应y轴，上向(up)对应z轴
    result_df['x'] = acc_world_all[:, 0]   # 东方向(east)加速度
    result_df['y'] = acc_world_all[:, 1]   # 北方向(north)加速度
    result_df['z'] = acc_world_all[:, 2]   # 上方向(up)加速度
    
    # 添加原始加速度列
    result_df['x_raw'] = acc_df['x'].values
    result_df['y_raw'] = acc_df['y'].values
    result_df['z_raw'] = acc_df['z'].values    # 生成输出文件名    # 从原始加速度文件名中提取时间戳部分，使用与原始加速度文件相同的时间戳
    acc_filename = os.path.basename(acc_df.name) if hasattr(acc_df, 'name') else ""
    print(f"提取时间戳的加速度文件名: {acc_filename}")
    match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
    
    if match:
        timestamp = match.group(1)
        print(f"成功提取时间戳: {timestamp}")
    else:
        # 尝试使用数据中第一行的时间作为时间戳，而不是当前时间
        if 'time' in acc_df.columns and len(acc_df) > 0:
            first_time = acc_df['time'].iloc[0]
            timestamp = first_time.replace(':', '-') if isinstance(first_time, str) else datetime.now().strftime('%H-%M-%S.%f')[:-3]
            print(f"无法从文件名提取时间戳，使用数据第一行时间: {timestamp}")
        else:
            timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
            print(f"无法从文件名提取时间戳，且数据中无时间列，使用当前时间: {timestamp}")
    
    output_file = os.path.join(output_folder, f'segment{segment_num}_world_acce_{timestamp}.csv')
    result_df.to_csv(output_file, index=False)
    print(f"已保存世界坐标系加速度数据: {output_file}")
    
    return result_df

def visualize_acceleration(segment_data, segment_num, world_acc_df, output_folder):
    """
    可视化原始加速度和世界坐标系加速度
    
    Args:
        segment_data: 片段数据字典
        segment_num: 片段编号
        world_acc_df: 世界坐标系加速度数据
        output_folder: 输出文件夹
    """
    if segment_num not in segment_data or 'acce' not in segment_data[segment_num]:
        print(f"片段 {segment_num} 没有加速度数据，无法可视化")
        return
    
    acc_df = segment_data[segment_num]['acce']
    
    # 创建时间轴
    time_seconds = np.arange(len(acc_df)) / 100  # 假设采样率为100Hz
    
    # 绘制原始加速度和世界坐标系加速度
    plt.figure(figsize=(12, 10))
    
    # 原始加速度
    plt.subplot(2, 1, 1)
    plt.plot(time_seconds, acc_df['x'], 'r-', label='X轴')
    plt.plot(time_seconds, acc_df['y'], 'g-', label='Y轴')
    plt.plot(time_seconds, acc_df['z'], 'b-', label='Z轴')
    plt.title(f'片段 {segment_num} - 原始加速度 (设备坐标系)')
    plt.xlabel('时间 (秒)')
    plt.ylabel('加速度 (m/s²)')
    plt.grid(True)
    plt.legend()    # 世界坐标系加速度
    plt.subplot(2, 1, 2)
    plt.plot(time_seconds, world_acc_df['x'], 'r-', label='东向(East/X轴)')
    plt.plot(time_seconds, world_acc_df['y'], 'g-', label='北向(North/Y轴)')
    plt.plot(time_seconds, world_acc_df['z'], 'b-', label='上向(Up/Z轴)')
    plt.title(f'片段 {segment_num} - 世界坐标系加速度')
    plt.xlabel('时间 (秒)')
    plt.ylabel('加速度 (m/s²)')
    plt.grid(True)
    plt.legend()
    
    plt.tight_layout()    # 保存图表，使用与原始加速度数据相同的时间戳
    acc_filename = os.path.basename(segment_data[segment_num]['acce'].name) if hasattr(segment_data[segment_num]['acce'], 'name') else ""
    print(f"可视化：提取时间戳的加速度文件名: {acc_filename}")
    match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
    
    if match:
        timestamp = match.group(1)
        print(f"可视化：成功提取时间戳: {timestamp}")
    else:
        # 尝试使用加速度数据中第一行的时间作为时间戳
        acc_df = segment_data[segment_num]['acce']
        if 'time' in acc_df.columns and len(acc_df) > 0:
            first_time = acc_df['time'].iloc[0]
            timestamp = first_time.replace(':', '-') if isinstance(first_time, str) else datetime.now().strftime('%H-%M-%S.%f')[:-3]
            print(f"可视化：无法从文件名提取时间戳，使用数据第一行时间: {timestamp}")
        else:
            timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
            print(f"可视化：无法从文件名提取时间戳，且数据中无时间列，使用当前时间: {timestamp}")
    
    output_file = os.path.join(output_folder, f'segment{segment_num}_acceleration_comparison_{timestamp}.png')
    plt.savefig(output_file, dpi=300)
    plt.close()
    print(f"已保存可视化图表: {output_file}")

def calculate_velocity_and_displacement(world_acc_df, dt=0.01):
    """
    计算世界坐标系下的速度和位移
    
    Args:
        world_acc_df: 世界坐标系加速度数据 (x=东向east, y=北向north, z=上向up)
        dt: 时间步长，单位秒，默认为0.01秒 (100Hz)
        
    Returns:
        添加了速度和位移列的DataFrame
    """
    # 创建结果DataFrame
    result_df = world_acc_df.copy()
    
    # 初始化速度和位移列
    for direction in ['x', 'y', 'z']:
        result_df[f'vel_{direction}'] = 0.0
        result_df[f'disp_{direction}'] = 0.0
    
    # 计算每个方向上的速度和位移
    for direction in ['x', 'y', 'z']:
        # 获取加速度数据
        acc = result_df[direction].values
        
        # 初始化速度和位移
        vel = np.zeros_like(acc)
        disp = np.zeros_like(acc)
        
        # 积分计算速度和位移
        for i in range(1, len(acc)):
            # 速度 = 上一时刻速度 + 加速度 * 时间步长
            vel[i] = vel[i-1] + acc[i] * dt
            
            # 位移 = 上一时刻位移 + 速度 * 时间步长
            disp[i] = disp[i-1] + vel[i] * dt
        
        # 存储结果
        result_df[f'vel_{direction}'] = vel
        result_df[f'disp_{direction}'] = disp
    
    return result_df

def process_all_segments(data_folder, output_folder=None, calculate_kinematics=False):
    """
    处理文件夹中所有片段数据
    
    Args:
        data_folder: 存放片段数据的文件夹路径
        output_folder: 输出文件夹路径，如果为None则使用数据文件夹
        calculate_kinematics: 是否计算速度和位移
    """
    # 使用输入文件夹作为输出文件夹
    # output_folder = data_folder
    
    if output_folder is None:
        output_folder = os.path.join(data_folder, 'world_coordinates')
    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)
    
    # 加载所有片段数据
    segment_data = load_segment_files(data_folder)
    
    if not segment_data:
        print(f"在文件夹 {data_folder} 中未找到任何片段数据")
        return
    
    print(f"发现 {len(segment_data)} 个片段，开始处理...")
    
    # 处理每个片段
    for segment_num in sorted(segment_data.keys()):
        print(f"\n处理片段 {segment_num}...")
        
        # 检查是否有必要的数据
        if not all(key in segment_data[segment_num] for key in ['acce', 'grav', 'mag']):
            print(f"片段 {segment_num} 缺少必要的数据 (加速度/重力/磁场)，跳过")
            continue
        
        # 处理数据
        world_acc_df = process_segment_data(segment_data, segment_num, output_folder)
        
        if world_acc_df is not None:
            # 可视化
            visualize_acceleration(segment_data, segment_num, world_acc_df, output_folder)
            
            # 如果需要，计算速度和位移
            if calculate_kinematics:
                print(f"计算片段 {segment_num} 的速度和位移...")
                
                # 推断采样率
                if len(world_acc_df) > 1:
                    try:
                        # 尝试从时间列计算采样率
                        time_format = '%H-%M-%S.%f'
                        t1 = datetime.strptime(world_acc_df['time'].iloc[0], time_format)
                        t2 = datetime.strptime(world_acc_df['time'].iloc[-1], time_format)
                        total_time = (t2 - t1).total_seconds()
                        if total_time > 0:
                            sampling_rate = (len(world_acc_df) - 1) / total_time
                            dt = 1.0 / sampling_rate
                        else:
                            dt = 0.01  # 默认100Hz
                    except:
                        dt = 0.01  # 默认100Hz
                else:
                    dt = 0.01  # 默认100Hz
                
                print(f"使用采样时间步长: {dt:.6f}秒 (约 {1/dt:.1f} Hz)")
                  # 计算速度和位移
                kinematics_df = calculate_velocity_and_displacement(world_acc_df, dt)
                
                # 提取时间戳，使用与原始加速度数据相同的时间戳
                acc_filename = os.path.basename(segment_data[segment_num]['acce'].name)
                print(f"运动学计算：提取时间戳的加速度文件名: {acc_filename}")
                match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
                
                if match:
                    timestamp = match.group(1)
                    print(f"运动学计算：成功提取时间戳: {timestamp}")
                else:
                    # 尝试使用加速度数据中第一行的时间作为时间戳
                    acc_df = segment_data[segment_num]['acce']
                    if 'time' in acc_df.columns and len(acc_df) > 0:
                        first_time = acc_df['time'].iloc[0]
                        timestamp = first_time.replace(':', '-') if isinstance(first_time, str) else datetime.now().strftime('%H-%M-%S.%f')[:-3]
                        print(f"运动学计算：无法从文件名提取时间戳，使用数据第一行时间: {timestamp}")
                    else:
                        timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
                        print(f"运动学计算：无法从文件名提取时间戳，且数据中无时间列，使用当前时间: {timestamp}")
                
                # 保存运动学数据（速度和位移）
                kinematics_file = os.path.join(output_folder, f'segment{segment_num}_kinematics_{timestamp}.csv')
                kinematics_df.to_csv(kinematics_file, index=False)
                print(f"已保存运动学数据: {kinematics_file}")
            
            print(f"片段 {segment_num} 处理完成")
    
    print("\n所有片段处理完成!")

def time_to_seconds(time_str):
    """
    将HH-MM-SS.mmm格式转换为秒数
    
    Args:
        time_str: 时间字符串，格式为HH-MM-SS.mmm
        
    Returns:
        浮点数表示的秒数
    """
    # 首先检查输入是否为非字符串类型（比如NaN或None）
    if not isinstance(time_str, str):
        print(f"警告: 非字符串时间数据: {time_str}，类型: {type(time_str)}")
        return 0
    
    # 然后尝试解析时间字符串
    try:
        # 去除可能存在的空格
        time_str = time_str.strip()
        
        # 如果是带有毫秒的时间格式
        if '.' in time_str:
            dt = datetime.strptime(time_str, '%H-%M-%S.%f')
        else:
            # 如果没有毫秒部分
            dt = datetime.strptime(time_str, '%H-%M-%S')
            
        return dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1e6
    except ValueError as e:
        print(f"无法解析时间字符串: {time_str}，错误: {e}")
        return 0

def preprocess_time_data(df):
    """预处理时间数据，将时间字符串转换为浮点数秒"""
    if 'time' in df.columns:
        # 记录列结构用于调试
        print(f"数据列: {list(df.columns)}")
        print(f"数据示例:\n{df.head(2)}")
        
        df['time_seconds'] = df['time'].apply(time_to_seconds)
    return df

if __name__ == "__main__":
    print("=" * 50)
    print("加速度世界坐标系转换工具")
    print("=" * 50)
      # 输入文件夹路径
    default_path = r"E:\Document\user001\7.25\Session_20250725_225125"
    folder_path = input(f"请输入数据文件夹路径 [默认: {default_path}]: ")
    folder_path = folder_path.strip() if folder_path.strip() else default_path
    
    # 确认文件夹存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹不存在: {folder_path}")
        exit(1)
      # 输出文件夹设置为 output_world_coordinates
    output_folder = os.path.join(folder_path, 'output_world_coordinates')
    
    # 是否计算速度和位移
    calc_kinematics = input("是否计算速度和位移? (y/n) [默认: n]: ").lower().strip()
    calc_kinematics = calc_kinematics == 'y'
    
    # 处理所有片段
    process_all_segments(folder_path, output_folder, calc_kinematics)
