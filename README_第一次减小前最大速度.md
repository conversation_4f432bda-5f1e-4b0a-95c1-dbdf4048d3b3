# 第一次减小前最大速度检测方法

## 问题描述

在原来的velocity检测方法中，存在以下问题：

- **全局最大值问题**: 寻找整个过程中的全局最大速度点
- **后续峰值干扰**: 可能检测到后续更大的加速峰值，而不是初始加速阶段
- **复杂运动误判**: 对于包含多次加速的复杂运动，可能错过真正的初始加速阶段

### 具体场景
```
运动过程: 初始加速 -> 短暂减速 -> 更大的二次加速
问题: 原方法可能选择二次加速的峰值，而不是初始加速的峰值
期望: 检测到初始加速阶段的结束点
```

## 解决方案

### 核心思路
**从零时刻开始计算，找到速度第一次开始减小之前的最大速度点**：

1. **检测速度减小点**: 找到速度第一次开始减小的时刻
2. **限定搜索范围**: 只在减小点之前寻找最大速度
3. **捕捉初始加速**: 确保检测到的是真正的初始加速阶段

### 算法流程

```python
def _find_acceleration_end_by_velocity_magnitude(self, velocity_magnitude, start_idx, min_duration_sec=0.2):
    # 1. 找到速度第一次开始减小的点
    first_decrease_point = self._find_first_velocity_decrease(velocity_magnitude, min_samples)
    
    # 2. 在减小点之前寻找最大速度
    if first_decrease_point is not None:
        search_range = velocity_magnitude[:first_decrease_point + 1]
    else:
        search_range = velocity_magnitude
    
    # 3. 在限定范围内找最大值
    max_velocity_point = find_max_in_range(search_range, min_samples)
    
    return max_velocity_point
```

## 技术实现

### 1. 速度减小点检测

```python
def _find_first_velocity_decrease(self, velocity_magnitude, min_samples):
    # 使用滑动窗口检测速度减小
    window_size = max(3, self.sample_rate // 50)  # 约0.02秒的窗口
    
    for i in range(min_samples, len(velocity_magnitude) - window_size):
        current_velocity = velocity_magnitude[i]
        future_velocity = np.mean(velocity_magnitude[i+1:i+1+window_size])
        
        # 检查是否开始减小（减小超过5%）
        if future_velocity < current_velocity * 0.95:
            if self._verify_velocity_decrease(velocity_magnitude, i, window_size):
                return i
    
    return None
```

### 2. 减小趋势验证

```python
def _verify_velocity_decrease(self, velocity_magnitude, decrease_point, window_size):
    # 检查减小点之后的速度是否持续较低
    verify_window = min(window_size * 3, len(velocity_magnitude) - decrease_point)
    
    pre_velocity = velocity_magnitude[decrease_point]
    post_velocities = velocity_magnitude[decrease_point+1:decrease_point+1+verify_window]
    
    # 统计后续速度低于减小点速度的比例
    lower_count = np.sum(post_velocities < pre_velocity * 0.98)
    consistency_ratio = lower_count / len(post_velocities)
    
    # 60%以上的时间保持较低速度才认为是稳定减小
    return consistency_ratio > 0.6
```

### 3. 限定范围内的最大值搜索

```python
# 在减小点之前寻找最大速度
if first_decrease_point is not None:
    search_range = velocity_magnitude[:first_decrease_point + 1]
else:
    search_range = velocity_magnitude

# 在时间阈值之后寻找最大值
valid_search_range = search_range[min_samples:]
max_vel_idx = min_samples + np.argmax(valid_search_range)
```

## 主要改进

### 1. 精确的初始加速检测
- **避免后续干扰**: 不会被后续更大的加速峰值误导
- **捕捉真实阶段**: 准确检测初始加速阶段的结束
- **时序准确性**: 保持运动事件的时间顺序

### 2. 智能减小点检测
- **滑动窗口**: 使用滑动窗口平滑检测，减少噪声影响
- **阈值判断**: 5%的减小阈值，避免微小波动的误判
- **稳定性验证**: 确保减小趋势是稳定的，不是暂时波动

### 3. 鲁棒性保证
- **备用策略**: 当没有检测到减小点时，使用全范围搜索
- **时间阈值**: 保持0.2秒最小持续时间要求
- **显著性验证**: 确保检测到的最大值足够显著

## 使用方法

### 1. 默认使用

新的第一次减小前检测已集成到velocity方法中：

```python
from motion_event_detector import MotionEventDetector

detector = MotionEventDetector(sample_rate=100)
start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='velocity'  # 自动使用第一次减小前检测
)
```

### 2. 测试验证

运行测试脚本验证改进效果：

```bash
python test_first_decrease_velocity.py
```

## 效果对比

### 改进前（全局最大值方法）
```
运动场景: 初始加速(0.2-1.0s) -> 减速(1.0-1.5s) -> 更大加速(1.5-2.5s)
检测结果: 可能选择2.5s的全局最大速度点
问题: 检测到的不是初始加速阶段
```

### 改进后（第一次减小前方法）
```
运动场景: 初始加速(0.2-1.0s) -> 减速(1.0-1.5s) -> 更大加速(1.5-2.5s)
减小点检测: 在1.0s检测到速度开始减小
检测结果: 在1.0s之前找到初始加速的最大速度点
改进: 准确检测到初始加速阶段结束
```

## 适用场景

### 1. 推荐使用
- **复杂运动模式**: 包含多次加速-减速的运动
- **初始加速重要**: 需要准确捕捉初始加速阶段的应用
- **时序敏感**: 对运动事件时间顺序有严格要求的场景

### 2. 效果显著的情况
- **多峰速度曲线**: 速度曲线包含多个峰值
- **递增加速模式**: 后续加速比初始加速更强
- **间歇性运动**: 包含停顿或减速的复杂运动

## 技术特性

### 1. 检测精度
- **时间分辨率**: 约0.02秒的检测精度
- **减小阈值**: 5%的速度减小判断阈值
- **稳定性要求**: 60%一致性的减小趋势验证

### 2. 参数配置
```python
window_size = max(3, self.sample_rate // 50)  # 检测窗口大小
decrease_threshold = 0.95  # 减小阈值（95%）
stability_threshold = 0.6  # 稳定性阈值（60%）
verification_window = window_size * 3  # 验证窗口大小
```

### 3. 调试信息
```python
print(f"寻找从零时刻开始第一次减小之前的最大速度点")
print(f"找到速度第一次开始减小的点: 索引{first_decrease_point}")
print(f"找到第一次减小前的最大速度点: 索引{max_vel_idx}")
```

## 可视化功能

### 1. 对比显示
- **检测结果对比**: 同时显示新旧方法的检测结果
- **减小点标记**: 清晰标记速度开始减小的时刻
- **最大值标记**: 分别标记全局最大值和减小前最大值

### 2. 分析图表
- **速度变化率**: 显示速度的变化趋势
- **检测过程**: 展示减小点检测的详细过程
- **准确性分析**: 对比不同方法的检测精度

## 故障排除

### 1. 未检测到减小点
- 检查运动是否包含速度减小阶段
- 调整减小阈值（decrease_threshold）
- 增加检测窗口大小

### 2. 减小点检测错误
- 增加稳定性验证的一致性要求
- 调整验证窗口大小
- 检查输入数据的噪声水平

### 3. 检测结果不理想
- 对比全局最大值方法的结果
- 检查最小时间阈值设置
- 验证运动模式是否符合预期

## 更新日志

- **v1.5**: 添加第一次减小前最大速度检测方法
- **v1.4**: 三轴合速度矢量检测方法
- **v1.3**: 基于速度转折点的位移检测
- **v1.2**: 添加最小时间阈值功能

现在的velocity方法能够智能识别速度减小点，准确检测初始加速阶段的结束！
