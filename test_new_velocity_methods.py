# -*- coding:utf-8 -*-
"""
测试新的velocity检测方法
验证三轴独立检测和主轴+副轴合成检测的功能
"""

import numpy as np
import matplotlib.pyplot as plt
from motion_event_detector import MotionEventDetector

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def create_multi_axis_motion_data(sample_rate=100):
    """
    创建多轴运动数据，每个轴有不同的峰值时间
    模拟真实的复杂三维运动情况
    """
    duration = 3.0  # 3秒数据
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # X轴：主要运动，峰值在1.0s
    acc_x = np.zeros_like(t)
    mask_x = (t >= 0.2) & (t < 1.2)
    t_x = t[mask_x] - 0.2
    acc_x[mask_x] = 2.5 * np.sin(np.pi * t_x / 1.0)
    
    # Y轴：较小运动，峰值在0.8s（更早）
    acc_y = np.zeros_like(t)
    mask_y = (t >= 0.3) & (t < 1.0)
    t_y = t[mask_y] - 0.3
    acc_y[mask_y] = 1.2 * np.sin(np.pi * t_y / 0.7)
    
    # Z轴：中等运动，峰值在1.3s（更晚）
    acc_z = np.zeros_like(t)
    mask_z = (t >= 0.4) & (t < 1.5)
    t_z = t[mask_z] - 0.4
    acc_z[mask_z] = 1.8 * np.sin(np.pi * t_z / 1.1)
    
    # 添加噪声
    noise_level = 0.05
    acc_x += noise_level * np.random.normal(0, 1, len(t))
    acc_y += noise_level * np.random.normal(0, 1, len(t))
    acc_z += noise_level * np.random.normal(0, 1, len(t))
    
    # 组合三轴数据
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    return acc_data, t

def test_all_velocity_methods():
    """
    测试所有velocity检测方法的对比
    """
    print("=== 测试所有velocity检测方法 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_multi_axis_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    methods = [
        ('velocity', '三轴合速度矢量'),
        ('velocity_individual', '三轴独立检测'),
        ('velocity_main_secondary', '主轴+副轴合成')
    ]
    
    results = {}
    
    for method_name, method_desc in methods:
        print(f"\n{'-'*50}")
        print(f"测试方法: {method_desc} ({method_name})")
        print(f"{'-'*50}")
        
        try:
            start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
                acc_data, only_acceleration_phase=True, use_integration=True, detection_method=method_name
            )
            
            duration = (end_idx - start_idx) / sample_rate
            results[method_name] = {
                'start_time': start_idx / sample_rate,
                'end_time': end_idx / sample_rate,
                'duration': duration,
                'confidence': confidence,
                'success': True
            }
            
            print(f"检测结果:")
            print(f"  起始时间: {start_idx/sample_rate:.3f}s")
            print(f"  结束时间: {end_idx/sample_rate:.3f}s")
            print(f"  持续时间: {duration:.3f}s")
            print(f"  置信度: {confidence:.3f}")
            
        except Exception as e:
            print(f"检测失败: {e}")
            results[method_name] = {
                'success': False,
                'error': str(e)
            }
    
    # 结果对比
    print(f"\n{'='*60}")
    print("检测结果对比:")
    print(f"{'='*60}")
    print(f"{'方法':<20} {'起始时间':<10} {'结束时间':<10} {'持续时间':<10} {'置信度':<8}")
    print(f"{'-'*60}")
    
    for method_name, method_desc in methods:
        if results[method_name]['success']:
            result = results[method_name]
            print(f"{method_desc:<20} {result['start_time']:<10.3f} {result['end_time']:<10.3f} {result['duration']:<10.3f} {result['confidence']:<8.3f}")
        else:
            print(f"{method_desc:<20} {'失败':<10} {'失败':<10} {'失败':<10} {'失败':<8}")
    
    return acc_data, time_axis, results

def analyze_individual_axes():
    """
    分析三轴独立检测的详细过程
    """
    print("\n=== 分析三轴独立检测过程 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_multi_axis_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    
    # 计算三轴速度
    window = 5
    smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
    smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
    smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
    
    combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
    threshold = 0.1 * np.max(combined_acc)
    start_candidates = np.where(combined_acc > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    dt = 1.0 / sample_rate
    velocity_x = np.cumsum(smoothed_x[start_idx:]) * dt
    velocity_y = np.cumsum(smoothed_y[start_idx:]) * dt
    velocity_z = np.cumsum(smoothed_z[start_idx:]) * dt
    
    # 分析各轴的峰值时间
    min_samples = int(0.1 * sample_rate)
    axis_names = ['X', 'Y', 'Z']
    axis_velocities = [velocity_x, velocity_y, velocity_z]
    
    print(f"各轴速度分析:")
    for i, (axis_name, velocity_axis) in enumerate(zip(axis_names, axis_velocities)):
        if len(velocity_axis) > min_samples:
            valid_range = velocity_axis[min_samples:]
            if len(valid_range) > 0:
                max_idx_in_valid = np.argmax(np.abs(valid_range))
                max_idx = min_samples + max_idx_in_valid
                max_value = velocity_axis[max_idx]
                max_time = (start_idx + max_idx) / sample_rate
                
                print(f"  {axis_name}轴: 最大速度时间 {max_time:.3f}s, 速度 {max_value:.3f}")
    
    return start_idx, velocity_x, velocity_y, velocity_z

def analyze_main_secondary_axes():
    """
    分析主轴+副轴合成检测的详细过程
    """
    print("\n=== 分析主轴+副轴合成检测过程 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_multi_axis_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    
    # 确定主运动轴
    x_var = np.var(processed_data[:, 0])
    z_var = np.var(processed_data[:, 2])
    main_axis = 0 if x_var > z_var else 2
    main_name = 'X' if main_axis == 0 else 'Z'
    
    print(f"主运动轴: {main_name}轴 (方差: X={x_var:.4f}, Z={z_var:.4f})")
    
    # 计算速度
    window = 5
    smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
    smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
    smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
    
    combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
    threshold = 0.1 * np.max(combined_acc)
    start_candidates = np.where(combined_acc > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    dt = 1.0 / sample_rate
    velocity_x = np.cumsum(smoothed_x[start_idx:]) * dt
    velocity_y = np.cumsum(smoothed_y[start_idx:]) * dt
    velocity_z = np.cumsum(smoothed_z[start_idx:]) * dt
    
    # 分析主轴和副轴
    axis_velocities = [velocity_x, velocity_y, velocity_z]
    axis_names = ['X', 'Y', 'Z']
    
    main_velocity = axis_velocities[main_axis]
    secondary_indices = [i for i in range(3) if i != main_axis]
    secondary_velocities = [axis_velocities[i] for i in secondary_indices]
    secondary_names = [axis_names[i] for i in secondary_indices]
    
    # 计算副轴合成速度
    secondary_magnitude = np.sqrt(secondary_velocities[0]**2 + secondary_velocities[1]**2)
    
    # 分析峰值时间
    min_samples = int(0.1 * sample_rate)
    
    # 主轴峰值
    if len(main_velocity) > min_samples:
        valid_range = main_velocity[min_samples:]
        if len(valid_range) > 0:
            max_idx_in_valid = np.argmax(np.abs(valid_range))
            max_idx = min_samples + max_idx_in_valid
            main_max_time = (start_idx + max_idx) / sample_rate
            main_max_value = main_velocity[max_idx]
            print(f"主轴({main_name})最大速度: 时间 {main_max_time:.3f}s, 速度 {main_max_value:.3f}")
    
    # 副轴合成峰值
    if len(secondary_magnitude) > min_samples:
        valid_range = secondary_magnitude[min_samples:]
        if len(valid_range) > 0:
            max_idx_in_valid = np.argmax(valid_range)
            max_idx = min_samples + max_idx_in_valid
            secondary_max_time = (start_idx + max_idx) / sample_rate
            secondary_max_value = secondary_magnitude[max_idx]
            print(f"副轴({secondary_names[0]}+{secondary_names[1]})合成最大速度: 时间 {secondary_max_time:.3f}s, 速度 {secondary_max_value:.3f}")
    
    return start_idx, main_velocity, secondary_magnitude, main_name, secondary_names

def visualize_all_methods_comparison():
    """
    可视化所有方法的对比
    """
    print("\n=== 生成所有方法对比图 ===")
    
    # 获取测试数据和结果
    acc_data, time_axis, results = test_all_velocity_methods()
    start_idx, velocity_x, velocity_y, velocity_z = analyze_individual_axes()
    start_idx2, main_velocity, secondary_magnitude, main_name, secondary_names = analyze_main_secondary_axes()
    
    sample_rate = 100
    
    # 创建可视化
    fig, axes = plt.subplots(4, 1, figsize=(14, 12))
    
    # 子图1: 三轴加速度数据
    axes[0].plot(time_axis, acc_data[:, 0], 'r-', label='X轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 1], 'g-', label='Y轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 2], 'b-', label='Z轴加速度', alpha=0.8)
    
    # 标记不同方法的检测结果
    colors = ['purple', 'orange', 'cyan']
    method_names = ['velocity', 'velocity_individual', 'velocity_main_secondary']
    method_labels = ['三轴合速度', '三轴独立', '主轴+副轴']
    
    for i, (method_name, label, color) in enumerate(zip(method_names, method_labels, colors)):
        if results[method_name]['success']:
            result = results[method_name]
            axes[0].axvspan(result['start_time'], result['end_time'], 
                           color=color, alpha=0.3, label=f'{label}检测')
    
    axes[0].set_title('三轴加速度数据与不同方法检测结果对比')
    axes[0].set_ylabel('加速度 (m/s²)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 子图2: 三轴速度分量
    time_vel = np.arange(start_idx, start_idx + len(velocity_x)) / sample_rate
    axes[1].plot(time_vel, velocity_x, 'r-', label='X轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_y, 'g-', label='Y轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_z, 'b-', label='Z轴速度', linewidth=2)
    
    # 标记各轴的峰值点
    min_samples = int(0.1 * sample_rate)
    for velocity_axis, color, axis_name in zip([velocity_x, velocity_y, velocity_z], ['red', 'green', 'blue'], ['X', 'Y', 'Z']):
        if len(velocity_axis) > min_samples:
            valid_range = velocity_axis[min_samples:]
            if len(valid_range) > 0:
                max_idx = min_samples + np.argmax(np.abs(valid_range))
                max_time = time_vel[max_idx]
                max_value = velocity_axis[max_idx]
                axes[1].plot(max_time, max_value, 'o', color=color, markersize=8, 
                           label=f'{axis_name}轴峰值')
    
    axes[1].set_title('三轴速度分量与峰值点')
    axes[1].set_ylabel('速度 (m/s)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 子图3: 主轴和副轴合成对比
    time_main = np.arange(start_idx2, start_idx2 + len(main_velocity)) / sample_rate
    axes[2].plot(time_main, main_velocity, 'purple', label=f'主轴({main_name})速度', linewidth=3)
    axes[2].plot(time_main, secondary_magnitude, 'orange', label=f'副轴({secondary_names[0]}+{secondary_names[1]})合成', linewidth=3)
    
    axes[2].set_title('主轴速度 vs 副轴合成速度')
    axes[2].set_ylabel('速度 (m/s)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    # 子图4: 检测结果总结
    axes[3].text(0.05, 0.8, '检测结果对比:', fontsize=12, fontweight='bold')
    
    y_pos = 0.7
    for method_name, label in zip(method_names, method_labels):
        if results[method_name]['success']:
            result = results[method_name]
            axes[3].text(0.05, y_pos, f'{label}: {result["start_time"]:.3f}s - {result["end_time"]:.3f}s (持续{result["duration"]:.3f}s)', 
                        fontsize=10)
        else:
            axes[3].text(0.05, y_pos, f'{label}: 检测失败', fontsize=10, color='red')
        y_pos -= 0.1
    
    axes[3].text(0.05, 0.3, '方法特点:', fontsize=12, fontweight='bold')
    axes[3].text(0.05, 0.2, '• 三轴合速度: 考虑整体运动幅值', fontsize=10)
    axes[3].text(0.05, 0.1, '• 三轴独立: 选择最早的轴峰值', fontsize=10)
    axes[3].text(0.05, 0.0, '• 主轴+副轴: 主要和次要运动分离', fontsize=10)
    
    axes[3].set_xlim(0, 1)
    axes[3].set_ylim(0, 1)
    axes[3].axis('off')
    
    plt.tight_layout()
    plt.savefig('new_velocity_methods_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("对比图已保存为: new_velocity_methods_comparison.png")

def test_different_scenarios():
    """
    测试不同运动场景下的检测效果
    """
    print("\n=== 测试不同运动场景 ===")
    
    scenarios = [
        ("X轴主导", lambda t: (2*np.sin(np.pi*t), 0.5*np.sin(np.pi*t), 0.3*np.sin(np.pi*t))),
        ("Y轴最早", lambda t: (1.5*np.sin(np.pi*t), 2*np.sin(np.pi*t*1.5), 1*np.sin(np.pi*t))),
        ("Z轴最强", lambda t: (1*np.sin(np.pi*t), 0.8*np.sin(np.pi*t), 2.5*np.sin(np.pi*t))),
        ("均匀三轴", lambda t: (1.5*np.sin(np.pi*t), 1.5*np.sin(np.pi*t*1.1), 1.5*np.sin(np.pi*t*0.9)))
    ]
    
    sample_rate = 100
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    print("场景名称        三轴合速度  三轴独立    主轴+副轴   最佳方法")
    print("-" * 65)
    
    for scenario_name, pattern_func in scenarios:
        try:
            # 创建测试数据
            duration = 2.0
            t = np.linspace(0, duration, int(sample_rate * duration))
            t_motion = t[(t >= 0.2) & (t <= 1.2)]  # 运动时间段
            
            acc_x, acc_y, acc_z = pattern_func(t_motion - 0.2)
            
            # 创建完整的加速度数组
            full_acc_x = np.zeros_like(t)
            full_acc_y = np.zeros_like(t)
            full_acc_z = np.zeros_like(t)
            
            motion_mask = (t >= 0.2) & (t <= 1.2)
            full_acc_x[motion_mask] = acc_x
            full_acc_y[motion_mask] = acc_y
            full_acc_z[motion_mask] = acc_z
            
            # 添加噪声
            noise = 0.05
            full_acc_x += noise * np.random.normal(0, 1, len(t))
            full_acc_y += noise * np.random.normal(0, 1, len(t))
            full_acc_z += noise * np.random.normal(0, 1, len(t))
            
            acc_data = np.column_stack([full_acc_x, full_acc_y, full_acc_z])
            
            # 测试三种方法
            methods = ['velocity', 'velocity_individual', 'velocity_main_secondary']
            durations = []
            
            for method in methods:
                try:
                    start_idx, end_idx, confidence, pattern_result = detector.detect_single_motion_event(
                        acc_data, only_acceleration_phase=True, use_integration=True, detection_method=method
                    )
                    duration_detected = (end_idx - start_idx) / sample_rate
                    durations.append(duration_detected)
                except:
                    durations.append(None)
            
            # 确定最佳方法（最接近理论值1.0s的）
            expected_duration = 1.0
            best_method = "N/A"
            if all(d is not None for d in durations):
                errors = [abs(d - expected_duration) for d in durations]
                best_idx = np.argmin(errors)
                method_names = ['合速度', '独立', '主+副']
                best_method = method_names[best_idx]
            
            # 格式化输出
            duration_strs = []
            for d in durations:
                if d is not None:
                    duration_strs.append(f"{d:.3f}s")
                else:
                    duration_strs.append("失败")
            
            print(f"{scenario_name:12} {duration_strs[0]:>10} {duration_strs[1]:>10} {duration_strs[2]:>10}   {best_method}")
            
        except Exception as e:
            print(f"{scenario_name:12} {'错误':>10} {'错误':>10} {'错误':>10}   检测失败")

if __name__ == "__main__":
    print("新的velocity检测方法测试")
    print("=" * 60)
    
    print("新增方法说明:")
    print("1. velocity_individual: 三轴独立检测，选取最早的峰值时间")
    print("2. velocity_main_secondary: 主轴+副轴合成，选取较早的时间")
    print("3. velocity: 原有的三轴合速度矢量方法")
    print()
    
    # 测试所有方法
    test_all_velocity_methods()
    
    # 分析详细过程
    analyze_individual_axes()
    analyze_main_secondary_axes()
    
    # 生成可视化对比
    visualize_all_methods_comparison()
    
    # 测试不同场景
    test_different_scenarios()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n新方法总结:")
    print("1. ✅ 实现了三轴独立检测方法")
    print("2. ✅ 实现了主轴+副轴合成检测方法")
    print("3. ✅ 保持了原有三轴合速度方法")
    print("4. ✅ 提供了详细的检测过程分析")
    print("5. ✅ 支持不同运动场景的适应性检测")
    print("\n现在有三种velocity检测方法可供选择！")
