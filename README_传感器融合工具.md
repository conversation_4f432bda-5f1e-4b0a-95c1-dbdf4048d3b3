# 传感器融合工具集

## 版本说明

### 🆕 AHRS库版本 (推荐)
- **文件**: `segment_acc_to_world_ahrs.py`
- **特点**: 使用标准AHRS库的官方Madgwick和Mahony算法实现
- **优势**: 更高精度、更好稳定性、经过充分测试的算法
- **依赖**: `pip install ahrs`
- **说明文档**: `README_AHRS版本使用说明.md`

### 手写算法版本
- **文件**: `segment_acc_to_world_advanced.py`
- **特点**: 自己实现的Madgwick和Mahony算法
- **优势**: 无外部依赖、便于理解和修改
- **适用**: 学习研究、定制化需求

### 基础版本
- **文件**: `segment_acc_to_world.py`、`segment_acc_to_world_fusion.py`
- **特点**: 基础的坐标系转换方法
- **适用**: 简单应用场景

---

# 传感器融合姿态估计工具使用说明

## 简介

这是一个集成了 Madgwick 和 Mahony 算法的高精度传感器融合工具，用于将设备坐标系的加速度数据转换到世界坐标系。

## 功能特点

- ✅ **高精度姿态估计**：集成了先进的 Madgwick 和 Mahony 算法
- ✅ **多传感器融合**：同时使用加速度计、陀螺仪、磁力计数据
- ✅ **批量处理**：支持处理多个片段文件
- ✅ **实时可视化**：自动生成详细的分析图表
- ✅ **运动学计算**：可选计算速度和位移
- ✅ **易于使用**：交互式界面，参数可调

## 依赖安装

运行前请确保安装以下 Python 包：

```bash
pip install numpy pandas matplotlib
```

## 文件结构要求

数据文件夹应包含以下格式的文件：
```
segment1_acce_TIMESTAMP.csv  # 加速度数据
segment1_grav_TIMESTAMP.csv  # 重力数据  
segment1_mag_TIMESTAMP.csv   # 磁力计数据
segment1_gyro_TIMESTAMP.csv  # 陀螺仪数据（可选）
segment2_acce_TIMESTAMP.csv
...
```

## 使用方法

### 1. 交互式使用（推荐）

```bash
python segment_acc_to_world_fusion.py
```

按提示输入：
- 数据文件夹路径
- 算法选择（Madgwick/Mahony）
- 采样率设置
- 输出文件夹
- 是否计算运动学

### 2. 编程使用

```python
from segment_acc_to_world_fusion import process_all_segments_with_fusion

# 基本用法
results = process_all_segments_with_fusion(
    data_folder='path/to/your/data',
    algorithm='madgwick',  # 或 'mahony'
    sample_rate=100,
    calculate_kinematics=True
)

# 处理单个片段
from segment_acc_to_world_fusion import load_segment_files, process_segment_with_fusion

segment_data = load_segment_files('path/to/data')
result = process_segment_with_fusion(
    segment_data, 
    segment_num=1, 
    algorithm='madgwick'
)
```

## 算法选择

### Madgwick 算法
- **优点**：精度高，适合大多数应用
- **缺点**：计算复杂度稍高
- **适用**：对精度要求高的应用

### Mahony 算法  
- **优点**：计算效率高，适合实时应用
- **缺点**：精度略低于Madgwick
- **适用**：实时性要求高的应用

## 输出文件

处理完成后会生成：

1. **世界坐标系加速度数据**：`segment{N}_{algorithm}_world_acce_{timestamp}.csv`
2. **可视化图表**：`segment{N}_{algorithm}_fusion_visualization_{timestamp}.png`
3. **运动学数据**（可选）：`segment{N}_{algorithm}_kinematics_{timestamp}.csv`

## 数据格式

### 输入数据格式
```csv
time,x,y,z
HH-MM-SS.fff,value1,value2,value3
...
```

### 输出数据格式
```csv
time,x,y,z,x_raw,y_raw,z_raw,qw,qx,qy,qz
```

其中：
- `x,y,z`：世界坐标系加速度（东向、北向、上向）
- `x_raw,y_raw,z_raw`：原始设备坐标系加速度
- `qw,qx,qy,qz`：姿态四元数

## 参数调优

### Madgwick 参数
- `beta`：收敛速度参数，默认0.1，可调范围0.01-2.86
- 增大beta：收敛更快，但可能增加噪声
- 减小beta：更平滑，但收敛较慢

### Mahony 参数
- `kp`：比例增益，默认1.0，可调范围0.1-10.0
- `ki`：积分增益，默认0.0，可调范围0.0-0.3
- 增大kp：响应更快
- 增大ki：减少稳态误差

## 常见问题

### Q: 处理结果精度不够？
A: 尝试：
1. 调整算法参数（beta、kp、ki）
2. 确认传感器数据质量
3. 检查采样率设置
4. 考虑传感器标定

### Q: 处理速度太慢？
A: 尝试：
1. 使用Mahony算法
2. 降低采样率
3. 减少数据量

### Q: 磁力计数据异常？
A: 系统会自动降级到IMU模式（仅使用加速度计和陀螺仪）

## 技术原理

### 坐标系转换
1. **设备坐标系**：以设备为参考的坐标系
2. **世界坐标系**：以地球为参考的坐标系（东-北-上）
3. **转换过程**：通过姿态四元数计算旋转矩阵进行坐标转换

### 传感器融合
- **加速度计**：提供重力方向信息
- **陀螺仪**：提供角速度信息
- **磁力计**：提供磁北方向信息
- **融合算法**：综合多传感器信息估计设备姿态

## 引用

如果使用本工具，请引用：

```
Madgwick, S. O. H., Harrison, A. J. L., & Vaidyanathan, R. (2011). 
Estimation of IMU and MARG orientation using a gradient descent algorithm. 
In Proc. IEEE Int. Conf. Rehabil. Robot. (pp. 1-7).

Mahony, R., Hamel, T., & Pflimlin, J. M. (2008). 
Nonlinear complementary filters on the special orthogonal group. 
IEEE Trans. Automat. Control, 53(5), 1203-1218.
```

## 联系信息

如有问题，请联系开发者或查看代码注释。

---

**版本**：1.0  
**最后更新**：2025年1月16日
