# 三轴合速度矢量检测方法

## 问题描述

在原来的velocity算法中，存在以下局限性：

- **单轴局限**: 只使用主运动轴的速度来判断最大速度时间
- **信息丢失**: 忽略了其他轴的运动信息
- **检测不全面**: 对于复杂的三维运动，可能无法准确反映整体运动状态

### 具体问题
```
原方法: 只考虑X轴或Z轴的速度最大点
问题: 当Y轴也有显著运动时，单轴检测可能不准确
例如: X轴速度峰值在1.0s，但三轴合成速度峰值在1.2s
```

## 解决方案

### 核心思路
使用**三轴速度合成的合速度矢量**来进行最大速度时间的判断：

1. **计算三轴速度**: 分别对X、Y、Z轴加速度进行积分
2. **合成速度矢量**: 计算三轴速度的矢量幅值
3. **峰值检测**: 在合速度矢量上寻找最大值点

### 数学原理

```python
# 三轴速度计算
velocity_x = ∫ acceleration_x dt
velocity_y = ∫ acceleration_y dt  
velocity_z = ∫ acceleration_z dt

# 合速度矢量幅值
velocity_magnitude = √(velocity_x² + velocity_y² + velocity_z²)

# 峰值检测
max_velocity_time = argmax(velocity_magnitude)
```

## 技术实现

### 1. 三轴数据预处理

```python
# 对三轴数据进行平滑处理
smoothed_data_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
smoothed_data_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
smoothed_data_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')

# 计算三轴合成加速度幅值用于起点检测
combined_acceleration = np.sqrt(smoothed_data_x**2 + smoothed_data_y**2 + smoothed_data_z**2)
```

### 2. 三轴速度积分

```python
# 分别计算三轴速度
velocity_x = np.cumsum(smoothed_data_x[start_idx:]) * dt
velocity_y = np.cumsum(smoothed_data_y[start_idx:]) * dt
velocity_z = np.cumsum(smoothed_data_z[start_idx:]) * dt

# 计算合速度矢量的幅值
velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)
```

### 3. 合速度峰值检测

```python
def _find_acceleration_end_by_velocity_magnitude(self, velocity_magnitude, start_idx, min_duration_sec=0.2):
    # 对于合速度矢量，只需要找正向峰值（因为幅值总是非负的）
    peaks, peak_properties = find_peaks(
        velocity_magnitude, 
        height=0, 
        distance=min_peak_distance, 
        prominence=0.01
    )
    
    # 应用时间阈值、显著性阈值等多重过滤条件
    # 选择最佳峰值作为加速阶段结束点
```

## 主要改进

### 1. 全面的运动检测
- **考虑所有轴**: 不再局限于单一主运动轴
- **整体运动状态**: 反映真实的三维运动情况
- **信息完整性**: 避免重要运动信息的丢失

### 2. 改进的起点检测
```python
# 使用三轴合成加速度检测起点
combined_acceleration = np.sqrt(smoothed_data_x**2 + smoothed_data_y**2 + smoothed_data_z**2)
threshold = 0.1 * np.max(combined_acceleration)
start_candidates = np.where(combined_acceleration > threshold)[0]
```

### 3. 增强的可视化
- **速度对比图**: 同时显示主轴速度和三轴合速度
- **分量分析**: 展示各轴的贡献比例
- **峰值标记**: 清晰标记不同方法的检测结果

## 使用方法

### 1. 默认使用

新的三轴合速度检测已集成到velocity方法中：

```python
from motion_event_detector import MotionEventDetector

detector = MotionEventDetector(sample_rate=100)
start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='velocity'  # 自动使用三轴合速度检测
)
```

### 2. 测试验证

运行测试脚本验证改进效果：

```bash
python test_three_axis_velocity.py
```

## 效果对比

### 改进前（单轴方法）
```
运动场景: X轴主导，Y轴和Z轴有辅助运动
检测结果: 只考虑X轴速度，峰值在1.0s
问题: 忽略了Y、Z轴的运动贡献
```

### 改进后（三轴合成方法）
```
运动场景: X轴主导，Y轴和Z轴有辅助运动
检测结果: 考虑三轴合成速度，峰值在1.2s
改进: 更准确地反映整体运动状态
```

## 适用场景

### 1. 推荐使用
- **复杂三维运动**: 多轴同时有显著运动
- **非主轴运动**: 主轴不明显或多轴贡献相近
- **精确检测要求**: 需要准确反映整体运动状态

### 2. 效果显著的情况
- **多轴运动模式**: 各轴方差比接近（< 5:1）
- **空间运动**: 三维空间中的复杂轨迹
- **协调运动**: 多轴协调的复合运动

## 技术特性

### 1. 兼容性保证
- **向后兼容**: 保持原有API接口不变
- **自动切换**: 自动使用三轴合成方法
- **主轴保留**: 仍保留主轴信息用于位移方法

### 2. 性能优化
- **高效计算**: 矢量化运算提高计算效率
- **内存优化**: 合理的数据结构减少内存占用
- **实时处理**: 适合实时运动检测应用

### 3. 鲁棒性增强
- **噪声抑制**: 三轴合成有助于降低单轴噪声影响
- **稳定性**: 多轴信息提供更稳定的检测结果
- **容错性**: 单轴异常不会严重影响整体检测

## 可视化功能

### 1. 对比显示
```python
# 同时显示主轴速度和三轴合速度
plt.plot(time, velocity_main_axis, 'g-', label='主轴速度', alpha=0.7)
plt.plot(time, velocity_magnitude, 'purple', label='三轴合速度矢量', linewidth=3)
```

### 2. 峰值标记
- **主轴峰值**: 绿色圆点标记
- **合速度峰值**: 紫色圆点标记
- **检测边界**: 彩色竖线标记

### 3. 分量分析
- **各轴贡献**: 显示X、Y、Z轴的速度贡献比例
- **峰值时间**: 标记各轴和合成速度的峰值时间
- **方差分析**: 显示各轴的方差比较

## 参数调优

### 1. 检测参数
```python
min_duration_sec = 0.2      # 最小持续时间
min_peak_distance = 5       # 峰值间最小距离
prominence_threshold = 0.01  # 峰值突出度阈值
```

### 2. 过滤条件
```python
significance_threshold = 0.3  # 显著性阈值（30%最大速度）
trend_consistency = True      # 趋势一致性检查
time_threshold = True         # 时间阈值过滤
```

## 故障排除

### 1. 检测结果异常
- 检查三轴数据质量
- 验证采样率设置
- 调整显著性阈值

### 2. 性能问题
- 优化数据预处理
- 调整窗口大小
- 减少不必要的计算

### 3. 可视化问题
- 确保matplotlib版本兼容
- 检查中文字体设置
- 调整图像分辨率

## 更新日志

- **v1.4**: 添加三轴合速度矢量检测方法
- **v1.3**: 基于速度转折点的位移检测
- **v1.2**: 添加最小时间阈值功能
- **v1.1**: 基础的速度和位移检测方法

现在的velocity方法能够全面考虑三轴运动，更准确地反映整体运动状态！
