# -*- coding:utf-8 -*-
"""
新的积分检测方法使用示例
演示如何使用速度和位移两种方法检测加速部分
"""

from motion_event_detector import MotionEventDetector, process_file
import numpy as np
import pandas as pd

def example_usage():
    """
    使用示例
    """
    print("=== 新的积分检测方法使用示例 ===\n")
    
    # 假设你有一个CSV文件包含加速度数据
    # 文件格式: time, x, y, z
    file_path = "your_acceleration_data.csv"  # 替换为你的文件路径
    
    print("方法1: 使用速度最大值检测加速部分")
    print("-" * 50)
    
    try:
        # 使用速度方法检测加速部分
        result_velocity = process_file(
            file_path, 
            visualize=True,                    # 生成可视化图
            only_acceleration=True,            # 只检测加速阶段
            detection_method='velocity'        # 使用速度最大值方法
        )
        
        print(f"速度方法检测结果:")
        print(f"  起始时间: {result_velocity['start_time']}")
        print(f"  结束时间: {result_velocity['end_time']}")
        print(f"  持续时间: {result_velocity['duration']:.3f} 秒")
        print(f"  置信度: {result_velocity['confidence']:.3f}")
        
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在，使用模拟数据演示")
        demo_with_synthetic_data()
        return
    
    print("\n方法2: 使用位移最大值检测加速部分")
    print("-" * 50)
    
    # 使用位移方法检测加速部分
    result_displacement = process_file(
        file_path, 
        visualize=True,                    # 生成可视化图
        only_acceleration=True,            # 只检测加速阶段
        detection_method='displacement'    # 使用位移最大值方法
    )
    
    print(f"位移方法检测结果:")
    print(f"  起始时间: {result_displacement['start_time']}")
    print(f"  结束时间: {result_displacement['end_time']}")
    print(f"  持续时间: {result_displacement['duration']:.3f} 秒")
    print(f"  置信度: {result_displacement['confidence']:.3f}")
    
    print("\n方法对比:")
    print("-" * 50)
    print(f"速度方法持续时间: {result_velocity['duration']:.3f} 秒")
    print(f"位移方法持续时间: {result_displacement['duration']:.3f} 秒")
    print(f"差异: {abs(result_velocity['duration'] - result_displacement['duration']):.3f} 秒")

def demo_with_synthetic_data():
    """
    使用合成数据演示
    """
    print("使用合成数据演示新的检测方法...")
    
    # 创建模拟的加速度数据
    sample_rate = 100
    duration = 2.0
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 创建一个典型的运动：加速 -> 减速
    acc_x = np.zeros_like(t)
    
    # 加速阶段 (0-0.8秒)
    mask1 = (t >= 0) & (t < 0.8)
    acc_x[mask1] = 3.0 * np.sin(np.pi * t[mask1] / 0.8)
    
    # 减速阶段 (0.8-2.0秒)
    mask2 = (t >= 0.8) & (t <= 2.0)
    acc_x[mask2] = -2.0 * np.sin(np.pi * (t[mask2] - 0.8) / 1.2)
    
    # 添加噪声
    acc_y = 0.1 * np.random.normal(0, 0.1, len(t))
    acc_z = 0.3 * acc_x + 0.1 * np.random.normal(0, 0.1, len(t))
    
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    print("\n使用速度方法:")
    start_vel, end_vel, conf_vel, pattern_vel = detector.detect_single_motion_event(
        acc_data, only_acceleration_phase=True, use_integration=True, detection_method='velocity'
    )
    print(f"  检测到的加速阶段: {start_vel/sample_rate:.3f}s - {end_vel/sample_rate:.3f}s")
    print(f"  持续时间: {(end_vel-start_vel)/sample_rate:.3f}s")
    print(f"  置信度: {conf_vel:.3f}")
    
    print("\n使用位移方法:")
    start_disp, end_disp, conf_disp, pattern_disp = detector.detect_single_motion_event(
        acc_data, only_acceleration_phase=True, use_integration=True, detection_method='displacement'
    )
    print(f"  检测到的加速阶段: {start_disp/sample_rate:.3f}s - {end_disp/sample_rate:.3f}s")
    print(f"  持续时间: {(end_disp-start_disp)/sample_rate:.3f}s")
    print(f"  置信度: {conf_disp:.3f}")
    
    # 可视化对比
    detector.visualize_integration_detection(acc_data, start_vel, end_vel, detector.last_main_axis, 'velocity', 'demo_velocity_method.png')
    detector.visualize_integration_detection(acc_data, start_disp, end_disp, detector.last_main_axis, 'displacement', 'demo_displacement_method.png')
    
    print(f"\n可视化结果已保存:")
    print(f"  速度方法: demo_velocity_method.png")
    print(f"  位移方法: demo_displacement_method.png")

def direct_api_usage():
    """
    直接使用API的示例
    """
    print("\n=== 直接使用API示例 ===")
    
    # 假设你已经有了加速度数据数组
    # acc_data 应该是 [N, 3] 的numpy数组，包含x, y, z三轴数据
    
    # 示例数据（实际使用时替换为你的数据）
    sample_rate = 100
    acc_data = np.random.randn(300, 3)  # 3秒的随机数据
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),  # 100ms窗口
        overlap=0.85,                        # 85%重叠
        sample_rate=sample_rate
    )
    
    # 方法1: 速度最大值检测
    start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
        acc_data, 
        only_acceleration_phase=True,    # 只检测加速阶段
        use_integration=True,            # 使用积分方法
        detection_method='velocity'      # 速度方法
    )
    
    print(f"速度方法检测结果:")
    print(f"  起始样本索引: {start_idx}")
    print(f"  结束样本索引: {end_idx}")
    print(f"  持续时间: {(end_idx-start_idx)/sample_rate:.3f} 秒")
    print(f"  置信度: {confidence:.3f}")
    print(f"  检测模式: {pattern}")
    
    # 方法2: 位移最大值检测
    start_idx2, end_idx2, confidence2, pattern2 = detector.detect_single_motion_event(
        acc_data, 
        only_acceleration_phase=True,    # 只检测加速阶段
        use_integration=True,            # 使用积分方法
        detection_method='displacement'  # 位移方法
    )
    
    print(f"\n位移方法检测结果:")
    print(f"  起始样本索引: {start_idx2}")
    print(f"  结束样本索引: {end_idx2}")
    print(f"  持续时间: {(end_idx2-start_idx2)/sample_rate:.3f} 秒")
    print(f"  置信度: {confidence2:.3f}")
    print(f"  检测模式: {pattern2}")

if __name__ == "__main__":
    print("新的积分检测方法使用指南")
    print("=" * 60)
    
    print("\n主要改进:")
    print("1. 不再依赖加速度零点检测")
    print("2. 通过积分计算速度和位移")
    print("3. 智能避免减速阶段的反向速度峰值")
    print("4. 支持两种检测方法选择")
    
    print("\n检测方法说明:")
    print("- velocity: 使用速度最大值点作为加速阶段结束点")
    print("- displacement: 使用位移最大值点作为加速阶段结束点")
    
    # 运行示例
    example_usage()
    
    # 直接API使用示例
    direct_api_usage()
    
    print("\n使用建议:")
    print("1. 对于短时间、高强度的运动，推荐使用速度方法")
    print("2. 对于长时间、渐进式的运动，推荐使用位移方法")
    print("3. 可以同时使用两种方法进行对比验证")
    print("4. 根据具体应用场景选择最适合的方法")
