# -*- coding:utf-8 -*-
"""
测试零速度点检测方法
验证新的最大速度检测优化：先找零速度点，再在0时刻到零速度点之间找最大速度
"""

import numpy as np
import matplotlib.pyplot as plt
from motion_event_detector import MotionEventDetector

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def create_zero_return_motion_data(sample_rate=100):
    """
    创建回到零速度的运动数据
    模拟加速-减速-回到零速度的完整运动过程
    """
    duration = 3.0  # 3秒数据
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # X轴：完整的加速-减速过程，在1.5s回到零速度
    acc_x = np.zeros_like(t)
    
    # 加速阶段 (0.2-0.8s)
    mask_accel = (t >= 0.2) & (t < 0.8)
    t_accel = t[mask_accel] - 0.2
    acc_x[mask_accel] = 3.0 * np.sin(np.pi * t_accel / 0.6)
    
    # 减速阶段 (0.8-1.5s)
    mask_decel = (t >= 0.8) & (t < 1.5)
    t_decel = t[mask_decel] - 0.8
    acc_x[mask_decel] = -2.0 * np.sin(np.pi * t_decel / 0.7)
    
    # Y轴：较短的运动，在1.2s回到零速度
    acc_y = np.zeros_like(t)
    
    # 加速阶段 (0.3-0.7s)
    mask_accel_y = (t >= 0.3) & (t < 0.7)
    t_accel_y = t[mask_accel_y] - 0.3
    acc_y[mask_accel_y] = 2.0 * np.sin(np.pi * t_accel_y / 0.4)
    
    # 减速阶段 (0.7-1.2s)
    mask_decel_y = (t >= 0.7) & (t < 1.2)
    t_decel_y = t[mask_decel_y] - 0.7
    acc_y[mask_decel_y] = -1.5 * np.sin(np.pi * t_decel_y / 0.5)
    
    # Z轴：更长的运动，在2.0s回到零速度
    acc_z = np.zeros_like(t)
    
    # 加速阶段 (0.4-1.0s)
    mask_accel_z = (t >= 0.4) & (t < 1.0)
    t_accel_z = t[mask_accel_z] - 0.4
    acc_z[mask_accel_z] = 2.5 * np.sin(np.pi * t_accel_z / 0.6)
    
    # 减速阶段 (1.0-2.0s)
    mask_decel_z = (t >= 1.0) & (t < 2.0)
    t_decel_z = t[mask_decel_z] - 1.0
    acc_z[mask_decel_z] = -1.8 * np.sin(np.pi * t_decel_z / 1.0)
    
    # 添加噪声
    noise_level = 0.05
    acc_x += noise_level * np.random.normal(0, 1, len(t))
    acc_y += noise_level * np.random.normal(0, 1, len(t))
    acc_z += noise_level * np.random.normal(0, 1, len(t))
    
    # 组合三轴数据
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    return acc_data, t

def test_zero_velocity_detection():
    """
    测试零速度点检测功能
    """
    print("=== 测试零速度点检测功能 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_zero_return_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    methods = [
        ('velocity', '三轴合速度矢量'),
        ('velocity_individual', '三轴独立检测'),
        ('velocity_main_secondary', '主轴+副轴合成')
    ]
    
    print("测试场景: 完整的加速-减速-回到零速度过程")
    print("预期行为: 检测到零速度点，在0时刻到零速度点之间找最大速度")
    print()
    
    results = {}
    
    for method_name, method_desc in methods:
        print(f"{'-'*50}")
        print(f"测试方法: {method_desc} ({method_name})")
        print(f"{'-'*50}")
        
        try:
            start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
                acc_data, only_acceleration_phase=True, use_integration=True, detection_method=method_name
            )
            
            duration = (end_idx - start_idx) / sample_rate
            results[method_name] = {
                'start_time': start_idx / sample_rate,
                'end_time': end_idx / sample_rate,
                'duration': duration,
                'confidence': confidence,
                'success': True
            }
            
            print(f"检测结果:")
            print(f"  起始时间: {start_idx/sample_rate:.3f}s")
            print(f"  结束时间: {end_idx/sample_rate:.3f}s")
            print(f"  持续时间: {duration:.3f}s")
            print(f"  置信度: {confidence:.3f}")
            
        except Exception as e:
            print(f"检测失败: {e}")
            results[method_name] = {
                'success': False,
                'error': str(e)
            }
        
        print()
    
    return acc_data, time_axis, results

def analyze_zero_velocity_detection():
    """
    分析零速度点检测的详细过程
    """
    print("=== 分析零速度点检测过程 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_zero_return_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    
    # 计算三轴速度
    window = 5
    smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
    smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
    smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
    
    combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
    threshold = 0.1 * np.max(combined_acc)
    start_candidates = np.where(combined_acc > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    dt = 1.0 / sample_rate
    velocity_x = np.cumsum(smoothed_x[start_idx:]) * dt
    velocity_y = np.cumsum(smoothed_y[start_idx:]) * dt
    velocity_z = np.cumsum(smoothed_z[start_idx:]) * dt
    velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)
    
    # 分析零速度点检测
    min_samples = int(0.1 * sample_rate)
    
    print(f"零速度点检测分析:")
    print(f"  时间阈值: 0.1秒 ({min_samples}个样本)")
    
    # 分析合速度的零速度点
    zero_point = detector._find_first_zero_velocity_point(velocity_magnitude, min_samples)
    if zero_point is not None:
        zero_time = (start_idx + zero_point) / sample_rate
        print(f"  合速度零速度点: 索引{zero_point}, 时间{zero_time:.3f}s")
        
        # 在零速度点之前找最大速度
        search_range = velocity_magnitude[:zero_point + 1]
        if len(search_range) > 0:
            max_idx = np.argmax(search_range)
            max_time = (start_idx + max_idx) / sample_rate
            max_value = velocity_magnitude[max_idx]
            print(f"  零速度点前最大速度: 索引{max_idx}, 时间{max_time:.3f}s, 速度{max_value:.3f}")
    else:
        print(f"  未找到合速度零速度点")
    
    # 分析各轴的零速度点
    axis_names = ['X', 'Y', 'Z']
    axis_velocities = [velocity_x, velocity_y, velocity_z]
    
    for axis_name, velocity_axis in zip(axis_names, axis_velocities):
        print(f"\n  {axis_name}轴分析:")
        zero_point = detector._find_first_velocity_decrease_single_axis(velocity_axis, min_samples, axis_name)
        if zero_point is not None:
            zero_time = (start_idx + zero_point) / sample_rate
            print(f"    零速度点: 索引{zero_point}, 时间{zero_time:.3f}s")
            
            # 在零速度点之前找最大速度
            search_range = velocity_axis[:zero_point + 1]
            if len(search_range) > 0:
                max_idx = np.argmax(np.abs(search_range))
                max_time = (start_idx + max_idx) / sample_rate
                max_value = velocity_axis[max_idx]
                print(f"    零速度点前最大速度: 索引{max_idx}, 时间{max_time:.3f}s, 速度{max_value:.3f}")
        else:
            print(f"    未找到零速度点")
    
    return start_idx, velocity_x, velocity_y, velocity_z, velocity_magnitude

def visualize_zero_velocity_detection():
    """
    可视化零速度点检测效果
    """
    print("\n=== 生成零速度点检测可视化 ===")
    
    # 获取测试数据和结果
    acc_data, time_axis, results = test_zero_velocity_detection()
    start_idx, velocity_x, velocity_y, velocity_z, velocity_magnitude = analyze_zero_velocity_detection()
    
    sample_rate = 100
    min_samples = int(0.1 * sample_rate)
    
    # 创建可视化
    fig, axes = plt.subplots(4, 1, figsize=(14, 12))
    
    # 子图1: 加速度数据
    axes[0].plot(time_axis, acc_data[:, 0], 'r-', label='X轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 1], 'g-', label='Y轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 2], 'b-', label='Z轴加速度', alpha=0.8)
    
    axes[0].set_title('加速度数据（完整的加速-减速-回零过程）')
    axes[0].set_ylabel('加速度 (m/s²)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 子图2: 三轴速度分量
    time_vel = np.arange(start_idx, start_idx + len(velocity_x)) / sample_rate
    axes[1].plot(time_vel, velocity_x, 'r-', label='X轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_y, 'g-', label='Y轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_z, 'b-', label='Z轴速度', linewidth=2)
    
    # 标记各轴的零速度点和最大速度点
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    for velocity_axis, color, axis_name in zip([velocity_x, velocity_y, velocity_z], ['red', 'green', 'blue'], ['X', 'Y', 'Z']):
        # 找零速度点
        zero_point = detector._find_first_velocity_decrease_single_axis(velocity_axis, min_samples, axis_name)
        if zero_point is not None:
            zero_time = time_vel[zero_point]
            axes[1].axvline(x=zero_time, color=color, linestyle='--', alpha=0.7, label=f'{axis_name}轴零速度点')
            
            # 在零速度点前找最大速度
            search_range = velocity_axis[:zero_point + 1]
            if len(search_range) > 0:
                max_idx = np.argmax(np.abs(search_range))
                max_time = time_vel[max_idx]
                max_value = velocity_axis[max_idx]
                axes[1].plot(max_time, max_value, 'o', color=color, markersize=8, 
                           label=f'{axis_name}轴最大速度')
    
    axes[1].set_title('三轴速度分量与零速度点检测')
    axes[1].set_ylabel('速度 (m/s)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 子图3: 合速度矢量
    axes[2].plot(time_vel, velocity_magnitude, 'purple', label='三轴合速度矢量', linewidth=3)
    
    # 标记合速度的零速度点和最大速度点
    zero_point = detector._find_first_zero_velocity_point(velocity_magnitude, min_samples)
    if zero_point is not None:
        zero_time = time_vel[zero_point]
        axes[2].axvline(x=zero_time, color='purple', linestyle='--', alpha=0.7, label='合速度零速度点')
        
        # 在零速度点前找最大速度
        search_range = velocity_magnitude[:zero_point + 1]
        if len(search_range) > 0:
            max_idx = np.argmax(search_range)
            max_time = time_vel[max_idx]
            max_value = velocity_magnitude[max_idx]
            axes[2].plot(max_time, max_value, 'o', color='purple', markersize=10, 
                       label='合速度最大值')
    
    axes[2].set_title('三轴合速度矢量与零速度点检测')
    axes[2].set_ylabel('速度 (m/s)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    # 子图4: 检测结果总结
    axes[3].text(0.05, 0.8, '零速度点检测结果:', fontsize=12, fontweight='bold')
    
    y_pos = 0.7
    for method_name in ['velocity', 'velocity_individual', 'velocity_main_secondary']:
        method_labels = {'velocity': '三轴合速度', 'velocity_individual': '三轴独立', 'velocity_main_secondary': '主轴+副轴'}
        if results[method_name]['success']:
            result = results[method_name]
            axes[3].text(0.05, y_pos, f'{method_labels[method_name]}: {result["start_time"]:.3f}s - {result["end_time"]:.3f}s (持续{result["duration"]:.3f}s)', 
                        fontsize=10)
        else:
            axes[3].text(0.05, y_pos, f'{method_labels[method_name]}: 检测失败', fontsize=10, color='red')
        y_pos -= 0.1
    
    axes[3].text(0.05, 0.3, '优化说明:', fontsize=12, fontweight='bold')
    axes[3].text(0.05, 0.2, '• 原方法: 滑动窗口检测减小5%的时间点', fontsize=10)
    axes[3].text(0.05, 0.1, '• 新方法: 检测回到0速度的时间点', fontsize=10)
    axes[3].text(0.05, 0.0, '• 优势: 在0时刻到零速度点之间找真正的最大速度', fontsize=10)
    
    axes[3].set_xlim(0, 1)
    axes[3].set_ylim(0, 1)
    axes[3].axis('off')
    
    plt.tight_layout()
    plt.savefig('zero_velocity_detection_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("零速度点检测图已保存为: zero_velocity_detection_comparison.png")

def compare_old_vs_new_method():
    """
    对比原方法和新方法的检测效果
    """
    print("\n=== 对比原方法 vs 新方法 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_zero_return_motion_data(sample_rate)
    
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 预处理和计算速度
    processed_data = detector.preprocess_data(acc_data)
    window = 5
    smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
    smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
    smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
    
    combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
    threshold = 0.1 * np.max(combined_acc)
    start_candidates = np.where(combined_acc > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    dt = 1.0 / sample_rate
    velocity_magnitude = np.sqrt(
        (np.cumsum(smoothed_x[start_idx:]) * dt)**2 + 
        (np.cumsum(smoothed_y[start_idx:]) * dt)**2 + 
        (np.cumsum(smoothed_z[start_idx:]) * dt)**2
    )
    
    min_samples = int(0.1 * sample_rate)
    
    # 新方法：零速度点检测
    zero_point = detector._find_first_zero_velocity_point(velocity_magnitude, min_samples)
    if zero_point is not None:
        new_search_range = velocity_magnitude[:zero_point + 1]
        new_max_idx = np.argmax(new_search_range) if len(new_search_range) > 0 else min_samples
        new_end_time = (start_idx + new_max_idx) / sample_rate
        new_zero_time = (start_idx + zero_point) / sample_rate
        print(f"新方法（零速度点）:")
        print(f"  零速度点: {new_zero_time:.3f}s")
        print(f"  最大速度点: {new_end_time:.3f}s")
        print(f"  搜索范围: 0 - {new_zero_time:.3f}s")
    else:
        print(f"新方法: 未找到零速度点")
    
    # 原方法：减小5%检测（模拟）
    old_decrease_point = detector._find_first_velocity_decrease(velocity_magnitude, min_samples)
    if old_decrease_point is not None:
        old_search_range = velocity_magnitude[:old_decrease_point + 1]
        old_max_idx = np.argmax(old_search_range) if len(old_search_range) > 0 else min_samples
        old_end_time = (start_idx + old_max_idx) / sample_rate
        old_decrease_time = (start_idx + old_decrease_point) / sample_rate
        print(f"\n原方法（减小5%）:")
        print(f"  减小点: {old_decrease_time:.3f}s")
        print(f"  最大速度点: {old_end_time:.3f}s")
        print(f"  搜索范围: 0 - {old_decrease_time:.3f}s")
    else:
        print(f"\n原方法: 未找到减小点")
    
    # 对比分析
    print(f"\n方法对比:")
    if zero_point is not None and old_decrease_point is not None:
        time_diff = new_zero_time - old_decrease_time
        max_time_diff = new_end_time - old_end_time
        print(f"  终止点时间差: {time_diff:.3f}s")
        print(f"  最大速度点时间差: {max_time_diff:.3f}s")
        
        if abs(time_diff) < 0.1:
            print(f"  结论: 两种方法检测结果相近")
        elif time_diff > 0:
            print(f"  结论: 新方法检测到更晚的终止点，可能更准确")
        else:
            print(f"  结论: 原方法检测到更晚的终止点")

if __name__ == "__main__":
    print("零速度点检测方法测试")
    print("=" * 60)
    
    print("优化说明:")
    print("- 原方法: 通过滑动窗口检测减小5%的时间点")
    print("- 新方法: 检测除初始时间外又一次达到0速度的时间点")
    print("- 优势: 在0时刻到零速度点之间找速度（绝对值）最大的时间")
    print()
    
    # 测试零速度点检测
    test_zero_velocity_detection()
    
    # 分析检测过程
    analyze_zero_velocity_detection()
    
    # 生成可视化
    visualize_zero_velocity_detection()
    
    # 对比新旧方法
    compare_old_vs_new_method()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n优化总结:")
    print("1. ✅ 实现了零速度点检测方法")
    print("2. ✅ 在0时刻到零速度点之间找最大速度")
    print("3. ✅ 避免了滑动窗口的复杂逻辑")
    print("4. ✅ 更直观地反映运动的完整过程")
    print("5. ✅ 提高了最大速度检测的准确性")
    print("\n现在能更准确地检测运动的真正最大速度点！")
