# -*- coding:utf-8 -*-
"""
四元数旋转角度分析工具
作者：何志想
日期：2025年07月17日
功能：读取AHRS四元数数据，转换为相对于初始状态的三轴旋转角度并可视化
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import math

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 


def quaternion_to_euler(q):
    """
    将四元数转换为欧拉角 (Roll, Pitch, Yaw)
    
    Args:
        q: 四元数数组 [w, x, y, z]
        
    Returns:
        (roll, pitch, yaw) 三个角度（弧度）
    """
    w, x, y, z = q[0], q[1], q[2], q[3]
    
    # Roll (x轴旋转) - 横滚角
    sinr_cosp = 2 * (w * x + y * z)
    cosr_cosp = 1 - 2 * (x * x + y * y)
    roll = math.atan2(sinr_cosp, cosr_cosp)
    
    # Pitch (y轴旋转) - 俯仰角
    sinp = 2 * (w * y - z * x)
    if abs(sinp) >= 1:
        pitch = math.copysign(math.pi / 2, sinp)
    else:
        pitch = math.asin(sinp)
    
    # Yaw (z轴旋转) - 偏航角
    siny_cosp = 2 * (w * z + x * y)
    cosy_cosp = 1 - 2 * (y * y + z * z)
    yaw = math.atan2(siny_cosp, cosy_cosp)
    
    return roll, pitch, yaw


def quaternion_multiply(q1, q2):
    """
    四元数乘法
    """
    w1, x1, y1, z1 = q1
    w2, x2, y2, z2 = q2
    
    w = w1*w2 - x1*x2 - y1*y2 - z1*z2
    x = w1*x2 + x1*w2 + y1*z2 - z1*y2
    y = w1*y2 - x1*z2 + y1*w2 + z1*x2
    z = w1*z2 + x1*y2 - y1*x2 + z1*w2
    
    return np.array([w, x, y, z])


def quaternion_conjugate(q):
    """
    四元数共轭
    """
    return np.array([q[0], -q[1], -q[2], -q[3]])


def normalize_quaternion(q):
    """
    标准化四元数
    """
    norm = np.linalg.norm(q)
    if norm == 0:
        return q
    return q / norm


def process_ahrs_quaternions(csv_file, reference_frame=0, sample_rate=100):
    """
    处理AHRS四元数数据，计算相对旋转角度
    
    Args:
        csv_file: CSV文件路径
        reference_frame: 参考帧索引
        sample_rate: 采样率（用于时间轴计算）
        
    Returns:
        处理后的数据字典
    """
    print(f"读取AHRS四元数数据: {csv_file}")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    print(f"数据形状: {df.shape}")
    print(f"数据列: {list(df.columns)}")
    
    # 检查数据质量
    print(f"数据示例:")
    print(df.head())
    
    # 提取四元数数据
    quaternions = df[['qw', 'qx', 'qy', 'qz']].values
    time_stamps = df['time'].values
    
    print(f"提取到 {len(quaternions)} 个四元数")
    print(f"时间范围: {time_stamps[0]} 到 {time_stamps[-1]}")
    
    # 检查四元数质量
    magnitudes = np.linalg.norm(quaternions, axis=1)
    print(f"四元数模长统计:")
    print(f"  均值: {np.mean(magnitudes):.6f}")
    print(f"  标准差: {np.std(magnitudes):.6f}")
    print(f"  最小值: {np.min(magnitudes):.6f}")
    print(f"  最大值: {np.max(magnitudes):.6f}")
    
    # 标准化所有四元数
    quaternions_normalized = np.array([normalize_quaternion(q) for q in quaternions])
    
    # 获取参考四元数（初始状态）
    reference_quaternion = quaternions_normalized[reference_frame]
    reference_conjugate = quaternion_conjugate(reference_quaternion)
    
    print(f"参考四元数 (第{reference_frame}帧):")
    print(f"  [qw={reference_quaternion[0]:.6f}, qx={reference_quaternion[1]:.6f}, qy={reference_quaternion[2]:.6f}, qz={reference_quaternion[3]:.6f}]")
    
    # 计算相对四元数和欧拉角
    relative_quaternions = []
    euler_angles = []
    
    for i, q in enumerate(quaternions_normalized):
        # 计算相对四元数: q_relative = q_current * q_reference_conjugate
        q_relative = quaternion_multiply(q, reference_conjugate)
        relative_quaternions.append(q_relative)
        
        # 转换为欧拉角
        roll, pitch, yaw = quaternion_to_euler(q_relative)
        euler_angles.append([roll, pitch, yaw])
    
    euler_angles = np.array(euler_angles)
    relative_quaternions = np.array(relative_quaternions)
    
    # 转换为度数
    euler_degrees = np.degrees(euler_angles)
    
    # 创建时间轴（相对秒数）
    time_seconds = np.arange(len(quaternions)) / sample_rate
    
    # 创建结果字典
    result = {
        'time_stamps': time_stamps,
        'time_seconds': time_seconds,
        'original_quaternions': quaternions_normalized,
        'relative_quaternions': relative_quaternions,
        'euler_radians': euler_angles,
        'euler_degrees': euler_degrees,
        'roll_deg': euler_degrees[:, 0],
        'pitch_deg': euler_degrees[:, 1],
        'yaw_deg': euler_degrees[:, 2],
        'reference_frame': reference_frame,
        'reference_quaternion': reference_quaternion,
        'data_length': len(quaternions),
        'sample_rate': sample_rate
    }
    
    print(f"\n旋转角度统计 (相对于初始状态):")
    print(f"  Roll  (横滚): [{np.min(euler_degrees[:, 0]):6.2f}°, {np.max(euler_degrees[:, 0]):6.2f}°], 标准差: {np.std(euler_degrees[:, 0]):.2f}°")
    print(f"  Pitch (俯仰): [{np.min(euler_degrees[:, 1]):6.2f}°, {np.max(euler_degrees[:, 1]):6.2f}°], 标准差: {np.std(euler_degrees[:, 1]):.2f}°")
    print(f"  Yaw   (偏航): [{np.min(euler_degrees[:, 2]):6.2f}°, {np.max(euler_degrees[:, 2]):6.2f}°], 标准差: {np.std(euler_degrees[:, 2]):.2f}°")
    
    return result


def create_comprehensive_visualization(result, output_folder='output'):
    """
    创建全面的四元数和旋转角度可视化
    """
    os.makedirs(output_folder, exist_ok=True)
    
    time_seconds = result['time_seconds']
    
    # 创建综合图表 (4行2列)
    fig, axes = plt.subplots(4, 2, figsize=(16, 20))
    
    # 1. 三轴旋转角度时间序列
    axes[0, 0].plot(time_seconds, result['roll_deg'], 'r-', label='Roll (横滚)', linewidth=1.5, alpha=0.8)
    axes[0, 0].plot(time_seconds, result['pitch_deg'], 'g-', label='Pitch (俯仰)', linewidth=1.5, alpha=0.8)
    axes[0, 0].plot(time_seconds, result['yaw_deg'], 'b-', label='Yaw (偏航)', linewidth=1.5, alpha=0.8)
    axes[0, 0].set_title('三轴旋转角度 (相对于初始状态)', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('时间 (秒)')
    axes[0, 0].set_ylabel('旋转角度 (度)')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].legend()
    axes[0, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 添加统计信息
    roll_max = np.max(np.abs(result['roll_deg']))
    pitch_max = np.max(np.abs(result['pitch_deg']))
    yaw_max = np.max(np.abs(result['yaw_deg']))
    axes[0, 0].text(0.02, 0.98, f'最大偏转:\nRoll: ±{roll_max:.1f}°\nPitch: ±{pitch_max:.1f}°\nYaw: ±{yaw_max:.1f}°', 
                   transform=axes[0, 0].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 2. 原始四元数分量
    axes[0, 1].plot(time_seconds, result['original_quaternions'][:, 0], 'k-', label='qw (标量)', linewidth=2)
    axes[0, 1].plot(time_seconds, result['original_quaternions'][:, 1], 'r-', label='qx', alpha=0.7)
    axes[0, 1].plot(time_seconds, result['original_quaternions'][:, 2], 'g-', label='qy', alpha=0.7)
    axes[0, 1].plot(time_seconds, result['original_quaternions'][:, 3], 'b-', label='qz', alpha=0.7)
    axes[0, 1].set_title('原始四元数分量 (AHRS输出)', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('时间 (秒)')
    axes[0, 1].set_ylabel('四元数值')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].legend()
    
    # 3. 相对四元数分量
    axes[1, 0].plot(time_seconds, result['relative_quaternions'][:, 0], 'k-', label='qw (相对)', linewidth=2)
    axes[1, 0].plot(time_seconds, result['relative_quaternions'][:, 1], 'r-', label='qx (相对)', alpha=0.7)
    axes[1, 0].plot(time_seconds, result['relative_quaternions'][:, 2], 'g-', label='qy (相对)', alpha=0.7)
    axes[1, 0].plot(time_seconds, result['relative_quaternions'][:, 3], 'b-', label='qz (相对)', alpha=0.7)
    axes[1, 0].set_title('相对四元数分量 (相对于初始状态)', fontsize=14, fontweight='bold')
    axes[1, 0].set_xlabel('时间 (秒)')
    axes[1, 0].set_ylabel('四元数值')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].legend()
    
    # 4. 总旋转角度幅值
    rotation_magnitude = np.sqrt(result['roll_deg']**2 + result['pitch_deg']**2 + result['yaw_deg']**2)
    axes[1, 1].plot(time_seconds, rotation_magnitude, 'purple', linewidth=2)
    axes[1, 1].fill_between(time_seconds, rotation_magnitude, alpha=0.3, color='purple')
    axes[1, 1].set_title('总旋转角度幅值', fontsize=14, fontweight='bold')
    axes[1, 1].set_xlabel('时间 (秒)')
    axes[1, 1].set_ylabel('角度幅值 (度)')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 统计信息
    max_magnitude = np.max(rotation_magnitude)
    mean_magnitude = np.mean(rotation_magnitude)
    std_magnitude = np.std(rotation_magnitude)
    axes[1, 1].text(0.02, 0.98, f'最大: {max_magnitude:.2f}°\n均值: {mean_magnitude:.2f}°\n标准差: {std_magnitude:.2f}°', 
                   transform=axes[1, 1].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 5. Roll角度详细分析
    axes[2, 0].plot(time_seconds, result['roll_deg'], 'r-', linewidth=2)
    axes[2, 0].fill_between(time_seconds, result['roll_deg'], alpha=0.3, color='red')
    axes[2, 0].set_title('Roll角度 (横滚 - 绕X轴)', fontsize=14, fontweight='bold')
    axes[2, 0].set_xlabel('时间 (秒)')
    axes[2, 0].set_ylabel('Roll角度 (度)')
    axes[2, 0].grid(True, alpha=0.3)
    axes[2, 0].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # Roll统计
    roll_std = np.std(result['roll_deg'])
    roll_range = np.max(result['roll_deg']) - np.min(result['roll_deg'])
    axes[2, 0].text(0.02, 0.98, f'范围: {roll_range:.2f}°\n标准差: {roll_std:.2f}°', 
                   transform=axes[2, 0].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
    
    # 6. Pitch角度详细分析
    axes[2, 1].plot(time_seconds, result['pitch_deg'], 'g-', linewidth=2)
    axes[2, 1].fill_between(time_seconds, result['pitch_deg'], alpha=0.3, color='green')
    axes[2, 1].set_title('Pitch角度 (俯仰 - 绕Y轴)', fontsize=14, fontweight='bold')
    axes[2, 1].set_xlabel('时间 (秒)')
    axes[2, 1].set_ylabel('Pitch角度 (度)')
    axes[2, 1].grid(True, alpha=0.3)
    axes[2, 1].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # Pitch统计
    pitch_std = np.std(result['pitch_deg'])
    pitch_range = np.max(result['pitch_deg']) - np.min(result['pitch_deg'])
    axes[2, 1].text(0.02, 0.98, f'范围: {pitch_range:.2f}°\n标准差: {pitch_std:.2f}°', 
                   transform=axes[2, 1].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    # 7. Yaw角度详细分析
    axes[3, 0].plot(time_seconds, result['yaw_deg'], 'b-', linewidth=2)
    axes[3, 0].fill_between(time_seconds, result['yaw_deg'], alpha=0.3, color='blue')
    axes[3, 0].set_title('Yaw角度 (偏航 - 绕Z轴)', fontsize=14, fontweight='bold')
    axes[3, 0].set_xlabel('时间 (秒)')
    axes[3, 0].set_ylabel('Yaw角度 (度)')
    axes[3, 0].grid(True, alpha=0.3)
    axes[3, 0].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # Yaw统计
    yaw_std = np.std(result['yaw_deg'])
    yaw_range = np.max(result['yaw_deg']) - np.min(result['yaw_deg'])
    axes[3, 0].text(0.02, 0.98, f'范围: {yaw_range:.2f}°\n标准差: {yaw_std:.2f}°', 
                   transform=axes[3, 0].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 8. 数据摘要和信息面板
    summary_text = f"""AHRS四元数旋转分析摘要

数据信息:
• 总数据点: {result['data_length']:,} 个
• 采样率: {result['sample_rate']} Hz
• 总时长: {result['data_length']/result['sample_rate']:.1f} 秒
• 参考帧: 第 {result['reference_frame']} 帧

旋转范围 (相对于初始状态):
• Roll  (横滚): {np.min(result['roll_deg']):6.2f}° ~ {np.max(result['roll_deg']):6.2f}°
• Pitch (俯仰): {np.min(result['pitch_deg']):6.2f}° ~ {np.max(result['pitch_deg']):6.2f}°
• Yaw   (偏航): {np.min(result['yaw_deg']):6.2f}° ~ {np.max(result['yaw_deg']):6.2f}°

最大单轴偏转:
• Roll:  ±{np.max(np.abs(result['roll_deg'])):6.2f}°
• Pitch: ±{np.max(np.abs(result['pitch_deg'])):6.2f}°  
• Yaw:   ±{np.max(np.abs(result['yaw_deg'])):6.2f}°

总旋转幅值:
• 最大: {np.max(rotation_magnitude):6.2f}°
• 均值: {np.mean(rotation_magnitude):6.2f}°
• 标准差: {np.std(rotation_magnitude):6.2f}°"""
    
    axes[3, 1].text(0.05, 0.95, summary_text, transform=axes[3, 1].transAxes, 
                   verticalalignment='top', fontsize=10, fontfamily='monospace',
                   bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.9))
    axes[3, 1].set_title('数据摘要', fontsize=14, fontweight='bold')
    axes[3, 1].set_xticks([])
    axes[3, 1].set_yticks([])
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime('%H-%M-%S')
    output_file = os.path.join(output_folder, f'quaternion_rotation_analysis_{timestamp}.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"已保存综合分析图表: {output_file}")
    
    return output_file


def save_analysis_data(result, output_folder='output'):
    """
    保存分析数据到CSV文件
    """
    os.makedirs(output_folder, exist_ok=True)
    
    # 计算总旋转角度幅值
    rotation_magnitude = np.sqrt(result['roll_deg']**2 + result['pitch_deg']**2 + result['yaw_deg']**2)
    
    # 创建结果DataFrame
    result_df = pd.DataFrame({
        'time_stamp': result['time_stamps'],
        'time_seconds': result['time_seconds'],
        'roll_deg': result['roll_deg'],
        'pitch_deg': result['pitch_deg'],
        'yaw_deg': result['yaw_deg'],
        'rotation_magnitude_deg': rotation_magnitude,
        'qw_original': result['original_quaternions'][:, 0],
        'qx_original': result['original_quaternions'][:, 1],
        'qy_original': result['original_quaternions'][:, 2],
        'qz_original': result['original_quaternions'][:, 3],
        'qw_relative': result['relative_quaternions'][:, 0],
        'qx_relative': result['relative_quaternions'][:, 1],
        'qy_relative': result['relative_quaternions'][:, 2],
        'qz_relative': result['relative_quaternions'][:, 3]
    })
    
    # 保存到CSV
    timestamp = datetime.now().strftime('%H-%M-%S')
    result_file = os.path.join(output_folder, f'quaternion_rotation_data_{timestamp}.csv')
    result_df.to_csv(result_file, index=False)
    print(f"已保存旋转角度数据: {result_file}")
    
    # 保存摘要统计
    summary_file = os.path.join(output_folder, f'quaternion_analysis_summary_{timestamp}.txt')
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("AHRS四元数旋转分析摘要\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"数据文件: segment1_madgwick_ahrs_quaternions_17-04-40.768.csv\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总数据点: {result['data_length']:,} 个\n")
        f.write(f"采样率: {result['sample_rate']} Hz\n")
        f.write(f"总时长: {result['data_length']/result['sample_rate']:.1f} 秒\n")
        f.write(f"参考帧: 第 {result['reference_frame']} 帧\n\n")
        
        f.write("旋转角度统计 (相对于初始状态):\n")
        f.write("-" * 40 + "\n")
        f.write(f"Roll  (横滚): [{np.min(result['roll_deg']):8.2f}°, {np.max(result['roll_deg']):8.2f}°], 标准差: {np.std(result['roll_deg']):6.2f}°\n")
        f.write(f"Pitch (俯仰): [{np.min(result['pitch_deg']):8.2f}°, {np.max(result['pitch_deg']):8.2f}°], 标准差: {np.std(result['pitch_deg']):6.2f}°\n")
        f.write(f"Yaw   (偏航): [{np.min(result['yaw_deg']):8.2f}°, {np.max(result['yaw_deg']):8.2f}°], 标准差: {np.std(result['yaw_deg']):6.2f}°\n\n")
        
        f.write("最大单轴偏转:\n")
        f.write("-" * 20 + "\n")
        f.write(f"Roll:  ±{np.max(np.abs(result['roll_deg'])):8.2f}°\n")
        f.write(f"Pitch: ±{np.max(np.abs(result['pitch_deg'])):8.2f}°\n")
        f.write(f"Yaw:   ±{np.max(np.abs(result['yaw_deg'])):8.2f}°\n\n")
        
        f.write("总旋转幅值统计:\n")
        f.write("-" * 20 + "\n")
        f.write(f"最大值: {np.max(rotation_magnitude):8.2f}°\n")
        f.write(f"均值:   {np.mean(rotation_magnitude):8.2f}°\n")
        f.write(f"标准差: {np.std(rotation_magnitude):8.2f}°\n")
    
    print(f"已保存分析摘要: {summary_file}")
    
    return result_df


def main():
    """主函数 - 处理特定的AHRS四元数文件"""
    print("=" * 70)
    print("AHRS四元数旋转角度分析工具")
    print("处理文件: segment1_madgwick_ahrs_quaternions_17-04-40.768.csv")
    print("=" * 70)
    
    csv_file = "segment1_madgwick_ahrs_quaternions_17-04-40.768.csv"
    
    # 检查文件是否存在
    if not os.path.exists(csv_file):
        print(f"错误: 找不到文件 {csv_file}")
        print("请确保文件在当前工作目录中")
        return
    
    # 参数设置
    reference_frame = 0  # 使用第一帧作为参考
    sample_rate = 100    # 假设采样率为100Hz
    
    print(f"参数设置:")
    print(f"  参考帧: {reference_frame} (初始状态)")
    print(f"  采样率: {sample_rate} Hz")
    
    try:
        # 处理四元数数据
        print(f"\n开始处理四元数数据...")
        result = process_ahrs_quaternions(csv_file, reference_frame, sample_rate)
        
        # 创建可视化
        print(f"\n生成可视化图表...")
        create_comprehensive_visualization(result)
        
        # 保存分析数据
        print(f"\n保存分析数据...")
        save_analysis_data(result)
        
        print(f"\n" + "=" * 70)
        print(f"🎉 分析完成!")
        print(f"主要发现:")
        
        max_roll = np.max(np.abs(result['roll_deg']))
        max_pitch = np.max(np.abs(result['pitch_deg']))
        max_yaw = np.max(np.abs(result['yaw_deg']))
        total_time = result['data_length'] / result['sample_rate']
        
        print(f"• 数据时长: {total_time:.1f} 秒 ({result['data_length']:,} 个数据点)")
        print(f"• 最大Roll偏转:  ±{max_roll:.2f}°")
        print(f"• 最大Pitch偏转: ±{max_pitch:.2f}°")
        print(f"• 最大Yaw偏转:   ±{max_yaw:.2f}°")
        
        # 判断主要运动方向
        if max_yaw > max_roll and max_yaw > max_pitch:
            print(f"• 主要运动: Yaw (偏航/转向) 运动")
        elif max_pitch > max_roll:
            print(f"• 主要运动: Pitch (俯仰) 运动")
        else:
            print(f"• 主要运动: Roll (横滚) 运动")
        
        print(f"=" * 70)
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
