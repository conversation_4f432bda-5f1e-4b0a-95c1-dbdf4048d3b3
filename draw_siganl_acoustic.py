# -*- coding:utf-8 -*-
"""
作者：${何志想}
日期：2024年08月11日
"""
from Auto_divide import Auto_divideSegment
from fliter_data import read_data, calcu_var
import numpy as np
from scipy.fftpack import fft
import matplotlib.pyplot as plt
from scipy.stats import zscore
import os
import scipy.io.wavfile as wav
import pandas as pd
from interpolation import sort_and_clean_data, interpolate_acc_data


def FFT (Fs,data):
    L = len (data)                        # 信号长度
    N =int(np.power(2,np.ceil(np.log2(L))))    # 下一个最近二次幂
    FFT_y1 = np.abs(fft(data,N))/L*2      # N点FFT 变化,但除以信号长度
    Fre = np.arange(int(N/2))/N*Fs       # 频率坐标
    FFT_y1 = FFT_y1[range(int(N/2))]      # 取一半
    return Fre, FFT_y1

def log_transform(signal):
    return np.log1p(signal)

def calcu_total_var(data, zeroThreshold):
    cumulative_variance = np.zeros(int(data.shape[0]/window_size))
    # 对每一列数据进行归一化
    for dim in range(data.shape[1]):
        var_data = calcu_var(data[:, dim], window_size)
        cumulative_variance += var_data
    # 这里将最大值的X以上的值设置为0
    threshold = max(cumulative_variance) * zeroThreshold
    cumulative_variance = [x if x <= threshold else 0 for x in cumulative_variance]
    return cumulative_variance

def calculate_time_info(sample_rate, window_size):
    """计算采样和时间的对应关系"""
    window_time_ms = (window_size / sample_rate) * 1000
    samples_per_ms = sample_rate / 1000
    return {
        "samples_per_second": sample_rate,
        "samples_per_ms": samples_per_ms,
        "window_time_ms": window_time_ms,
        "window_size": window_size
    }

def convert_samples_to_time(sample_index, sample_rate):
    """将样本点索引转换为时间（秒）"""
    return sample_index / sample_rate

def format_segments_info(segments_starts, segments_ends, sample_rate, window_size, start_seg, end_seg):
    """将检测到的片段信息格式化为文本，只包含指定范围内的片段"""
    segments_text = ""
    for i, (start, end) in enumerate(zip(segments_starts, segments_ends)):
        # 只处理在指定范围内的片段
        if start > start_seg and end < end_seg:
            # 计算时间，开始时间减少0.1秒
            start_time = (start * window_size) / sample_rate  # 减少0.1秒
            end_time = (end * window_size) / sample_rate + 0.1
            
            # 确保开始时间不小于0
            start_time = max(0, start_time)
            
            # 转换为分:秒.毫秒格式
            start_min = int(start_time // 60)
            start_sec = start_time % 60
            end_min = int(end_time // 60)
            end_sec = end_time % 60
            
            segment_info = f"""
片段 {i+1}:
开始时间: {start_min:02d}:{start_sec:06.3f}
结束时间: {end_min:02d}:{end_sec:06.3f}
持续时间: {end_time - start_time:.3f} 秒
窗口范围: {start} - {end}
"""
            segments_text += segment_info
    
    return segments_text

if __name__ == '__main__':

    file_path = r"E:\Document\user001\7.25\Session_20250725_225125\audio_data_22-51-25.910.wav"
    original_sampling_rate, data = wav.read(file_path)

    window_size = 200 
    


    # 计算并显示时间信息
    time_info = calculate_time_info(original_sampling_rate, window_size)


    # zeroThreshold = 0.75 #0.75
    # 计算方差和(不同列方差相加)
    # cumulative_variance = calcu_total_var(data, zeroThreshold)
    variance = calcu_var(data, window_size)
    # print("len(variance):", len(variance))

    # # 将其中某一部分置为零
    # for i in range(1200, 1400):
    #     variance[i] = 0
    # for i in range(1810, 1850):
    #     variance[i] = 0

    #标记开始分割的起点和终点
    #构建横轴label(以1000为间隔)
    tick_interval = 1000
    ticks = range(0, len(variance), tick_interval)
    tick_labels = [str(tick) for tick in ticks]
    index = []
    for i in range(len(variance)):
        index.append(i)

    # 数据标准化(均值为0，方差为1)
    zscore_variance = zscore(variance)
    PTh = np.min(zscore_variance) + 0.05 # 0.05 事件检测阈值
    PTl = np.min(zscore_variance) + 0.02  # 0.02 事件检测阈值
    
    minLen = 2   # 5 事件最小长度
    mergeThresh = 50   # 20 合并阈值
    
    peakThresh = np.min(zscore_variance) + 1   # 0.02 峰值检测阈值

    # 开始执行分割(针对方差和),检测信号开始和结束点(移除静默部分)
    autoSeg = Auto_divideSegment(zscore_variance, PTh, PTl, minLen, mergeThresh, peakThresh)
    autoSeg.run_Auto_divideSegment()

    # 标记需要分割的信息的起点/结束点
    start_seg = 150
    end_seg = 6000

    plt.xticks(ticks, tick_labels, rotation=-35)
    for xc in [start_seg, end_seg]:
        plt.axvline(x=xc, color='k', linestyle='-', linewidth=1)
    for i in range(len(autoSeg.longSegment_start)):
        start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
        plt.axvspan(start, end, color='r', alpha=0.5, linewidth=2.5)
    plt.plot(index, zscore_variance)


    # 绘制第二幅，关注低幅度区域
    plt.figure()
    plt.xticks(ticks, tick_labels, rotation=-35)
    for xc in [start_seg, end_seg]:
        plt.axvline(x=xc, color='k', linestyle='-', linewidth=1)
    for i in range(len(autoSeg.longSegment_start)):
        start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
        plt.axvspan(start, end, color='r', alpha=0.5, linewidth=0.5)
    plt.plot(index, zscore_variance)
    plt.ylim(0, 1)

    # # 绘制第三幅，对其中部分内容放大[观察具体某一部分分割是否正确]
    # plt.figure()
    # plt.xticks(ticks, tick_labels, rotation=-35)
    # for xc in [start_seg, end_seg]:
    #     plt.axvline(x=xc, color='k', linestyle='-', linewidth=1)
    # for i in range(len(autoSeg.longSegment_start)):
    #     start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
    #     plt.axvspan(start, end, color='r', alpha=0.5, linewidth=0.5)
    # plt.plot(index, zscore_variance)
    # plt.xlim(10000, 10500)


    plt.show()

    # 开始读取并保存数据
    j = 0
    for i in range(len(autoSeg.longSegment_start)):
        start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
        if start > start_seg and end < end_seg:
            # 正确的时间换算：
            # 1. 每个window包含200个采样点
            # 2. 采样率为48000Hz，即每秒48000个采样点
            # start_time = (start * window_size) / original_sampling_rate - 0.05  # 起始时间(秒)
            # end_time = (end * window_size) / original_sampling_rate + 0.10   # 结束时间(秒)
            start_time = (start * window_size) / original_sampling_rate # 起始时间(秒)
            end_time = (end * window_size) / original_sampling_rate + 0.1   # 结束时间(秒)

            start_time = max(0, start_time)
            duration = end_time - start_time
            
            # 转换为分:秒.毫秒格式
            start_min = int(start_time // 60)
            start_sec = start_time % 60
            end_min = int(end_time // 60)
            end_sec = end_time % 60
            
            print(f"\n片段 {i+1}:")
            print(f"开始时间: {start_min:02d}:{start_sec:06.3f}")
            print(f"结束时间: {end_min:02d}:{end_sec:06.3f}")
            print(f"持续时间: {duration:.3f} 秒")
            print(f"窗口范围: {start} - {end}")

    # 生成片段信息文本
    segments_info = format_segments_info(
        autoSeg.longSegment_start, 
        autoSeg.longSegment_end,
        original_sampling_rate,
        window_size,
        start_seg,  # 添加起始点
        end_seg     # 添加结束点
    )
    
    # 保存片段信息到文件
    output_dir = os.path.dirname(file_path)
    segments_file = os.path.join(output_dir, 'segments_info.txt')
    with open(segments_file, 'w') as f:
        f.write(segments_info)
    
    print(f"\n片段信息已保存至: {segments_file}")

    # 在保存片段信息之后，添加加速度数据处理部分
    print("\n开始处理对应的加速度数据...")
    
    # 构建对应的加速度数据文件路径
    acc_file = file_path.replace('audio_', 'acce_').replace('.wav', '.csv')
    gyro_file = file_path.replace('audio_', 'gyro_').replace('.wav', '.csv')
    grav_file = file_path.replace('audio_', 'grav_').replace('.wav', '.csv')
    mag_file = file_path.replace('audio_', 'mag_').replace('.wav', '.csv')
    rot_file = file_path.replace('audio_', 'rotvec_').replace('.wav', '.csv')

    if os.path.exists(acc_file):
        print(f"找到对应的加速度数据文件: {acc_file}")
        
        # 读取并处理加速度数据
        original_df = pd.read_csv(acc_file)
        orginal_gyro_df = pd.read_csv(gyro_file)
        orginal_grav_df = pd.read_csv(grav_file)
        orginal_mag_df = pd.read_csv(mag_file)
        orginal_rot_df = pd.read_csv(rot_file)
        
        # 数据排序和清理
        print("\n执行数据清理和排序...")
        cleaned_df = sort_and_clean_data(original_df)
        cleaned_gyro_df = sort_and_clean_data(orginal_gyro_df)
        cleaned_grav_df = sort_and_clean_data(orginal_grav_df)
        cleaned_mag_df = sort_and_clean_data(orginal_mag_df)
        cleaned_rot_df = sort_and_clean_data(orginal_rot_df)
        
        # 进行插值
        print("\n执行500Hz插值...")
        # interpolated_df = interpolate_acc_data(cleaned_df)
        
        # 保存处理后的数据
        sorted_file = acc_file.replace('.csv', '_sorted.csv')
        sorted_gyro_file = gyro_file.replace('.csv', '_sorted.csv')
        sorted_grav_file = grav_file.replace('.csv', '_sorted.csv')
        sorted_mag_file = mag_file.replace('.csv', '_sorted.csv')
        sorted_rot_file = rot_file.replace('.csv', '_sorted.csv')

        
        output_file = acc_file.replace('.csv', '_500hz.csv')
        
        cleaned_df.to_csv(sorted_file, index=False)
        cleaned_gyro_df.to_csv(sorted_gyro_file, index=False)
        cleaned_grav_df.to_csv(sorted_grav_file, index=False)
        cleaned_mag_df.to_csv(sorted_mag_file, index=False)
        cleaned_rot_df.to_csv(sorted_rot_file, index=False)
        # interpolated_df.to_csv(output_file, index=False)
        
        print(f"\n已保存排序后的数据: {sorted_file}")
        print(f"已保存插值后的数据: {output_file}")
        
        # 自动执行空档期提取
        print("\n开始提取空档期数据...")
        from extract_gap_segments import extract_gap_segments_from_audio_log, extract_and_save_gap_data
        
        segments, gaps = extract_gap_segments_from_audio_log(segments_info)
        extract_and_save_gap_data(sorted_file, gaps)
        extract_and_save_gap_data(sorted_gyro_file, gaps)
        extract_and_save_gap_data(sorted_grav_file, gaps)
        extract_and_save_gap_data(sorted_mag_file, gaps)
        extract_and_save_gap_data(sorted_rot_file, gaps)
        
    else:
        print(f"未找到对应的加速度数据文件: {acc_file}")




