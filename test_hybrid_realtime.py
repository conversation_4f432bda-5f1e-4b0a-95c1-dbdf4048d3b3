# -*- coding:utf-8 -*-
"""
测试修改后的混合算法 - 静态段实时坐标系计算版本
作者：何志想
日期：2025年07月16日
"""

import os
import sys
from segment_acc_to_world_hybrid import load_segment_files, process_segment_data_hybrid

def test_hybrid_realtime():
    """测试混合算法的实时静态处理功能"""
    
    print("=" * 60)
    print("测试混合算法 - 静态段实时计算版本")
    print("=" * 60)
    
    # 检查是否有数据文件
    current_dir = os.getcwd()
    test_files = []
    
    # 查找测试数据文件
    for file in os.listdir(current_dir):
        if file.startswith('segment') and 'acce' in file and file.endswith('.csv'):
            test_files.append(file)
    
    if not test_files:
        print("未找到测试数据文件，请确保有segment_acce_*.csv文件")
        return False
    
    print(f"找到 {len(test_files)} 个加速度数据文件")
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    # 提取片段编号和时间戳
    import re
    match = re.search(r'segment(\d+)_acce_(\d+-\d+-\d+\.\d+)\.csv', test_file)
    if match:
        segment_num = int(match.group(1))
        timestamp = match.group(2)
        print(f"片段编号: {segment_num}, 时间戳: {timestamp}")
    else:
        print("无法从文件名提取片段信息，使用默认值")
        segment_num = 1
        timestamp = "test"
    
    # 查找对应的其他传感器文件
    base_pattern = f"segment{segment_num}_"
    grav_file = f"{base_pattern}grav_{timestamp}.csv"
    mag_file = f"{base_pattern}mag_{timestamp}.csv"
    gyro_file = f"{base_pattern}gyro_{timestamp}.csv"
    
    print(f"\n查找对应的传感器文件:")
    print(f"加速度文件: {test_file} {'✓' if os.path.exists(test_file) else '✗'}")
    print(f"重力文件: {grav_file} {'✓' if os.path.exists(grav_file) else '✗'}")
    print(f"磁力文件: {mag_file} {'✓' if os.path.exists(mag_file) else '✗'}")
    print(f"陀螺仪文件: {gyro_file} {'✓' if os.path.exists(gyro_file) else '✗'}")
    
    # 检查必要文件
    if not os.path.exists(grav_file):
        print(f"错误: 找不到重力数据文件 {grav_file}")
        return False
    
    if not os.path.exists(mag_file):
        print(f"错误: 找不到磁力数据文件 {mag_file}")
        return False
    
    try:
        print(f"\n开始测试混合算法处理...")
        
        # 加载片段数据
        print(f"加载片段 {segment_num} 的数据...")
        segment_data_dict = load_segment_files(current_dir, segment_num)
        
        if segment_num not in segment_data_dict:
            print(f"错误: 无法加载片段 {segment_num} 的数据")
            return False
        
        segment_data = segment_data_dict[segment_num]
        print(f"成功加载片段数据，包含传感器: {list(segment_data.keys())}")
        
        # 确保有必要的传感器数据
        required_sensors = ['acce', 'grav', 'mag']
        missing_sensors = [sensor for sensor in required_sensors if sensor not in segment_data]
        if missing_sensors:
            print(f"错误: 缺少必要的传感器数据: {missing_sensors}")
            return False
        
        # 调用混合算法处理函数
        result = process_segment_data_hybrid(
            segment_data=segment_data,
            segment_num=segment_num,
            output_folder='output',
            kp=0.5,  # Mahony比例增益
            ki=0.0,  # Mahony积分增益
            static_threshold=0.1,  # 静态检测阈值
            static_duration=30     # 静态持续时间
        )
        
        if result is not None:
            print(f"\n✓ 混合算法处理成功!")
            print(f"处理结果统计:")
            print(f"- 总帧数: {len(result)}")
            
            # 统计运动阶段
            if 'motion_phase' in result.columns:
                phase_counts = result['motion_phase'].value_counts()
                print(f"- 运动阶段分布:")
                for phase, count in phase_counts.items():
                    print(f"  {phase}: {count} 帧")
            
            # 检查输出文件
            output_files = []
            for file in os.listdir('output'):
                if f"segment{segment_num}_hybrid" in file and timestamp in file:
                    output_files.append(file)
            
            print(f"- 生成输出文件: {len(output_files)} 个")
            for file in output_files:
                print(f"  {file}")
            
            print(f"\n✓ 混合算法实时静态处理测试完成")
            return True
            
        else:
            print("✗ 混合算法处理失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_hybrid_realtime()
    if success:
        print("\n" + "=" * 60)
        print("🎉 混合算法实时静态处理测试成功!")
        print("主要特性:")
        print("- ✓ 静态段：使用实时重力和磁力数据进行每一时刻的直接坐标系转换")
        print("- ✓ 动态段：使用Mahony算法基于重力数据进行姿态估计")
        print("- ✓ 无缝切换：静态段和动态段之间平滑过渡")
        print("- ✓ 数据分离：姿态估计用重力数据，坐标转换用加速度数据")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 测试失败，请检查错误信息")
        print("=" * 60)
        sys.exit(1)
