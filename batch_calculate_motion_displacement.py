# -*- coding:utf-8 -*-
"""
批量处理运动分段信息，自动计算每段运动的位移
"""

import os
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import re
from batch_calculate_displacement import calculate_specific_displacement, calculate_displacement
from motion_event_detector import time_to_seconds

def read_detection_results(result_file):
    """读取运动检测结果文件"""
    if not os.path.exists(result_file):
        print(f"结果文件不存在: {result_file}")
        return []
    
    with open(result_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 解析文件中的事件记录
    events = []
    file_sections = re.split(r'\n文件: ', content)[1:]  # 跳过文件头
    
    for section in file_sections:
        lines = section.split("\n")
        file_name = lines[0].strip()
        
        # 获取文件路径 - 构建_kalman.csv文件名
        base_name = os.path.basename(file_name)
        if "_kalman" not in base_name:
            kalman_file = base_name.replace(".csv", "_kalman.csv")
        else:
            kalman_file = base_name
        
        # 查找基本信息
        base_time = None
        sample_rate = None
        for line in lines[1:4]:
            if line.startswith("基准时间:"):
                base_time = line.split(":", 1)[1].strip()
            elif line.startswith("采样率:"):
                sample_rate_str = line.split(":", 1)[1].strip().split(" ")[0]
                sample_rate = float(sample_rate_str)
        
        # 查找事件时间段
        current_event = None
        event_counter = 0
        
        for i, line in enumerate(lines):
            if "事件 #" in line:
                event_counter += 1
                current_event = {
                    'file_name': file_name,
                    'kalman_file': kalman_file,
                    'base_time': base_time,
                    'sample_rate': sample_rate,
                    'event_number': event_counter
                }
            
            elif "  起始时间:" in line and current_event:
                current_event['start_time'] = line.split(":", 1)[1].strip()
            
            elif "  结束时间:" in line and current_event:
                current_event['end_time'] = line.split(":", 1)[1].strip()
            
            elif "  持续时间:" in line and current_event:
                duration_str = line.split(":", 1)[1].strip().replace(" 秒", "")
                current_event['duration'] = float(duration_str)
                
                # 当找到完整的一个事件信息后，保存事件并重置
                if all(k in current_event for k in ['start_time', 'end_time', 'duration']):
                    events.append(current_event)
                    current_event = None
    
    print(f"从文件中解析出 {len(events)} 个事件")
    return events

def find_kalman_file(event, data_dir):
    """查找对应的Kalman滤波文件"""
    kalman_file = event['kalman_file']
    
    # 1. 直接在数据目录下查找
    direct_path = os.path.join(data_dir, kalman_file)
    if os.path.exists(direct_path):
        return direct_path
    
    # 2. 移除原始文件名中的.csv后缀，确保正确构建kalman文件名
    base_name = os.path.basename(event['file_name'])
    file_stem = os.path.splitext(base_name)[0]
    kalman_name = f"{file_stem}_kalman.csv"
    
    # 3. 递归搜索文件夹
    for root, _, files in os.walk(data_dir):
        for file in files:
            if file == kalman_name:
                return os.path.join(root, file)
    
    # 4. 尝试模糊匹配
    pattern = file_stem.split('_')[0]  # 获取segment部分
    for root, _, files in os.walk(data_dir):
        for file in files:
            if pattern in file and "_kalman.csv" in file:
                return os.path.join(root, file)
    
    return None

def process_events(events, data_dir, output_dir=None, visualize=True):
    """处理所有检测到的事件"""
    if not events:
        print("没有可处理的事件")
        return
    
    # 创建输出目录
    if output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(data_dir, f"disp_results_{timestamp}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建汇总结果文件
    summary_file = os.path.join(output_dir, "disp_summary.csv")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("文件,事件编号,起始时间,结束时间,持续时间(秒),X轴最终位移(cm),Y轴最终位移(cm),Z轴最终位移(cm),总位移(cm),X轴最大位移(cm),Y轴最大位移(cm),Z轴最大位移(cm)\n")
    
    # 创建详细报告文件
    report_file = os.path.join(output_dir, "displacement_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("运动事件位移分析报告\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"数据目录: {data_dir}\n")
        f.write("=" * 80 + "\n\n")
    
    # 处理每个事件
    successful = 0
    failed = 0
    
    for i, event in enumerate(events):
        print(f"\n处理事件 {i+1}/{len(events)}: {event['file_name']} - 事件 #{event['event_number']}")
        print(f"时间范围: {event['start_time']} - {event['end_time']}")
        
        # 查找对应的Kalman文件
        kalman_file = find_kalman_file(event, data_dir)
        if not kalman_file:
            print(f"未找到Kalman滤波文件: {event['kalman_file']}")
            failed += 1
            
            # 记录失败信息
            with open(report_file, 'a', encoding='utf-8') as f:
                f.write(f"文件: {event['file_name']} - 事件 #{event['event_number']}\n")
                f.write(f"  起始时间: {event['start_time']}\n")
                f.write(f"  结束时间: {event['end_time']}\n")
                f.write(f"  持续时间: {event['duration']:.3f} 秒\n")
                f.write(f"  错误: 未找到对应的Kalman滤波文件\n")
                f.write("-" * 50 + "\n\n")
            
            continue
        
        try:
            # 计算位移 - 通过调用calculate_specific_displacement函数
            print(f"计算位移: {kalman_file}")
            df = pd.read_csv(kalman_file)
            
            # 提取指定时间范围的数据
            mask = (df['time'] >= event['start_time']) & (df['time'] <= event['end_time'])
            segment_data = df.loc[mask].copy()
            
            if segment_data.empty:
                print(f"错误: 未找到时间范围 {event['start_time']} - {event['end_time']} 内的数据")
                failed += 1
                
                # 记录失败信息
                with open(report_file, 'a', encoding='utf-8') as f:
                    f.write(f"文件: {event['file_name']} - 事件 #{event['event_number']}\n")
                    f.write(f"  起始时间: {event['start_time']}\n")
                    f.write(f"  结束时间: {event['end_time']}\n")
                    f.write(f"  持续时间: {event['duration']:.3f} 秒\n")
                    f.write(f"  错误: 未找到指定时间范围内的数据\n")
                    f.write("-" * 50 + "\n\n")
                
                continue
            
            # 计算相对时间
            time_seconds = np.array([time_to_seconds(t) for t in segment_data['time']])
            time_seconds = time_seconds - time_seconds[0]
            
            # 重命名列以计算速度
            segment_data = segment_data.rename(columns={'x': 'ax', 'y': 'ay', 'z': 'az'})
            
            # 计算速度
            from calculate_velocity import calculate_velocity_axis
            vel_x, vel_y, vel_z = calculate_velocity_axis(segment_data, time_seconds)
            
            # 计算位移
            disp_x = calculate_displacement(vel_x, time_seconds)
            disp_y = calculate_displacement(vel_y, time_seconds)
            disp_z = calculate_displacement(vel_z, time_seconds)
            
            # 保存结果
            base_name = os.path.basename(kalman_file)
            file_stem = os.path.splitext(base_name)[0]
            start_time_str = event['start_time'].replace(":", "-")
            
            results_df = pd.DataFrame({
                'time': segment_data['time'],
                'vel_x': vel_x,
                'vel_y': vel_y,
                'vel_z': vel_z,
                'disp_x': disp_x,
                'disp_y': disp_y,
                'disp_z': disp_z
            })
            
            output_file = os.path.join(output_dir, f"{file_stem}_displacement.csv")
            results_df.to_csv(output_file, index=False)
            
            # 计算位移指标 - 最终位移和最大位移
            final_x = disp_x[-1]
            final_y = disp_y[-1]
            final_z = disp_z[-1]
            
            max_disp_x = np.max(np.abs(disp_x))
            max_disp_y = np.max(np.abs(disp_y))
            max_disp_z = np.max(np.abs(disp_z))
            
            # 计算总位移
            total_disp = np.sqrt(final_x**2 + final_y**2 + final_z**2)
            
            # 可视化结果
            if visualize:
                fig = plt.figure(figsize=(12, 10))
                
                # 绘制位移
                plt.subplot(3, 1, 1)
                plt.plot(time_seconds, disp_x, 'r-', label='X轴')
                plt.plot(time_seconds, disp_y, 'g-', label='Y轴')
                plt.plot(time_seconds, disp_z, 'b-', label='Z轴')
                plt.title('三轴位移')
                plt.xlabel('时间 (秒)')
                plt.ylabel('位移 (cm)')
                plt.grid(True)
                plt.legend()
                
                # 绘制速度
                plt.subplot(3, 1, 2)
                plt.plot(time_seconds, vel_x, 'r-', label='X轴')
                plt.plot(time_seconds, vel_y, 'g-', label='Y轴')
                plt.plot(time_seconds, vel_z, 'b-', label='Z轴')
                plt.title('三轴速度')
                plt.xlabel('时间 (秒)')
                plt.ylabel('速度 (cm/s)')
                plt.grid(True)
                plt.legend()
                
                # 绘制加速度
                plt.subplot(3, 1, 3)
                plt.plot(time_seconds, segment_data['ax'], 'r-', label='X轴')
                plt.plot(time_seconds, segment_data['ay'], 'g-', label='Y轴')
                plt.plot(time_seconds, segment_data['az'], 'b-', label='Z轴')
                plt.title('三轴加速度')
                plt.xlabel('时间 (秒)')
                plt.ylabel('加速度 (m/s²)')
                plt.grid(True)
                plt.legend()
                
                plt.tight_layout()
                
                # 保存图像
                fig_file = os.path.join(output_dir, f"{file_stem}_event{event['event_number']}_displacement.png")
                plt.savefig(fig_file)
                plt.close()
            
            # 添加到汇总文件
            with open(summary_file, 'a', encoding='utf-8') as f:
                f.write(f"{base_name},{event['event_number']},{event['start_time']},{event['end_time']},"
                       f"{event['duration']:.3f},{final_x:.4f},{final_y:.4f},{final_z:.4f},{total_disp:.4f},"
                       f"{max_disp_x:.4f},{max_disp_y:.4f},{max_disp_z:.4f}\n")
            
            # 添加到报告文件
            with open(report_file, 'a', encoding='utf-8') as f:
                f.write(f"文件: {base_name} - 事件 #{event['event_number']}\n")
                f.write(f"  起始时间: {event['start_time']}\n")
                f.write(f"  结束时间: {event['end_time']}\n")
                f.write(f"  持续时间: {event['duration']:.3f} 秒\n")
                f.write(f"  数据点数: {len(segment_data)}\n")
                f.write("\n  最终位移:\n")
                f.write(f"    X轴: {final_x:.4f} cm\n")
                f.write(f"    Y轴: {final_y:.4f} cm\n")
                f.write(f"    Z轴: {final_z:.4f} cm\n")
                f.write(f"    总位移: {total_disp:.4f} cm\n")
                f.write("\n  最大位移:\n")
                f.write(f"    X轴: {max_disp_x:.4f} cm\n")
                f.write(f"    Y轴: {max_disp_y:.4f} cm\n")
                f.write(f"    Z轴: {max_disp_z:.4f} cm\n")
                f.write(f"  结果CSV: {os.path.basename(output_file)}\n")
                if visualize:
                    f.write(f"  结果图像: {os.path.basename(fig_file)}\n")
                f.write("-" * 50 + "\n\n")
            
            successful += 1
            print(f"位移计算成功:")
            print(f"  最终位移: X={final_x:.2f} cm, Y={final_y:.2f} cm, Z={final_z:.2f} cm")
            print(f"  总位移: {total_disp:.2f} cm")
            print(f"  结果保存至: {output_file}")
            
        except Exception as e:
            print(f"处理事件时出错: {str(e)}")
            failed += 1
            
            # 记录错误
            with open(report_file, 'a', encoding='utf-8') as f:
                f.write(f"文件: {event['file_name']} - 事件 #{event['event_number']}\n")
                f.write(f"  起始时间: {event['start_time']}\n")
                f.write(f"  结束时间: {event['end_time']}\n")
                f.write(f"  持续时间: {event['duration']:.3f} 秒\n")
                f.write(f"  处理错误: {str(e)}\n")
                f.write("-" * 50 + "\n\n")
    
    # 写入统计信息
    with open(report_file, 'a', encoding='utf-8') as f:
        f.write("=" * 50 + "\n")
        f.write(f"处理统计: 共 {len(events)} 个事件，成功 {successful} 个，失败 {failed} 个\n")
    
    print(f"\n处理完成，结果保存至: {output_dir}")
    print(f"共 {len(events)} 个事件，成功 {successful} 个，失败 {failed} 个")
    print(f"汇总报告: {summary_file}")
    print(f"详细报告: {report_file}")

def main():
    """主函数"""
    print("批量计算IMU事件位移程序")
    print("-" * 50)
    
    # 获取输入参数
    data_dir = r"E:\Document\user001\7.25\Session_20250725_225125 - 副本 - 副本 - 副本\output_world_coordinates"
    if not os.path.exists(data_dir):
        print(f"错误: 目录不存在 {data_dir}")
        return
    
    # 查找事件检测结果文件
    result_files = glob.glob(os.path.join(data_dir, "event_detection_results_*.txt"))
    
    if not result_files:
        print(f"在 {data_dir} 中未找到事件检测结果文件")
        custom_file = input("请手动输入事件检测结果文件路径 (可拖入文件): ").strip().strip('"')
        if os.path.exists(custom_file):
            result_files = [custom_file]
        else:
            print("无法找到有效的事件检测结果文件，程序退出")
            return
    
    # 如果找到多个结果文件，让用户选择
    if len(result_files) > 1:
        print("\n找到多个事件检测结果文件:")
        for i, file in enumerate(result_files):
            print(f"{i+1}. {os.path.basename(file)}")
        
        choice = input("\n请选择要处理的文件编号: ")
        try:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(result_files):
                result_file = result_files[choice_idx]
            else:
                print("无效的选择，使用最新的文件")
                result_file = max(result_files, key=os.path.getmtime)
        except ValueError:
            print("无效的输入，使用最新的文件")
            result_file = max(result_files, key=os.path.getmtime)
    else:
        result_file = result_files[0]
    
    print(f"\n使用事件检测结果文件: {os.path.basename(result_file)}")
    
    # 可视化选择
    # vis_choice = input("是否生成可视化图表 [Y/N，默认Y]: ").strip().lower()
    visualize = True
    
    # 输出目录
    output_dir = data_dir
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
        except Exception as e:
            print(f"创建目录失败: {str(e)}")
            output_dir = None
    
    # 读取事件列表
    events = read_detection_results(result_file)
    
    if events:
        # 处理事件
        process_events(events, data_dir, output_dir, visualize)
    else:
        print("未从结果文件中读取到有效事件，程序退出")

if __name__ == "__main__":
    main()
