# -*- coding:utf-8 -*-
"""
四元数到欧拉角转换与可视化工具
作者：何志想
日期：2025年07月17日
功能：读取四元数CSV文件，转换为相对于初始状态的三轴旋转角度并可视化
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import math

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 


def quaternion_to_euler(q):
    """
    将四元数转换为欧拉角 (Roll, Pitch, Yaw)
    
    Args:
        q: 四元数数组 [w, x, y, z] 或 [qw, qx, qy, qz]
        
    Returns:
        (roll, pitch, yaw) 三个角度（弧度）
    """
    w, x, y, z = q[0], q[1], q[2], q[3]
    
    # Roll (x轴旋转)
    sinr_cosp = 2 * (w * x + y * z)
    cosr_cosp = 1 - 2 * (x * x + y * y)
    roll = math.atan2(sinr_cosp, cosr_cosp)
    
    # Pitch (y轴旋转)
    sinp = 2 * (w * y - z * x)
    if abs(sinp) >= 1:
        pitch = math.copysign(math.pi / 2, sinp)  # 使用 90 度，如果超出范围
    else:
        pitch = math.asin(sinp)
    
    # Yaw (z轴旋转)
    siny_cosp = 2 * (w * z + x * y)
    cosy_cosp = 1 - 2 * (y * y + z * z)
    yaw = math.atan2(siny_cosp, cosy_cosp)
    
    return roll, pitch, yaw


def quaternion_multiply(q1, q2):
    """
    四元数乘法
    
    Args:
        q1: 四元数1 [w, x, y, z]
        q2: 四元数2 [w, x, y, z]
        
    Returns:
        乘积四元数 [w, x, y, z]
    """
    w1, x1, y1, z1 = q1
    w2, x2, y2, z2 = q2
    
    w = w1*w2 - x1*x2 - y1*y2 - z1*z2
    x = w1*x2 + x1*w2 + y1*z2 - z1*y2
    y = w1*y2 - x1*z2 + y1*w2 + z1*x2
    z = w1*z2 + x1*y2 - y1*x2 + z1*w2
    
    return np.array([w, x, y, z])


def quaternion_conjugate(q):
    """
    四元数共轭
    
    Args:
        q: 四元数 [w, x, y, z]
        
    Returns:
        共轭四元数 [w, -x, -y, -z]
    """
    return np.array([q[0], -q[1], -q[2], -q[3]])


def normalize_quaternion(q):
    """
    标准化四元数
    
    Args:
        q: 四元数 [w, x, y, z]
        
    Returns:
        标准化后的四元数
    """
    norm = np.linalg.norm(q)
    if norm == 0:
        return q
    return q / norm


def process_quaternion_csv(csv_file, reference_frame=0):
    """
    处理四元数CSV文件，计算相对于初始状态的旋转角度
    
    Args:
        csv_file: CSV文件路径
        reference_frame: 参考帧索引（默认为0，即初始状态）
        
    Returns:
        处理后的数据字典
    """
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    print(f"读取四元数数据文件: {csv_file}")
    print(f"数据形状: {df.shape}")
    print(f"数据列: {list(df.columns)}")
    print(f"数据示例:\n{df.head()}")
    
    # 检查必要的列
    required_cols = ['qw', 'qx', 'qy', 'qz']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"错误: 缺少必要的列: {missing_cols}")
        return None
    
    # 提取四元数数据
    quaternions = df[required_cols].values
    print(f"提取到 {len(quaternions)} 个四元数")
    
    # 检查四元数质量
    magnitudes = np.linalg.norm(quaternions, axis=1)
    print(f"四元数模长统计: 均值={np.mean(magnitudes):.6f}, 标准差={np.std(magnitudes):.6f}")
    
    # 标准化所有四元数
    quaternions_normalized = np.array([normalize_quaternion(q) for q in quaternions])
    
    # 获取参考四元数（初始状态）
    reference_quaternion = quaternions_normalized[reference_frame]
    reference_conjugate = quaternion_conjugate(reference_quaternion)
    print(f"参考四元数 (第{reference_frame}帧): [{reference_quaternion[0]:.6f}, {reference_quaternion[1]:.6f}, {reference_quaternion[2]:.6f}, {reference_quaternion[3]:.6f}]")
    
    # 计算相对四元数和欧拉角
    relative_quaternions = []
    euler_angles = []
    
    for i, q in enumerate(quaternions_normalized):
        # 计算相对四元数: q_relative = q_current * q_reference_conjugate
        q_relative = quaternion_multiply(q, reference_conjugate)
        relative_quaternions.append(q_relative)
        
        # 转换为欧拉角
        roll, pitch, yaw = quaternion_to_euler(q_relative)
        euler_angles.append([roll, pitch, yaw])
    
    euler_angles = np.array(euler_angles)
    relative_quaternions = np.array(relative_quaternions)
    
    # 转换为度数
    euler_degrees = np.degrees(euler_angles)
    
    # 创建结果字典
    result = {
        'time': df['time'].values if 'time' in df.columns else np.arange(len(quaternions)),
        'original_quaternions': quaternions_normalized,
        'relative_quaternions': relative_quaternions,
        'euler_radians': euler_angles,
        'euler_degrees': euler_degrees,
        'roll_deg': euler_degrees[:, 0],
        'pitch_deg': euler_degrees[:, 1],
        'yaw_deg': euler_degrees[:, 2],
        'reference_frame': reference_frame,
        'reference_quaternion': reference_quaternion
    }
    
    print(f"成功处理四元数数据:")
    print(f"- 旋转角度范围 (度):")
    print(f"  Roll:  [{np.min(euler_degrees[:, 0]):.2f}, {np.max(euler_degrees[:, 0]):.2f}]")
    print(f"  Pitch: [{np.min(euler_degrees[:, 1]):.2f}, {np.max(euler_degrees[:, 1]):.2f}]")
    print(f"  Yaw:   [{np.min(euler_degrees[:, 2]):.2f}, {np.max(euler_degrees[:, 2]):.2f}]")
    
    return result


def visualize_rotation_angles(result, output_folder='output', file_prefix='rotation_analysis'):
    """
    可视化旋转角度数据
    
    Args:
        result: process_quaternion_csv 返回的结果字典
        output_folder: 输出文件夹
        file_prefix: 输出文件前缀
    """
    if result is None:
        print("无效的结果数据")
        return
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    # 创建时间轴
    time_data = result['time']
    if isinstance(time_data[0], str):
        # 如果是时间字符串，转换为相对秒数
        time_seconds = np.arange(len(time_data)) * 0.01  # 假设100Hz采样率
    else:
        time_seconds = time_data
    
    # 创建综合可视化图表
    fig, axes = plt.subplots(3, 2, figsize=(15, 12))
    
    # 1. 三轴旋转角度时间序列
    axes[0, 0].plot(time_seconds, result['roll_deg'], 'r-', label='Roll (绕X轴)', linewidth=1.5)
    axes[0, 0].plot(time_seconds, result['pitch_deg'], 'g-', label='Pitch (绕Y轴)', linewidth=1.5)
    axes[0, 0].plot(time_seconds, result['yaw_deg'], 'b-', label='Yaw (绕Z轴)', linewidth=1.5)
    axes[0, 0].set_title('三轴旋转角度 (相对于初始状态)')
    axes[0, 0].set_xlabel('时间 (秒)')
    axes[0, 0].set_ylabel('旋转角度 (度)')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].legend()
    axes[0, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 2. 原始四元数分量
    axes[0, 1].plot(time_seconds, result['original_quaternions'][:, 0], 'k-', label='qw', linewidth=2)
    axes[0, 1].plot(time_seconds, result['original_quaternions'][:, 1], 'r-', label='qx', alpha=0.7)
    axes[0, 1].plot(time_seconds, result['original_quaternions'][:, 2], 'g-', label='qy', alpha=0.7)
    axes[0, 1].plot(time_seconds, result['original_quaternions'][:, 3], 'b-', label='qz', alpha=0.7)
    axes[0, 1].set_title('原始四元数分量')
    axes[0, 1].set_xlabel('时间 (秒)')
    axes[0, 1].set_ylabel('四元数值')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].legend()
    
    # 3. 相对四元数分量
    axes[1, 0].plot(time_seconds, result['relative_quaternions'][:, 0], 'k-', label='qw (相对)', linewidth=2)
    axes[1, 0].plot(time_seconds, result['relative_quaternions'][:, 1], 'r-', label='qx (相对)', alpha=0.7)
    axes[1, 0].plot(time_seconds, result['relative_quaternions'][:, 2], 'g-', label='qy (相对)', alpha=0.7)
    axes[1, 0].plot(time_seconds, result['relative_quaternions'][:, 3], 'b-', label='qz (相对)', alpha=0.7)
    axes[1, 0].set_title('相对四元数分量 (相对于初始状态)')
    axes[1, 0].set_xlabel('时间 (秒)')
    axes[1, 0].set_ylabel('四元数值')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].legend()
    
    # 4. 旋转角度幅值
    rotation_magnitude = np.sqrt(result['roll_deg']**2 + result['pitch_deg']**2 + result['yaw_deg']**2)
    axes[1, 1].plot(time_seconds, rotation_magnitude, 'purple', linewidth=2)
    axes[1, 1].set_title('总旋转角度幅值')
    axes[1, 1].set_xlabel('时间 (秒)')
    axes[1, 1].set_ylabel('角度幅值 (度)')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加统计信息
    max_magnitude = np.max(rotation_magnitude)
    mean_magnitude = np.mean(rotation_magnitude)
    axes[1, 1].text(0.05, 0.95, f'最大: {max_magnitude:.2f}°\n均值: {mean_magnitude:.2f}°', 
                   transform=axes[1, 1].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 5. Roll角度详细分析
    axes[2, 0].plot(time_seconds, result['roll_deg'], 'r-', linewidth=2)
    axes[2, 0].fill_between(time_seconds, result['roll_deg'], alpha=0.3, color='red')
    axes[2, 0].set_title('Roll角度 (绕X轴旋转)')
    axes[2, 0].set_xlabel('时间 (秒)')
    axes[2, 0].set_ylabel('Roll角度 (度)')
    axes[2, 0].grid(True, alpha=0.3)
    axes[2, 0].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # Roll统计
    roll_max = np.max(np.abs(result['roll_deg']))
    roll_std = np.std(result['roll_deg'])
    axes[2, 0].text(0.05, 0.95, f'最大偏转: ±{roll_max:.2f}°\n标准差: {roll_std:.2f}°', 
                   transform=axes[2, 0].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
    
    # 6. Pitch和Yaw角度对比
    axes[2, 1].plot(time_seconds, result['pitch_deg'], 'g-', label='Pitch (绕Y轴)', linewidth=2)
    axes[2, 1].plot(time_seconds, result['yaw_deg'], 'b-', label='Yaw (绕Z轴)', linewidth=2)
    axes[2, 1].set_title('Pitch & Yaw角度对比')
    axes[2, 1].set_xlabel('时间 (秒)')
    axes[2, 1].set_ylabel('角度 (度)')
    axes[2, 1].grid(True, alpha=0.3)
    axes[2, 1].legend()
    axes[2, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # Pitch和Yaw统计
    pitch_max = np.max(np.abs(result['pitch_deg']))
    yaw_max = np.max(np.abs(result['yaw_deg']))
    axes[2, 1].text(0.05, 0.95, f'Pitch最大: ±{pitch_max:.2f}°\nYaw最大: ±{yaw_max:.2f}°', 
                   transform=axes[2, 1].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
    output_file = os.path.join(output_folder, f'{file_prefix}_rotation_analysis_{timestamp}.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"已保存旋转角度分析图表: {output_file}")
    
    # 保存处理后的数据
    result_df = pd.DataFrame({
        'time': result['time'],
        'roll_deg': result['roll_deg'],
        'pitch_deg': result['pitch_deg'],
        'yaw_deg': result['yaw_deg'],
        'rotation_magnitude': rotation_magnitude,
        'qw_original': result['original_quaternions'][:, 0],
        'qx_original': result['original_quaternions'][:, 1],
        'qy_original': result['original_quaternions'][:, 2],
        'qz_original': result['original_quaternions'][:, 3],
        'qw_relative': result['relative_quaternions'][:, 0],
        'qx_relative': result['relative_quaternions'][:, 1],
        'qy_relative': result['relative_quaternions'][:, 2],
        'qz_relative': result['relative_quaternions'][:, 3]
    })
    
    result_file = os.path.join(output_folder, f'{file_prefix}_rotation_data_{timestamp}.csv')
    result_df.to_csv(result_file, index=False)
    print(f"已保存旋转角度数据: {result_file}")
    
    return result_df


def main():
    """主函数"""
    print("=" * 60)
    print("四元数旋转角度分析工具")
    print("功能：将四元数转换为相对于初始状态的三轴旋转角度")
    print("=" * 60)
    
    # 查找CSV文件
    csv_files = []
    for file in os.listdir('.'):
        if file.endswith('.csv') and 'quaternion' in file.lower():
            csv_files.append(file)
    
    if not csv_files:
        print("未找到四元数CSV文件")
        print("请将四元数CSV文件放在当前目录下，文件名应包含'quaternion'")
        return
    
    print(f"找到 {len(csv_files)} 个可能的四元数文件:")
    for i, file in enumerate(csv_files):
        print(f"{i+1}. {file}")
    
    # 选择文件
    if len(csv_files) == 1:
        selected_file = csv_files[0]
        print(f"自动选择: {selected_file}")
    else:
        while True:
            try:
                choice = int(input(f"请选择文件 (1-{len(csv_files)}): "))
                if 1 <= choice <= len(csv_files):
                    selected_file = csv_files[choice-1]
                    break
                else:
                    print("无效选择，请重试")
            except ValueError:
                print("请输入数字")
    
    # 选择参考帧
    reference_frame = 0
    ref_input = input("请输入参考帧索引 [默认: 0 (初始状态)]: ").strip()
    if ref_input:
        try:
            reference_frame = int(ref_input)
        except ValueError:
            print("无效输入，使用默认值0")
            reference_frame = 0
    
    print(f"\n开始处理文件: {selected_file}")
    print(f"参考帧: {reference_frame}")
    
    # 处理数据
    result = process_quaternion_csv(selected_file, reference_frame)
    
    if result is not None:
        # 可视化
        print(f"\n开始生成可视化...")
        visualize_rotation_angles(result, output_folder='output', 
                                file_prefix=os.path.splitext(selected_file)[0])
        
        print(f"\n处理完成!")
        print(f"数据摘要:")
        print(f"- 数据点数: {len(result['time'])}")
        print(f"- 参考帧: {reference_frame}")
        print(f"- 最大旋转角度: {np.max(np.sqrt(result['roll_deg']**2 + result['pitch_deg']**2 + result['yaw_deg']**2)):.2f}°")
    else:
        print("处理失败")


if __name__ == "__main__":
    main()
