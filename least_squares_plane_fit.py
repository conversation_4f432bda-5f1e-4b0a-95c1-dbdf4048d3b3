import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import glob

def find_best_fit_plane_least_squares(points):
    """
    使用最小二乘法找到最佳拟合平面
    
    参数:
    points: numpy数组，形状为(n, 3)，表示n个3D点的坐标
    
    返回:
    normal: 平面法向量
    d: 平面方程中的常数项
    """
    points = np.asarray(points, dtype=np.float64)
    # 计算点的质心
    centroid = np.mean(points, axis=0)
    
    # 将点平移到质心为原点
    centered_points = points - centroid
    
    # 构建矩阵A，每行为一个点的坐标
    A = centered_points
    
    # 使用SVD分解求解最小二乘问题
    U, S, Vt = np.linalg.svd(A)
    
    # 最小奇异值对应的右奇异向量即为法向量
    normal = Vt[-1]
    
    # 确保法向量指向正确方向（可根据应用需求调整）
    if normal[2] < 0:
        normal = -normal
    
    # 计算平面方程中的常数项d
    d = -np.dot(normal, centroid)
    
    return normal, d

def project_vector_to_plane(vector, plane_normal):
    """
    计算向量在平面上的投影
    
    参数:
    vector: 要投影的向量
    plane_normal: 平面的法向量（单位向量）
    
    返回:
    投影后的向量
    """
    # 确保法向量是单位向量
    plane_normal = plane_normal / np.linalg.norm(plane_normal)
    
    # 计算向量在法向量方向上的分量
    projection_on_normal = np.dot(vector, plane_normal) * plane_normal
    
    # 向量减去法向量方向的分量，得到平面上的投影
    projection_on_plane = vector - projection_on_normal
    
    return projection_on_plane

def find_optimal_projection_plane_least_squares(csv_folder, return_source_files=False):
    """
    使用最小二乘法找到使所有位移矢量投影和最长的平面
    
    参数:
    csv_folder: 包含CSV文件的文件夹路径
    return_source_files: 是否返回每个向量对应的源文件路径
    
    返回:
    plane_normal: 平面法向量
    d: 平面方程中的常数项
    displacement_endpoints: 所有位移矢量的终点坐标
    source_files: (可选) 每个向量对应的源文件路径
    """
    # 获取文件夹中所有后缀为displacement的CSV文件
    csv_files = glob.glob(os.path.join(csv_folder, '*displacement*.csv'))
    
    if len(csv_files) == 0:
        print("未找到包含displacement的CSV文件！")
        return None, None, None
    
    # 收集所有位移矢量的终点坐标
    displacement_endpoints = []
    
    # 如果要求返回源文件路径，则创建一个列表存储文件路径
    source_files_list = []
    
    for csv_file in csv_files:
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file)
            
            # 提取终点位移坐标
            if all(col in df.columns for col in ['disp_x', 'disp_y', 'disp_z']):
                endpoint = np.array([df['disp_x'].iloc[-1], df['disp_y'].iloc[-1], df['disp_z'].iloc[-1]])
            elif all(col in df.columns for col in ['x', 'y', 'z']):
                endpoint = np.array([df['x'].iloc[-1], df['y'].iloc[-1], df['z'].iloc[-1]])
            elif len(df.columns) >= 3:
                endpoint = np.array([df.iloc[-1, 0], df.iloc[-1, 1], df.iloc[-1, 2]])
            else:
                print(f"文件 {os.path.basename(csv_file)} 格式不正确，跳过")
                continue
            
            displacement_endpoints.append(endpoint)
            
            if return_source_files:
                source_files_list.append(csv_file)
            
        except Exception as e:
            print(f"处理文件 {os.path.basename(csv_file)} 时出错: {str(e)}")
    
    # 转换为numpy数组
    displacement_endpoints = np.array(displacement_endpoints)
    
    if len(displacement_endpoints) < 3:  # 最小二乘法需要至少3个点
        print("有效位移数据不足，需要至少3个位移矢量")
        return None, None, None
    
    # 使用最小二乘法找到最佳拟合平面
    plane_normal, d = find_best_fit_plane_least_squares(displacement_endpoints)
    
    if return_source_files:
        return plane_normal, d, displacement_endpoints, source_files_list
    else:
        return plane_normal, d, displacement_endpoints

def visualize_least_squares_plane(csv_folder, output_file=None):
    """
    可视化使用最小二乘法找到的最佳拟合平面和位移矢量的投影
    
    参数:
    csv_folder: 包含CSV文件的文件夹路径
    output_file: 可选，输出图像的文件路径
    """
    # 找到最佳拟合平面
    plane_normal, d, displacement_endpoints = find_optimal_projection_plane_least_squares(csv_folder)
    
    if plane_normal is None:
        return
    
    # 创建3D图
    fig = plt.figure(figsize=(14, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    # 为了绘制平面，我们需要创建平面上的点网格
    # 我们先确定平面范围
    all_points = displacement_endpoints
    
    # 计算数据范围
    x_min, x_max = all_points[:, 0].min(), all_points[:, 0].max()
    y_min, y_max = all_points[:, 1].min(), all_points[:, 1].max()
    z_min, z_max = all_points[:, 2].min(), all_points[:, 2].max()
    
    # 计算范围差
    range_diff = max(x_max - x_min, y_max - y_min, z_max - z_min)
    
    # 创建一个较大的平面用于可视化
    xx, yy = np.meshgrid(
        np.linspace(x_min - range_diff*0.2, x_max + range_diff*0.2, 10),
        np.linspace(y_min - range_diff*0.2, y_max + range_diff*0.2, 10)
    )
    
    # 计算平面上的z值（使用平面方程 ax + by + cz + d = 0）
    a, b, c = plane_normal
    zz = (-a * xx - b * yy - d) / c if c != 0 else np.zeros_like(xx)
    
    # 绘制平面
    surf = ax.plot_surface(xx, yy, zz, alpha=0.2, color='blue')
    
    # 计算平面上的一个点（用于投影）
    plane_point = np.array([0, 0, -d/c]) if c != 0 else np.array([0, -d/b, 0]) if b != 0 else np.array([-d/a, 0, 0])
    
    # 绘制位移矢量和它们的投影
    colors = plt.cm.jet(np.linspace(0, 1, len(displacement_endpoints)))
    
    for i, endpoint in enumerate(displacement_endpoints):
        # 原始位移矢量（从原点到终点）
        ax.quiver(0, 0, 0, endpoint[0], endpoint[1], endpoint[2], 
                 color=colors[i], alpha=0.7, arrow_length_ratio=0.1)
        
        # 计算投影
        projection = project_vector_to_plane(endpoint, plane_normal)
        
        # 绘制投影矢量
        ax.quiver(0, 0, 0, projection[0], projection[1], projection[2], 
                 color=colors[i], linestyle='dotted', linewidth=2, alpha=1.0,
                 arrow_length_ratio=0.1)
        
        # 绘制从终点到投影点的连接线
        ax.plot([endpoint[0], projection[0]], 
                [endpoint[1], projection[1]], 
                [endpoint[2], projection[2]], 
                'k--', alpha=0.3)
    
    # 绘制平面法向量
    ax.quiver(0, 0, 0, 
             plane_normal[0], plane_normal[1], plane_normal[2], 
             color='red', linewidth=2, label='Plane Normal', length=range_diff*0.3)
    
    # 绘制坐标轴
    ax.set_xlabel('X axis')
    ax.set_ylabel('Y axis')
    ax.set_zlabel('Z axis')
    
    # 设置坐标轴范围
    buffer = range_diff * 0.2
    ax.set_xlim([x_min - buffer, x_max + buffer])
    ax.set_ylim([y_min - buffer, y_max + buffer])
    ax.set_zlim([z_min - buffer, z_max + buffer])
    
    # 计算每个位移矢量到平面的距离
    distances = []
    for point in displacement_endpoints:
        # 点到平面的距离: |ax + by + cz + d| / sqrt(a^2 + b^2 + c^2)
        distance = abs(np.dot(plane_normal, point) + d) / np.linalg.norm(plane_normal)
        distances.append(distance)
    
    # 计算平均距离和最大距离
    mean_distance = np.mean(distances)
    max_distance = np.max(distances)
    
    # 计算原始矢量和投影矢量的长度之和
    original_lengths = np.linalg.norm(displacement_endpoints, axis=1)
    original_sum = np.sum(original_lengths)
    
    projected_vectors = np.array([project_vector_to_plane(v, plane_normal) for v in displacement_endpoints])
    projected_lengths = np.linalg.norm(projected_vectors, axis=1)
    projected_sum = np.sum(projected_lengths)
    
    # 计算保留的长度比例
    preserved_ratio = projected_sum / original_sum
    
    # 输出平面方程和距离信息
    plane_eq = f"{a:.3f}x + {b:.3f}y + {c:.3f}z + {d:.3f} = 0"
    print(f"Least Squares Fitted Plane Equation: {plane_eq}")
    print(f"Average Distance Error: {mean_distance:.5f}")
    print(f"Maximum Distance Error: {max_distance:.5f}")
    print(f"Preserved Length Ratio: {preserved_ratio:.3f} ({preserved_ratio*100:.1f}%)")
    
    # 标题和信息
    plt.title('Least Squares Best-Fit Plane')
    
    # 添加平面方程说明
    ax.text2D(0.05, 0.95, f"Plane Equation: {plane_eq}", 
             transform=ax.transAxes, fontsize=9, 
             bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    # 添加距离误差说明
    ax.text2D(0.05, 0.90, f"Average Distance Error: {mean_distance:.5f}", 
             transform=ax.transAxes, fontsize=9,
             bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    # 添加保留比例说明
    ax.text2D(0.05, 0.85, f"Preserved Length Ratio: {preserved_ratio*100:.1f}%", 
             transform=ax.transAxes, fontsize=9,
             bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    # 如果指定了输出文件路径，保存图像
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    plt.tight_layout()
    plt.show()

    return plane_normal, d, preserved_ratio

def compare_pca_and_least_squares(csv_folder):
    """
    比较PCA和最小二乘法找到的最佳平面
    
    参数:
    csv_folder: 包含CSV文件的文件夹路径
    """
    print("======= PCA方法 =======")
    # 导入PCA相关函数
    from optimal_projection_plane import find_optimal_projection_plane
    
    plane_normal_pca, plane_point_pca, pca, displacement_endpoints = find_optimal_projection_plane(csv_folder)
    
    if plane_normal_pca is None:
        print("无法使用PCA方法计算")
        return
    
    # 计算PCA平面方程
    a_pca, b_pca, c_pca = plane_normal_pca
    d_pca = -np.dot(plane_normal_pca, plane_point_pca)
    pca_eq = f"{a_pca:.3f}x + {b_pca:.3f}y + {c_pca:.3f}z + {d_pca:.3f} = 0"
    print(f"PCA平面方程：{pca_eq}")
    
    # 计算解释方差比
    explained_variance_ratio = pca.explained_variance_ratio_
    print(f"PCA解释方差比: {', '.join(['{:.1f}%'.format(100 * var) for var in explained_variance_ratio])}")
    
    # 计算投影保留比例
    projected_vectors_pca = np.array([project_vector_to_plane(v, plane_normal_pca) for v in displacement_endpoints])
    projected_lengths_pca = np.linalg.norm(projected_vectors_pca, axis=1)
    original_lengths = np.linalg.norm(displacement_endpoints, axis=1)
    preserved_ratio_pca = np.sum(projected_lengths_pca) / np.sum(original_lengths)
    print(f"PCA投影保留的长度比例：{preserved_ratio_pca:.3f} ({preserved_ratio_pca*100:.1f}%)")
    
    print("\n======= 最小二乘法 =======")
    plane_normal_ls, d_ls, preserved_ratio_ls = visualize_least_squares_plane(csv_folder)
    
    if plane_normal_ls is None:
        print("无法使用最小二乘法计算")
        return
    
    # 计算两个法向量之间的夹角
    dot_product = np.dot(plane_normal_pca, plane_normal_ls)
    angle = np.arccos(min(abs(dot_product), 1.0)) * 180 / np.pi
    
    print("\n======= 比较结果 =======")
    print(f"两种方法法向量夹角: {angle:.2f}°")
    print(f"PCA保留比例: {preserved_ratio_pca*100:.1f}%, 最小二乘法保留比例: {preserved_ratio_ls*100:.1f}%")
    print(f"保留比例差异: {abs(preserved_ratio_pca - preserved_ratio_ls)*100:.2f}%")

if __name__ == "__main__":
    # 指定包含CSV文件的文件夹路径
    csv_folder = r"E:\Study\科研\data\平面估计\3"
    
    # 可选：指定输出图像的路径
    output_file = r"E:\Study\科研\data\平面估计\3\最小二乘法拟合平面.png"
    
    # 使用最小二乘法可视化最佳拟合平面
    # visualize_least_squares_plane(csv_folder, output_file)
    
    # 比较PCA和最小二乘法
    compare_pca_and_least_squares(csv_folder)
