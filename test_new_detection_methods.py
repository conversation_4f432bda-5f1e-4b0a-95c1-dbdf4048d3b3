# -*- coding:utf-8 -*-
"""
测试新的积分检测方法
演示速度最大值和位移最大值两种检测方法的差异
"""

import numpy as np
import matplotlib.pyplot as plt
from motion_event_detector import MotionEventDetector, process_file
import os

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def create_synthetic_motion_data(sample_rate=100, duration=3.0):
    """
    创建合成的运动数据用于测试
    包含明显的加速、匀速、减速阶段
    """
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 创建一个典型的运动模式：加速 -> 匀速 -> 减速 -> 反向加速
    # 加速阶段 (0-1秒): 加速度从0增加到最大值
    # 匀速阶段 (1-1.5秒): 加速度为0
    # 减速阶段 (1.5-2.5秒): 加速度从0减少到负最大值
    # 反向加速 (2.5-3秒): 加速度从负值回到0
    
    acc_x = np.zeros_like(t)
    
    # 加速阶段
    mask1 = (t >= 0) & (t < 1.0)
    acc_x[mask1] = 2.0 * np.sin(np.pi * t[mask1] / 1.0)  # 正弦加速
    
    # 匀速阶段
    mask2 = (t >= 1.0) & (t < 1.5)
    acc_x[mask2] = 0.0
    
    # 减速阶段
    mask3 = (t >= 1.5) & (t < 2.5)
    acc_x[mask3] = -1.5 * np.sin(np.pi * (t[mask3] - 1.5) / 1.0)  # 负正弦减速
    
    # 反向加速阶段
    mask4 = (t >= 2.5) & (t <= 3.0)
    acc_x[mask4] = -0.5 * np.sin(np.pi * (t[mask4] - 2.5) / 0.5)  # 反向加速
    
    # Y轴和Z轴添加少量噪声
    acc_y = 0.1 * np.random.normal(0, 0.1, len(t))
    acc_z = 0.2 * acc_x + 0.1 * np.random.normal(0, 0.1, len(t))
    
    # 组合三轴数据
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    return acc_data, t

def compare_detection_methods():
    """
    比较速度方法和位移方法的检测结果
    """
    print("=== 比较速度方法和位移方法的检测结果 ===\n")
    
    # 创建合成数据
    sample_rate = 100
    acc_data, time_axis = create_synthetic_motion_data(sample_rate=sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    print("1. 使用速度最大值方法检测加速阶段:")
    print("-" * 50)
    
    # 方法1: 速度最大值方法
    start_idx_vel, end_idx_vel, confidence_vel, pattern_vel = detector.detect_single_motion_event(
        acc_data, only_acceleration_phase=True, use_integration=True, detection_method='velocity'
    )
    
    duration_vel = (end_idx_vel - start_idx_vel) / sample_rate
    print(f"检测结果: 起始索引={start_idx_vel}, 结束索引={end_idx_vel}")
    print(f"持续时间: {duration_vel:.3f} 秒")
    print(f"置信度: {confidence_vel:.3f}")
    print(f"模式: {pattern_vel}")
    
    print("\n2. 使用位移最大值方法检测加速阶段:")
    print("-" * 50)
    
    # 方法2: 位移最大值方法
    start_idx_disp, end_idx_disp, confidence_disp, pattern_disp = detector.detect_single_motion_event(
        acc_data, only_acceleration_phase=True, use_integration=True, detection_method='displacement'
    )
    
    duration_disp = (end_idx_disp - start_idx_disp) / sample_rate
    print(f"检测结果: 起始索引={start_idx_disp}, 结束索引={end_idx_disp}")
    print(f"持续时间: {duration_disp:.3f} 秒")
    print(f"置信度: {confidence_disp:.3f}")
    print(f"模式: {pattern_disp}")
    
    print("\n3. 方法对比:")
    print("-" * 50)
    print(f"速度方法检测的加速阶段: {duration_vel:.3f} 秒")
    print(f"位移方法检测的加速阶段: {duration_disp:.3f} 秒")
    print(f"差异: {abs(duration_vel - duration_disp):.3f} 秒")
    
    # 可视化对比
    create_comparison_visualization(acc_data, time_axis, 
                                  start_idx_vel, end_idx_vel,
                                  start_idx_disp, end_idx_disp,
                                  detector, sample_rate)
    
    return acc_data, detector

def create_comparison_visualization(acc_data, time_axis, 
                                  start_vel, end_vel, start_disp, end_disp,
                                  detector, sample_rate):
    """
    创建对比可视化图
    """
    plt.figure(figsize=(15, 12))
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    main_axis = detector.last_main_axis
    main_axis_data = processed_data[:, main_axis]
    
    # 平滑数据
    window = 5
    smoothed_data = np.convolve(main_axis_data, np.ones(window)/window, mode='same')
    
    # 计算积分
    dt = 1.0 / sample_rate
    
    # 速度方法的积分
    if start_vel < len(smoothed_data):
        velocity_vel = np.cumsum(smoothed_data[start_vel:]) * dt
        displacement_vel = np.cumsum(velocity_vel) * dt
    else:
        velocity_vel = np.array([])
        displacement_vel = np.array([])
    
    # 位移方法的积分
    if start_disp < len(smoothed_data):
        velocity_disp = np.cumsum(smoothed_data[start_disp:]) * dt
        displacement_disp = np.cumsum(velocity_disp) * dt
    else:
        velocity_disp = np.array([])
        displacement_disp = np.array([])
    
    # 子图1: 原始加速度
    plt.subplot(4, 1, 1)
    axis_name = 'X' if main_axis == 0 else 'Z'
    plt.plot(time_axis, acc_data[:, main_axis], 'b-', label=f'{axis_name}轴加速度')
    plt.axvspan(start_vel/sample_rate, end_vel/sample_rate, color='red', alpha=0.3, label='速度方法检测区域')
    plt.axvspan(start_disp/sample_rate, end_disp/sample_rate, color='green', alpha=0.3, label='位移方法检测区域')
    plt.title('原始加速度数据对比')
    plt.ylabel('加速度 (m/s²)')
    plt.legend()
    plt.grid(True)
    
    # 子图2: 速度对比
    plt.subplot(4, 1, 2)
    if len(velocity_vel) > 0:
        time_vel = np.arange(start_vel, start_vel + len(velocity_vel)) / sample_rate
        plt.plot(time_vel, velocity_vel, 'r-', label='速度方法积分速度', linewidth=2)
        # 标记速度最大值点
        max_vel_idx = np.argmax(np.abs(velocity_vel))
        plt.plot(time_vel[max_vel_idx], velocity_vel[max_vel_idx], 'ro', markersize=8, label='速度峰值')
        plt.axvline(x=end_vel/sample_rate, color='red', linestyle='--', label='速度方法边界')
    
    if len(velocity_disp) > 0:
        time_disp = np.arange(start_disp, start_disp + len(velocity_disp)) / sample_rate
        plt.plot(time_disp, velocity_disp, 'g--', label='位移方法积分速度', linewidth=2)
        plt.axvline(x=end_disp/sample_rate, color='green', linestyle='--', label='位移方法边界')
    
    plt.title('速度对比')
    plt.ylabel('速度 (m/s)')
    plt.legend()
    plt.grid(True)
    
    # 子图3: 位移对比
    plt.subplot(4, 1, 3)
    if len(displacement_vel) > 0:
        plt.plot(time_vel, displacement_vel, 'r-', label='速度方法积分位移', linewidth=2)
    
    if len(displacement_disp) > 0:
        plt.plot(time_disp, displacement_disp, 'g--', label='位移方法积分位移', linewidth=2)
        # 标记位移最大值点
        if np.max(displacement_disp) > abs(np.min(displacement_disp)):
            max_disp_idx = np.argmax(displacement_disp)
        else:
            max_disp_idx = np.argmin(displacement_disp)
        plt.plot(time_disp[max_disp_idx], displacement_disp[max_disp_idx], 'go', markersize=8, label='位移极值')
        plt.axvline(x=end_disp/sample_rate, color='green', linestyle='--', label='位移方法边界')
    
    plt.title('位移对比')
    plt.ylabel('位移 (m)')
    plt.legend()
    plt.grid(True)
    
    # 子图4: 检测结果总结
    plt.subplot(4, 1, 4)
    plt.text(0.1, 0.8, f'速度方法检测结果:', fontsize=12, fontweight='bold', color='red')
    plt.text(0.1, 0.7, f'  加速阶段: {start_vel/sample_rate:.3f}s - {end_vel/sample_rate:.3f}s', fontsize=10)
    plt.text(0.1, 0.6, f'  持续时间: {(end_vel-start_vel)/sample_rate:.3f}s', fontsize=10)
    
    plt.text(0.1, 0.4, f'位移方法检测结果:', fontsize=12, fontweight='bold', color='green')
    plt.text(0.1, 0.3, f'  加速阶段: {start_disp/sample_rate:.3f}s - {end_disp/sample_rate:.3f}s', fontsize=10)
    plt.text(0.1, 0.2, f'  持续时间: {(end_disp-start_disp)/sample_rate:.3f}s', fontsize=10)
    
    plt.text(0.1, 0.05, f'差异: {abs((end_vel-start_vel)-(end_disp-start_disp))/sample_rate:.3f}s', 
             fontsize=10, fontweight='bold')
    
    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')
    plt.title('检测结果对比总结')
    
    plt.tight_layout()
    plt.savefig('detection_methods_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n对比图已保存为: detection_methods_comparison.png")

def test_with_real_data():
    """
    使用真实数据测试两种方法
    """
    print("\n=== 使用真实数据测试 ===")
    
    # 查找CSV文件
    current_dir = os.getcwd()
    csv_files = [f for f in os.listdir(current_dir) if f.endswith('.csv')]
    
    if not csv_files:
        print("未找到CSV文件，跳过真实数据测试")
        return
    
    # 使用第一个找到的CSV文件
    test_file = csv_files[0]
    print(f"使用文件: {test_file}")
    
    print("\n1. 速度方法检测:")
    result_vel = process_file(test_file, visualize=True, only_acceleration=True, detection_method='velocity')
    
    print("\n2. 位移方法检测:")
    result_disp = process_file(test_file, visualize=True, only_acceleration=True, detection_method='displacement')
    
    print("\n3. 结果对比:")
    print(f"速度方法持续时间: {result_vel['duration']:.3f}s")
    print(f"位移方法持续时间: {result_disp['duration']:.3f}s")
    print(f"差异: {abs(result_vel['duration'] - result_disp['duration']):.3f}s")

if __name__ == "__main__":
    print("新的积分检测方法测试")
    print("=" * 60)
    
    # 1. 使用合成数据比较两种方法
    acc_data, detector = compare_detection_methods()
    
    # 2. 使用真实数据测试
    test_with_real_data()
    
    print("\n测试完成！")
    print("主要改进:")
    print("1. 不再依赖加速度零点检测")
    print("2. 通过积分计算速度和位移")
    print("3. 支持两种检测方法:")
    print("   - 速度最大值方法：避免减速阶段的反向速度峰值")
    print("   - 位移最大值方法：基于位移极值点检测")
    print("4. 智能识别运动方向，避免误检测")
