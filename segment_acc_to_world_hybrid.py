# -*- coding:utf-8 -*-
"""
作者：何志想
日期：2025年07月16日
功能：混合算法版本 - 静态初始化+动态Mahony算法进行姿态估计和坐标系转换
特点：在静止状态使用静态方法建立初始坐标系，在运动过程中使用Mahony算法动态更新
依赖：pip install ahrs
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import re
import glob

# 导入AHRS库
try:
    from ahrs.filters import Mahony
    from ahrs.common import Quaternion
    AHRS_AVAILABLE = True
    print("成功导入AHRS库")
except ImportError as e:
    print(f"警告: 无法导入AHRS库: {e}")
    print("请安装AHRS库: pip install ahrs")
    AHRS_AVAILABLE = False

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 


def check_ahrs_dependency():
    """检查AHRS库依赖"""
    if not AHRS_AVAILABLE:
        print("错误: AHRS库未安装或导入失败")
        print("请运行以下命令安装: pip install ahrs")
        return False
    return True


def load_segment_files(segment_folder, segment_number=None):
    """
    加载特定片段的所有传感器数据文件
    
    Args:
        segment_folder: 存放片段数据的文件夹路径
        segment_number: 指定片段编号，如果为None则加载所有片段
        
    Returns:
        字典，包含每个片段的数据，格式为 {片段编号: {'acce': df_acc, 'grav': df_grav, 'mag': df_mag, 'gyro': df_gyro}}
    """
    # 查找所有片段文件
    all_files = glob.glob(os.path.join(segment_folder, "segment*_*.csv"))
    
    segment_data = {}
    # 正则表达式匹配文件名 - 格式为 segment数字_传感器类型_时间戳.csv
    pattern = r"segment(\d+)_(acce|grav|mag|gyro)_(\d+-\d+-\d+\.\d+)\.csv"
    
    for file_path in all_files:
        file_name = os.path.basename(file_path)
        match = re.match(pattern, file_name)
        
        if match:
            seg_num = int(match.group(1))
            sensor_type = match.group(2)
            timestamp = match.group(3)
            
            # 如果指定了片段编号，只处理该片段
            if segment_number is not None and seg_num != segment_number:
                continue
            
            # 读取数据
            try:
                df = pd.read_csv(file_path)
                # 保存文件路径到DataFrame的元数据中，方便后续引用
                df.name = file_path
                
                # 初始化字典
                if seg_num not in segment_data:
                    segment_data[seg_num] = {}
                
                # 保存数据
                segment_data[seg_num][sensor_type] = df
                
                print(f"加载文件: {file_name}, 形状: {df.shape}")
            except Exception as e:
                print(f"加载文件 {file_name} 错误: {e}")
    
    return segment_data


def time_to_seconds(time_str):
    """
    将HH-MM-SS.mmm格式转换为秒数
    
    Args:
        time_str: 时间字符串，格式为HH-MM-SS.mmm
        
    Returns:
        浮点数表示的秒数
    """
    if not isinstance(time_str, str):
        print(f"警告: 非字符串时间数据: {time_str}，类型: {type(time_str)}")
        return 0
    
    try:
        time_str = time_str.strip()
        if '.' in time_str:
            dt = datetime.strptime(time_str, '%H-%M-%S.%f')
        else:
            dt = datetime.strptime(time_str, '%H-%M-%S')
        return dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1e6
    except ValueError as e:
        print(f"无法解析时间字符串: {time_str}，错误: {e}")
        return 0


def preprocess_time_data(df):
    """预处理时间数据，将时间字符串转换为浮点数秒"""
    if 'time' in df.columns:
        print(f"数据列: {list(df.columns)}")
        print(f"数据示例:\n{df.head(2)}")
        df['time_seconds'] = df['time'].apply(time_to_seconds)
    return df


def smooth_data(data, window_size=10):
    """
    使用滑动窗口平均对数据进行平滑处理
    
    Args:
        data: 需要平滑的数据数组
        window_size: 滑动窗口大小
        
    Returns:
        平滑后的数据数组
    """
    if len(data) <= window_size:
        return data
    
    smoothed = np.zeros_like(data)
    for i in range(len(data)):
        start = max(0, i - window_size//2)
        end = min(len(data), i + window_size//2 + 1)
        smoothed[i] = np.mean(data[start:end], axis=0)
    
    return smoothed


def synchronize_sensor_data(acc_df, grav_df, mag_df, gyro_df=None):
    """
    同步所有传感器数据到统一的时间基准
    
    Args:
        acc_df: 加速度数据
        grav_df: 重力数据
        mag_df: 磁力计数据
        gyro_df: 陀螺仪数据（可选）
        
    Returns:
        同步后的传感器数据字典
    """
    # 使用加速度数据的时间戳作为参考
    timestamps = acc_df['time_seconds'].values
    
    synchronized_data = {
        'time': acc_df['time'].values,
        'time_seconds': timestamps,
        'accel': np.zeros((len(timestamps), 3)),
        'mag': np.zeros((len(timestamps), 3)),
        'grav': np.zeros((len(timestamps), 3))
    }
    
    if gyro_df is not None:
        synchronized_data['gyro'] = np.zeros((len(timestamps), 3))
    
    # 同步每个时间点的数据
    for i, time_sec in enumerate(timestamps):
        # 加速度数据（已经对齐）
        synchronized_data['accel'][i] = [acc_df.iloc[i]['x'], acc_df.iloc[i]['y'], acc_df.iloc[i]['z']]
        
        # 寻找最接近的重力数据
        if 'sensor_timestamp' in acc_df.columns and 'sensor_timestamp' in grav_df.columns:
            acc_sensor_time = acc_df.iloc[i]['sensor_timestamp']
            grav_diffs = np.abs(grav_df['sensor_timestamp'].values - acc_sensor_time)
        else:
            grav_diffs = np.abs(grav_df['time_seconds'].values - time_sec)
        grav_idx = np.argmin(grav_diffs)
        synchronized_data['grav'][i] = [grav_df.iloc[grav_idx]['x'], grav_df.iloc[grav_idx]['y'], grav_df.iloc[grav_idx]['z']]
        
        # 寻找最接近的磁力计数据
        if 'sensor_timestamp' in acc_df.columns and 'sensor_timestamp' in mag_df.columns:
            acc_sensor_time = acc_df.iloc[i]['sensor_timestamp']
            mag_diffs = np.abs(mag_df['sensor_timestamp'].values - acc_sensor_time)
        else:
            mag_diffs = np.abs(mag_df['time_seconds'].values - time_sec)
        mag_idx = np.argmin(mag_diffs)
        synchronized_data['mag'][i] = [mag_df.iloc[mag_idx]['x'], mag_df.iloc[mag_idx]['y'], mag_df.iloc[mag_idx]['z']]
        
        # 同步陀螺仪数据（如果存在）
        if gyro_df is not None:
            if 'sensor_timestamp' in acc_df.columns and 'sensor_timestamp' in gyro_df.columns:
                acc_sensor_time = acc_df.iloc[i]['sensor_timestamp']
                gyro_diffs = np.abs(gyro_df['sensor_timestamp'].values - acc_sensor_time)
            else:
                gyro_diffs = np.abs(gyro_df['time_seconds'].values - time_sec)
            gyro_idx = np.argmin(gyro_diffs)
            synchronized_data['gyro'][i] = [gyro_df.iloc[gyro_idx]['x'], gyro_df.iloc[gyro_idx]['y'], gyro_df.iloc[gyro_idx]['z']]
    
    return synchronized_data


def detect_motion_phases(sync_data, static_threshold=0.5, static_duration=5):
    """
    检测运动的静止阶段和运动阶段
    
    Args:
        sync_data: 同步的传感器数据
        static_threshold: 静止判断的加速度阈值 (m/s²)
        static_duration: 静止状态的最小持续帧数
        
    Returns:
        dict: 包含静止和运动阶段的字典
    """
    accel_data = sync_data['accel']
    n_samples = len(accel_data)
    
    # 计算加速度变化幅度（去除重力）
    accel_magnitude = np.linalg.norm(accel_data, axis=1)
    gravity_est = np.mean(accel_magnitude[:min(20, n_samples)])  # 使用前20帧估计重力
    
    # 计算去重力后的加速度变化
    accel_change = np.abs(accel_magnitude - gravity_est)
    
    # 标识静止状态
    is_static = accel_change < static_threshold
    
    # 寻找连续的静止段
    static_segments = []
    start_idx = None
    
    for i, static in enumerate(is_static):
        if static and start_idx is None:
            start_idx = i
        elif not static and start_idx is not None:
            if i - start_idx >= static_duration:
                static_segments.append((start_idx, i-1))
            start_idx = None
    
    # 处理末尾的静止段
    if start_idx is not None and n_samples - start_idx >= static_duration:
        static_segments.append((start_idx, n_samples-1))
    
    print(f"检测到 {len(static_segments)} 个静止段")
    for i, (start, end) in enumerate(static_segments):
        duration = (end - start + 1) / 100  # 假设100Hz采样率
        print(f"  静止段 {i+1}: 帧 {start}-{end} (持续 {duration:.2f}秒)")
    
    return {
        'static_segments': static_segments,
        'is_static': is_static,
        'accel_change': accel_change,
        'gravity_estimate': gravity_est
    }


def build_initial_coordinate_system(sync_data, static_segment):
    """
    使用静态方法在静止段建立初始世界坐标系
    
    Args:
        sync_data: 同步的传感器数据
        static_segment: 静止段的索引范围 (start, end)
        
    Returns:
        tuple: (初始旋转矩阵, 初始四元数)
    """
    start_idx, end_idx = static_segment
    
    # 使用静止段的平均值
    gravity_mean = np.mean(sync_data['grav'][start_idx:end_idx+1], axis=0)
    magnetic_mean = np.mean(sync_data['mag'][start_idx:end_idx+1], axis=0)
    
    print(f"使用静止段 {start_idx}-{end_idx} 建立初始坐标系")
    print(f"重力平均值: {gravity_mean}")
    print(f"磁场平均值: {magnetic_mean}")
    
    # 构建世界坐标系（方法3）
    up = gravity_mean / np.linalg.norm(gravity_mean) if np.linalg.norm(gravity_mean) > 0 else np.array([0, 0, 1])
    east = np.cross(magnetic_mean, up)
    east = east / np.linalg.norm(east) if np.linalg.norm(east) > 0 else np.array([1, 0, 0])
    north = np.cross(up, east)
    north = north / np.linalg.norm(north) if np.linalg.norm(north) > 0 else np.array([0, 1, 0])
    
    # 初始旋转矩阵 (从设备坐标系到世界坐标系)
    initial_rotation_matrix = np.array([east, north, up])
    
    print(f"初始坐标系:")
    print(f"  东向 (East): {east}")
    print(f"  北向 (North): {north}")
    print(f"  上向 (Up): {up}")
    
    # 将旋转矩阵转换为四元数
    initial_quaternion = rotation_matrix_to_quaternion(initial_rotation_matrix.T)  # 转置因为我们需要从世界到设备的变换
    
    print(f"初始四元数: {initial_quaternion}")
    
    return initial_rotation_matrix, initial_quaternion


def rotation_matrix_to_quaternion(R):
    """
    将旋转矩阵转换为四元数 [w, x, y, z]
    
    Args:
        R: 3x3旋转矩阵
        
    Returns:
        np.array: 四元数 [w, x, y, z]
    """
    trace = np.trace(R)
    
    if trace > 0:
        S = np.sqrt(trace + 1.0) * 2  # S=4*qw 
        qw = 0.25 * S
        qx = (R[2, 1] - R[1, 2]) / S
        qy = (R[0, 2] - R[2, 0]) / S
        qz = (R[1, 0] - R[0, 1]) / S
    elif R[0, 0] > R[1, 1] and R[0, 0] > R[2, 2]:
        S = np.sqrt(1.0 + R[0, 0] - R[1, 1] - R[2, 2]) * 2  # S=4*qx
        qw = (R[2, 1] - R[1, 2]) / S
        qx = 0.25 * S
        qy = (R[0, 1] + R[1, 0]) / S
        qz = (R[0, 2] + R[2, 0]) / S
    elif R[1, 1] > R[2, 2]:
        S = np.sqrt(1.0 + R[1, 1] - R[0, 0] - R[2, 2]) * 2  # S=4*qy
        qw = (R[0, 2] - R[2, 0]) / S
        qx = (R[0, 1] + R[1, 0]) / S
        qy = 0.25 * S
        qz = (R[1, 2] + R[2, 1]) / S
    else:
        S = np.sqrt(1.0 + R[2, 2] - R[0, 0] - R[1, 1]) * 2  # S=4*qz
        qw = (R[1, 0] - R[0, 1]) / S
        qx = (R[0, 2] + R[2, 0]) / S
        qy = (R[1, 2] + R[2, 1]) / S
        qz = 0.25 * S
    
    return np.array([qw, qx, qy, qz])


def process_segment_data_hybrid(segment_data, segment_num, output_folder, 
                               window_size=10, sample_rate=100.0,
                               kp=1.0, ki=0.3, static_threshold=0.5, static_duration=5):
    """
    使用混合算法处理一个片段的数据 - 静态初始化+动态Mahony更新
    特点：使用重力计数据(grav)作为Mahony算法的加速度输入，用计算的四元数转换加速度数据(acce)
    
    Args:
        segment_data: 片段数据字典
        segment_num: 片段编号
        output_folder: 输出文件夹
        window_size: 平滑窗口大小
        sample_rate: 采样率 (Hz)
        kp: Mahony算法的kp参数
        ki: Mahony算法的ki参数
        static_threshold: 静止判断阈值
        static_duration: 静止状态最小持续帧数
        
    Returns:
        转换后的加速度数据DataFrame
    """
    if not check_ahrs_dependency():
        print(f"AHRS库不可用，无法使用混合算法")
        return None
    
    segment_info = segment_data[segment_num]
    
    # 检查必要的数据是否存在
    required_sensors = ['acce', 'grav', 'mag']
    if 'gyro' in segment_info:
        required_sensors.append('gyro')
    
    missing_sensors = [sensor for sensor in required_sensors if sensor not in segment_info]
    if missing_sensors:
        print(f"片段 {segment_num} 缺少必要的数据: {missing_sensors}")
        if 'gyro' in missing_sensors:
            print(f"警告: 缺少陀螺仪数据，将使用仅加速度计+磁力计模式")
        elif len(missing_sensors) > 1 or 'acce' in missing_sensors:
            return None
    
    # 获取传感器数据
    acc_df = segment_info['acce'].copy()
    grav_df = segment_info['grav'].copy()
    mag_df = segment_info['mag'].copy()
    gyro_df = segment_info.get('gyro', None)
    if gyro_df is not None:
        gyro_df = gyro_df.copy()
    
    print(f"\n处理片段 {segment_num} - 使用改进混合算法 (实时静态转换+Mahony动态更新)")
    print(f"策略1: 静态段使用每时刻的实时重力+磁力数据计算坐标系转换")
    print(f"策略2: 动态段使用重力数据(grav)进行Mahony姿态估计，转换加速度数据(acce)")
    print(f"加速度数据: {acc_df.shape}")
    print(f"重力数据: {grav_df.shape}")
    print(f"磁力计数据: {mag_df.shape}")
    if gyro_df is not None:
        print(f"陀螺仪数据: {gyro_df.shape}")
    
    # 预处理时间数据
    acc_df = preprocess_time_data(acc_df)
    grav_df = preprocess_time_data(grav_df)
    mag_df = preprocess_time_data(mag_df)
    if gyro_df is not None:
        gyro_df = preprocess_time_data(gyro_df)
    
    # 同步传感器数据
    sync_data = synchronize_sensor_data(acc_df, grav_df, mag_df, gyro_df)
    
    # 估计采样率
    if len(sync_data['time_seconds']) > 1:
        time_diff = sync_data['time_seconds'][-1] - sync_data['time_seconds'][0]
        estimated_rate = len(sync_data['time_seconds']) / time_diff
        sample_rate = min(sample_rate, estimated_rate)
    
    print(f"使用采样率: {sample_rate:.1f} Hz")
    
    # 检测运动阶段（基于实际加速度数据进行检测）
    motion_phases = detect_motion_phases(sync_data, static_threshold, static_duration)
    
    if not motion_phases['static_segments']:
        print("警告: 未检测到静止段，将使用前几帧作为静态段")
        init_frames = min(10, len(sync_data['accel']))
        static_segments = [(0, init_frames-1)]
    else:
        static_segments = motion_phases['static_segments']
    
    # 建立初始坐标系
    initial_rotation_matrix, initial_quaternion = build_initial_coordinate_system(sync_data, static_segments[0])
    
    # 准备数据用于Mahony算法
    # 关键修改：使用重力数据作为Mahony算法的加速度输入，而不是实际加速度数据
    n_samples = len(sync_data['accel'])
    gravity_data = sync_data['grav']  # 使用重力数据进行姿态估计
    actual_accel_data = sync_data['accel']  # 实际要转换的加速度数据
    mag_data = sync_data['mag']
    
    print(f"使用重力数据形状: {gravity_data.shape} 进行姿态估计")
    print(f"转换加速度数据形状: {actual_accel_data.shape}")
    
    if gyro_df is not None:
        gyro_data = sync_data['gyro'] * np.pi / 180  # 转换为弧度
        print("使用陀螺仪+重力计+磁力计模式进行姿态估计")
    else:
        gyro_data = np.zeros_like(gravity_data)
        print("使用重力计+磁力计模式进行姿态估计 (无陀螺仪)")
    
    # 创建静态段标记数组
    is_static_array = np.zeros(n_samples, dtype=bool)
    for static_start, static_end in static_segments:
        is_static_array[static_start:static_end+1] = True
    
    print(f"静态段: {static_segments}")
    print(f"静态帧数: {np.sum(is_static_array)}, 动态帧数: {n_samples - np.sum(is_static_array)}")
    
    # 初始化结果存储
    acc_world_all = []
    quaternions_all = []
    motion_phase_labels = []
    
    i = 0
    while i < n_samples:
        if is_static_array[i]:
            # 静态段：使用当前时刻的重力和磁力数据实时计算坐标系转换
            current_gravity = gravity_data[i]
            current_magnetic = mag_data[i]
            current_accel = actual_accel_data[i]
            
            # 实时构建世界坐标系（基于当前时刻的传感器数据）
            up = current_gravity / np.linalg.norm(current_gravity) if np.linalg.norm(current_gravity) > 0 else np.array([0, 0, 1])
            east = np.cross(current_magnetic, up)
            east = east / np.linalg.norm(east) if np.linalg.norm(east) > 0 else np.array([1, 0, 0])
            north = np.cross(up, east)
            north = north / np.linalg.norm(north) if np.linalg.norm(north) > 0 else np.array([0, 1, 0])
            
            # 当前时刻的旋转矩阵 (从设备坐标系到世界坐标系)
            current_rotation_matrix = np.array([east, north, up])
            
            # 直接应用旋转矩阵到当前加速度数据
            acc_world = np.dot(current_rotation_matrix, current_accel)
            acc_world_all.append(acc_world)
            
            # 将旋转矩阵转换为四元数保存
            current_quaternion = rotation_matrix_to_quaternion(current_rotation_matrix.T)
            quaternions_all.append(current_quaternion)
            motion_phase_labels.append('static')
            
            i += 1
            
        else:
            # 动态段：找到连续的动态段范围
            dynamic_start = i
            dynamic_end = i
            while dynamic_end < n_samples and not is_static_array[dynamic_end]:
                dynamic_end += 1
            dynamic_end -= 1  # 调整到最后一个动态帧
            
            print(f"处理动态段: 第 {dynamic_start} - {dynamic_end} 帧 (共 {dynamic_end - dynamic_start + 1} 帧)")
            
            # 确定初始四元数
            if quaternions_all:
                initial_quaternion_for_dynamic = quaternions_all[-1].copy()
                print(f"使用前一帧的四元数作为动态段初始姿态")
            else:
                # 使用第一个静态段的平均值作为初始化
                initial_quaternion_for_dynamic = initial_quaternion.copy()
                print(f"使用静态方法计算的初始四元数作为动态段初始姿态")
            
            # 准备动态段数据
            dynamic_gravity = gravity_data[dynamic_start:dynamic_end+1]
            dynamic_actual_accel = actual_accel_data[dynamic_start:dynamic_end+1]
            dynamic_mag = mag_data[dynamic_start:dynamic_end+1]
            dynamic_gyro = gyro_data[dynamic_start:dynamic_end+1]
            
            try:
                # 使用Mahony算法处理动态段
                mahony = Mahony(gyr=dynamic_gyro, acc=dynamic_gravity, mag=dynamic_mag, 
                               frequency=sample_rate, k_P=kp, k_I=ki, q0=initial_quaternion_for_dynamic)
                
                dynamic_quaternions = mahony.Q
                print(f"Mahony算法基于重力数据生成 {len(dynamic_quaternions)} 个四元数")
                
                # 应用四元数到实际加速度数据
                for j, q in enumerate(dynamic_quaternions):
                    if dynamic_start + j <= dynamic_end:
                        quat = Quaternion(q)
                        rotation_matrix = quat.to_DCM()
                        acc_world = np.dot(rotation_matrix, dynamic_actual_accel[j])
                        acc_world_all.append(acc_world)
                        quaternions_all.append(q)
                        motion_phase_labels.append('dynamic')
                
            except Exception as e:
                print(f"Mahony算法处理失败，使用静态方法回退: {e}")
                # 回退到静态方法
                for j in range(len(dynamic_gravity)):
                    current_gravity = dynamic_gravity[j]
                    current_magnetic = dynamic_mag[j]
                    current_accel = dynamic_actual_accel[j]
                    
                    up = current_gravity / np.linalg.norm(current_gravity) if np.linalg.norm(current_gravity) > 0 else np.array([0, 0, 1])
                    east = np.cross(current_magnetic, up)
                    east = east / np.linalg.norm(east) if np.linalg.norm(east) > 0 else np.array([1, 0, 0])
                    north = np.cross(up, east)
                    north = north / np.linalg.norm(north) if np.linalg.norm(north) > 0 else np.array([0, 1, 0])
                    
                    current_rotation_matrix = np.array([east, north, up])
                    acc_world = np.dot(current_rotation_matrix, current_accel)
                    acc_world_all.append(acc_world)
                    
                    current_quaternion = rotation_matrix_to_quaternion(current_rotation_matrix.T)
                    quaternions_all.append(current_quaternion)
                    motion_phase_labels.append('dynamic_fallback')
            
            # 跳过已处理的动态段
            i = dynamic_end + 1
    
    # 转换为numpy数组
    acc_world_all = np.array(acc_world_all)
    quaternions_all = np.array(quaternions_all)
    
    print(f"最终处理了 {len(acc_world_all)} 帧数据")
    
    # 应用平滑滤波
    if len(acc_world_all) > window_size:
        acc_world_all = smooth_data(acc_world_all, window_size)
    
    # 创建结果DataFrame
    result_df = pd.DataFrame()
    result_df['time'] = sync_data['time'][:len(acc_world_all)]
    result_df['x'] = acc_world_all[:, 0]  # 东方向
    result_df['y'] = acc_world_all[:, 1]  # 北方向
    result_df['z'] = acc_world_all[:, 2]  # 上方向
    
    # 添加原始加速度数据（实际要转换的加速度数据acce）
    result_df['x_raw'] = actual_accel_data[:len(acc_world_all), 0]
    result_df['y_raw'] = actual_accel_data[:len(acc_world_all), 1]
    result_df['z_raw'] = actual_accel_data[:len(acc_world_all), 2]
    
    # 添加重力数据（用于姿态估计的数据grav）
    result_df['x_grav'] = gravity_data[:len(acc_world_all), 0]
    result_df['y_grav'] = gravity_data[:len(acc_world_all), 1]
    result_df['z_grav'] = gravity_data[:len(acc_world_all), 2]
    
    # 添加四元数信息（基于重力数据计算得出）
    result_df['qw'] = quaternions_all[:, 0]
    result_df['qx'] = quaternions_all[:, 1]
    result_df['qy'] = quaternions_all[:, 2]
    result_df['qz'] = quaternions_all[:, 3]
    
    # 添加运动阶段标记
    result_df['motion_phase'] = motion_phase_labels[:len(acc_world_all)]
    
    # 生成输出文件名
    acc_filename = os.path.basename(acc_df.name) if hasattr(acc_df, 'name') else ""
    match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
    
    if match:
        timestamp = match.group(1)
    else:
        if 'time' in acc_df.columns and len(acc_df) > 0:
            first_time = acc_df['time'].iloc[0]
            timestamp = first_time.replace(':', '-') if isinstance(first_time, str) else datetime.now().strftime('%H-%M-%S.%f')[:-3]
        else:
            timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
    
    # 保存结果
    output_file = os.path.join(output_folder, f'segment{segment_num}_hybrid_world_acce_{timestamp}.csv')
    result_df.to_csv(output_file, index=False)
    print(f"已保存世界坐标系加速度数据: {output_file}")
    
    # 保存四元数数据
    quat_output_file = os.path.join(output_folder, f'segment{segment_num}_hybrid_quaternions_{timestamp}.csv')
    quat_df = pd.DataFrame({
        'time': sync_data['time'][:len(quaternions_all)],
        'qw': quaternions_all[:, 0],
        'qx': quaternions_all[:, 1],
        'qy': quaternions_all[:, 2],
        'qz': quaternions_all[:, 3],
        'motion_phase': result_df['motion_phase']
    })
    quat_df.to_csv(quat_output_file, index=False)
    print(f"已保存四元数数据: {quat_output_file}")
    
    # 保存处理摘要信息
    summary_file = os.path.join(output_folder, f'segment{segment_num}_hybrid_summary_{timestamp}.txt')
    static_count = sum(1 for label in motion_phase_labels if label == 'static')
    dynamic_count = sum(1 for label in motion_phase_labels if label == 'dynamic')
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("混合算法处理摘要\n")
        f.write("=" * 40 + "\n")
        f.write(f"片段编号: {segment_num}\n")
        f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"数据长度: {len(acc_world_all)} 帧\n")
        f.write(f"算法策略: 静态段实时计算，动态段Mahony算法\n")
        f.write(f"静态段: {static_count} 帧\n")
        f.write(f"动态段: {dynamic_count} 帧\n")
        f.write(f"姿态估计输入: 重力数据 (grav)\n")
        f.write(f"坐标转换目标: 加速度数据 (acce)\n")
        f.write(f"Mahony参数: kp={kp}, ki={ki}\n")
        f.write(f"采样率: {sample_rate:.1f} Hz\n")
    print(f"已保存处理摘要: {summary_file}")
    
    return result_df


def visualize_acceleration_hybrid(segment_data, segment_num, world_acc_df, output_folder):
    """
    可视化混合算法的结果，包含运动阶段分析和重力/加速度数据对比
    
    Args:
        segment_data: 片段数据字典
        segment_num: 片段编号
        world_acc_df: 世界坐标系加速度数据
        output_folder: 输出文件夹
    """
    if segment_num not in segment_data or 'acce' not in segment_data[segment_num]:
        print(f"片段 {segment_num} 没有加速度数据，无法可视化")
        return
    
    # 创建时间轴
    time_seconds = np.arange(len(world_acc_df)) / 100  # 假设采样率为100Hz
    
    # 创建可视化
    fig, axes = plt.subplots(4, 2, figsize=(15, 16))
    
    # 运动阶段标记
    static_mask = world_acc_df['motion_phase'] == 'static'
    dynamic_mask = world_acc_df['motion_phase'] == 'dynamic'
    
    # 1. 原始加速度数据（acce，实际转换的数据）
    axes[0, 0].plot(time_seconds, world_acc_df['x_raw'], 'r-', label='Acce-X轴', alpha=0.7)
    axes[0, 0].plot(time_seconds, world_acc_df['y_raw'], 'g-', label='Acce-Y轴', alpha=0.7)
    axes[0, 0].plot(time_seconds, world_acc_df['z_raw'], 'b-', label='Acce-Z轴', alpha=0.7)
    axes[0, 0].axvspan(time_seconds[static_mask].min(), time_seconds[static_mask].max(), 
                      alpha=0.2, color='gray', label='静止段')
    axes[0, 0].set_title(f'片段 {segment_num} - 加速度数据 (被转换的数据)')
    axes[0, 0].set_xlabel('时间 (秒)')
    axes[0, 0].set_ylabel('加速度 (m/s²)')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].legend()
    
    # 2. 重力数据（grav，用于姿态估计的数据）
    if 'x_grav' in world_acc_df.columns:
        axes[0, 1].plot(time_seconds, world_acc_df['x_grav'], 'r--', label='Grav-X轴', alpha=0.7)
        axes[0, 1].plot(time_seconds, world_acc_df['y_grav'], 'g--', label='Grav-Y轴', alpha=0.7)
        axes[0, 1].plot(time_seconds, world_acc_df['z_grav'], 'b--', label='Grav-Z轴', alpha=0.7)
        axes[0, 1].axvspan(time_seconds[static_mask].min(), time_seconds[static_mask].max(), 
                          alpha=0.2, color='gray', label='静态段')
        axes[0, 1].set_title('重力数据 (用于姿态估计)')
        axes[0, 1].set_xlabel('时间 (秒)')
        axes[0, 1].set_ylabel('重力加速度 (m/s²)')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].legend()
    
    # 3. 世界坐标系加速度（转换结果）
    axes[1, 0].plot(time_seconds, world_acc_df['x'], 'r-', label='东向(East/X轴)', alpha=0.7)
    axes[1, 0].plot(time_seconds, world_acc_df['y'], 'g-', label='北向(North/Y轴)', alpha=0.7)
    axes[1, 0].plot(time_seconds, world_acc_df['z'], 'b-', label='上向(Up/Z轴)', alpha=0.7)
    axes[1, 0].axvspan(time_seconds[static_mask].min(), time_seconds[static_mask].max(), 
                      alpha=0.2, color='gray', label='静态初始化')
    axes[1, 0].set_title('世界坐标系加速度 (混合算法转换结果)')
    axes[1, 0].set_xlabel('时间 (秒)')
    axes[1, 0].set_ylabel('加速度 (m/s²)')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].legend()
    
    # 4. 四元数可视化
    if 'qw' in world_acc_df.columns:
        axes[1, 1].plot(time_seconds, world_acc_df['qw'], 'k-', label='qw (标量)', linewidth=2)
        axes[1, 1].plot(time_seconds, world_acc_df['qx'], 'r-', label='qx', alpha=0.7)
        axes[1, 1].plot(time_seconds, world_acc_df['qy'], 'g-', label='qy', alpha=0.7)
        axes[1, 1].plot(time_seconds, world_acc_df['qz'], 'b-', label='qz', alpha=0.7)
        axes[1, 1].axvspan(time_seconds[static_mask].min(), time_seconds[static_mask].max(), 
                          alpha=0.2, color='gray', label='静态段')
        axes[1, 1].set_title('姿态四元数 (基于重力数据计算)')
        axes[1, 1].set_xlabel('时间 (秒)')
        axes[1, 1].set_ylabel('四元数值')
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].legend()
    
    # 5. 加速度vs重力数据幅值对比
    raw_magnitude = np.sqrt(world_acc_df['x_raw']**2 + world_acc_df['y_raw']**2 + world_acc_df['z_raw']**2)
    if 'x_grav' in world_acc_df.columns:
        grav_magnitude = np.sqrt(world_acc_df['x_grav']**2 + world_acc_df['y_grav']**2 + world_acc_df['z_grav']**2)
        axes[2, 0].plot(time_seconds, raw_magnitude, 'orange', label='加速度数据幅值', alpha=0.7)
        axes[2, 0].plot(time_seconds, grav_magnitude, 'purple', label='重力数据幅值', alpha=0.7)
        axes[2, 0].axvspan(time_seconds[static_mask].min(), time_seconds[static_mask].max(), 
                          alpha=0.2, color='gray', label='静态段')
        axes[2, 0].set_title('加速度 vs 重力数据幅值对比')
        axes[2, 0].set_xlabel('时间 (秒)')
        axes[2, 0].set_ylabel('幅值 (m/s²)')
        axes[2, 0].grid(True, alpha=0.3)
        axes[2, 0].legend()
    
    # 6. 四元数模长验证
    if 'qw' in world_acc_df.columns:
        quat_magnitude = np.sqrt(world_acc_df['qw']**2 + world_acc_df['qx']**2 + 
                                world_acc_df['qy']**2 + world_acc_df['qz']**2)
        axes[2, 1].plot(time_seconds, quat_magnitude, 'purple', linewidth=2)
        axes[2, 1].axhline(y=1.0, color='red', linestyle='--', alpha=0.5, label='理想值=1')
        axes[2, 1].axvspan(time_seconds[static_mask].min(), time_seconds[static_mask].max(), 
                          alpha=0.2, color='gray', label='静态段')
        axes[2, 1].set_title('四元数模长验证 (应接近1)')
        axes[2, 1].set_xlabel('时间 (秒)')
        axes[2, 1].set_ylabel('模长')
        axes[2, 1].grid(True, alpha=0.3)
        axes[2, 1].legend()
        
        # 统计信息
        mean_magnitude = np.mean(quat_magnitude)
        std_magnitude = np.std(quat_magnitude)
        axes[2, 1].text(0.05, 0.95, f'均值: {mean_magnitude:.6f}\n标准差: {std_magnitude:.6f}', 
                       transform=axes[2, 1].transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 6. 重力分离效果
    axes[2, 1].plot(time_seconds, world_acc_df['z'], 'b-', label='Z轴 (上向)', alpha=0.7)
    axes[2, 1].axhline(y=9.8, color='red', linestyle='--', alpha=0.5, label='标准重力 9.8 m/s²')
    axes[2, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axes[2, 1].axvspan(time_seconds[static_mask].min(), time_seconds[static_mask].max(), 
                      alpha=0.2, color='gray', label='静态段')
    axes[2, 1].set_title('重力分离效果 (Z轴)')
    axes[2, 1].set_xlabel('时间 (秒)')
    axes[2, 1].set_ylabel('加速度 (m/s²)')
    axes[2, 1].grid(True, alpha=0.3)
    axes[2, 1].legend()
    
    # Z轴统计信息
    z_mean = np.mean(world_acc_df['z'])
    z_std = np.std(world_acc_df['z'])
    static_z_mean = np.mean(world_acc_df.loc[static_mask, 'z']) if static_mask.any() else 0
    dynamic_z_mean = np.mean(world_acc_df.loc[dynamic_mask, 'z']) if dynamic_mask.any() else 0
    
    axes[2, 1].text(0.05, 0.95, f'总体均值: {z_mean:.3f} m/s²\n总体标准差: {z_std:.3f} m/s²\n静态段均值: {static_z_mean:.3f} m/s²\n动态段均值: {dynamic_z_mean:.3f} m/s²', 
                   transform=axes[2, 1].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 7. 运动阶段分析
    axes[3, 0].plot(time_seconds[static_mask], world_acc_df.loc[static_mask, 'x'], 'ro', 
                   markersize=3, alpha=0.7, label='静态段-东向')
    axes[3, 0].plot(time_seconds[dynamic_mask], world_acc_df.loc[dynamic_mask, 'x'], 'r-', 
                   alpha=0.7, label='动态段-东向')
    axes[3, 0].plot(time_seconds[static_mask], world_acc_df.loc[static_mask, 'y'], 'go', 
                   markersize=3, alpha=0.7, label='静态段-北向')
    axes[3, 0].plot(time_seconds[dynamic_mask], world_acc_df.loc[dynamic_mask, 'y'], 'g-', 
                   alpha=0.7, label='动态段-北向')
    axes[3, 0].set_title('运动阶段分析 (静态vs动态)')
    axes[3, 0].set_xlabel('时间 (秒)')
    axes[3, 0].set_ylabel('加速度 (m/s²)')
    axes[3, 0].grid(True, alpha=0.3)
    axes[3, 0].legend()
    
    # 8. 算法切换点分析
    if dynamic_mask.any():
        switch_point = time_seconds[static_mask].max()
        axes[3, 1].plot(time_seconds, world_acc_df['z'], 'b-', alpha=0.7, label='Z轴加速度')
        axes[3, 1].axvline(x=switch_point, color='red', linestyle='--', linewidth=2, 
                          label=f'算法切换点 ({switch_point:.2f}s)')
        axes[3, 1].set_title('算法切换点分析')
        axes[3, 1].set_xlabel('时间 (秒)')
        axes[3, 1].set_ylabel('Z轴加速度 (m/s²)')
        axes[3, 1].grid(True, alpha=0.3)
        axes[3, 1].legend()
        
        # 添加切换点信息
        axes[3, 1].text(0.05, 0.95, f'切换点: {switch_point:.2f}s\n静态段长度: {static_mask.sum()}帧\n动态段长度: {dynamic_mask.sum()}帧', 
                       transform=axes[3, 1].transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图表
    acc_filename = os.path.basename(segment_data[segment_num]['acce'].name) if hasattr(segment_data[segment_num]['acce'], 'name') else ""
    match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
    
    if match:
        timestamp = match.group(1)
    else:
        timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
    
    output_file = os.path.join(output_folder, f'segment{segment_num}_hybrid_acceleration_analysis_{timestamp}.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"已保存可视化图表: {output_file}")


def calculate_velocity_and_displacement(world_acc_df, dt=0.01):
    """
    计算世界坐标系下的速度和位移
    
    Args:
        world_acc_df: 世界坐标系加速度数据
        dt: 时间步长，单位秒
        
    Returns:
        添加了速度和位移列的DataFrame
    """
    result_df = world_acc_df.copy()
    
    # 初始化速度和位移列
    for direction in ['x', 'y', 'z']:
        result_df[f'vel_{direction}'] = 0.0
        result_df[f'disp_{direction}'] = 0.0
    
    # 计算每个方向上的速度和位移
    for direction in ['x', 'y', 'z']:
        acc = result_df[direction].values
        vel = np.zeros_like(acc)
        disp = np.zeros_like(acc)
        
        for i in range(1, len(acc)):
            vel[i] = vel[i-1] + acc[i] * dt
            disp[i] = disp[i-1] + vel[i] * dt
        
        result_df[f'vel_{direction}'] = vel
        result_df[f'disp_{direction}'] = disp
    
    return result_df


def process_all_segments_hybrid(data_folder, output_folder=None, 
                               calculate_kinematics=False, sample_rate=100.0,
                               kp=1.0, ki=0.3, static_threshold=0.5, static_duration=5):
    """
    使用混合算法处理文件夹中所有片段数据
    
    Args:
        data_folder: 存放片段数据的文件夹路径
        output_folder: 输出文件夹路径
        calculate_kinematics: 是否计算速度和位移
        sample_rate: 采样率 (Hz)
        kp: Mahony算法的kp参数
        ki: Mahony算法的ki参数
        static_threshold: 静止判断阈值
        static_duration: 静止状态最小持续帧数
    """
    if not check_ahrs_dependency():
        print("AHRS库不可用，无法使用混合算法")
        return
    
    if output_folder is None:
        output_folder = os.path.join(data_folder, 'hybrid_world_coordinates')
    
    os.makedirs(output_folder, exist_ok=True)
    
    # 加载所有片段数据
    segment_data = load_segment_files(data_folder)
    
    if not segment_data:
        print(f"在文件夹 {data_folder} 中未找到任何片段数据")
        return
    
    print(f"发现 {len(segment_data)} 个片段，开始使用混合算法处理...")
    print(f"算法参数: kp={kp}, ki={ki}")
    print(f"静止检测参数: threshold={static_threshold}, duration={static_duration}")
    
    # 处理每个片段
    for segment_num in sorted(segment_data.keys()):
        print(f"\n{'='*60}")
        print(f"处理片段 {segment_num}...")
        
        # 处理数据
        world_acc_df = process_segment_data_hybrid(
            segment_data, segment_num, output_folder,
            sample_rate=sample_rate, kp=kp, ki=ki,
            static_threshold=static_threshold, static_duration=static_duration
        )
        
        if world_acc_df is not None:
            # 可视化
            visualize_acceleration_hybrid(
                segment_data, segment_num, world_acc_df, output_folder
            )
            
            # 如果需要，计算速度和位移
            if calculate_kinematics:
                print(f"计算片段 {segment_num} 的速度和位移...")
                
                # 推断采样率
                if len(world_acc_df) > 1:
                    try:
                        time_format = '%H-%M-%S.%f'
                        t1 = datetime.strptime(world_acc_df['time'].iloc[0], time_format)
                        t2 = datetime.strptime(world_acc_df['time'].iloc[-1], time_format)
                        total_time = (t2 - t1).total_seconds()
                        if total_time > 0:
                            sampling_rate = (len(world_acc_df) - 1) / total_time
                            dt = 1.0 / sampling_rate
                        else:
                            dt = 1.0 / sample_rate
                    except:
                        dt = 1.0 / sample_rate
                else:
                    dt = 1.0 / sample_rate
                
                print(f"使用采样时间步长: {dt:.6f}秒 (约 {1/dt:.1f} Hz)")
                
                # 计算速度和位移
                kinematics_df = calculate_velocity_and_displacement(world_acc_df, dt)
                
                # 提取时间戳
                acc_filename = os.path.basename(segment_data[segment_num]['acce'].name)
                match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
                
                if match:
                    timestamp = match.group(1)
                else:
                    timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
                
                # 保存运动学数据
                kinematics_file = os.path.join(output_folder, f'segment{segment_num}_hybrid_kinematics_{timestamp}.csv')
                kinematics_df.to_csv(kinematics_file, index=False)
                print(f"已保存运动学数据: {kinematics_file}")
            
            print(f"片段 {segment_num} 处理完成")
    
    print(f"\n{'='*60}")
    print(f"所有片段处理完成！结果保存在: {output_folder}")
    print(f"使用算法: 混合算法 (静态初始化+Mahony动态更新)")


def main():
    """主函数，提供交互式界面"""
    print("=" * 60)
    print("混合算法加速度世界坐标系转换工具")
    print("静态初始化 + Mahony算法动态更新")
    print("特点：使用重力数据(grav)进行姿态估计，转换加速度数据(acce)")
    print("=" * 60)
    
    # 检查依赖
    if not check_ahrs_dependency():
        print("请先安装AHRS库后再运行此程序")
        return
    
    print("\n算法说明:")
    print("1. 静止阶段：使用静态方法3建立初始世界坐标系")
    print("2. 运动阶段：使用重力数据(grav)作为Mahony算法的加速度输入")
    print("3. 坐标转换：用计算的四元数转换实际的加速度数据(acce)")
    print("4. 优势：避免运动加速度对姿态估计的干扰")
    
    # 输入文件夹路径
    default_path = r"E:\Document\user001\Session_20250702_235136"
    folder_path = input(f"\n请输入数据文件夹路径 [默认: {default_path}]: ")
    folder_path = folder_path.strip() if folder_path.strip() else default_path
    
    # 确认文件夹存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹不存在: {folder_path}")
        return
    
    # Mahony算法参数设置
    print("\n=== Mahony算法参数设置 ===")
    kp_input = input("请输入Mahony算法kp参数 [默认: 1.0]: ").strip()
    ki_input = input("请输入Mahony算法ki参数 [默认: 0.3]: ").strip()
    
    try:
        kp = float(kp_input) if kp_input else 1.0
        ki = float(ki_input) if ki_input else 0.3
    except ValueError:
        print("无效输入，使用默认值: kp=1.0, ki=0.3")
        kp, ki = 1.0, 0.3
    
    # 运动检测参数设置
    print("\n=== 运动检测参数设置 ===")
    threshold_input = input("请输入静止判断阈值 [默认: 0.5 m/s²]: ").strip()
    duration_input = input("请输入静止状态最小持续帧数 [默认: 5]: ").strip()
    
    try:
        static_threshold = float(threshold_input) if threshold_input else 0.5
        static_duration = int(duration_input) if duration_input else 5
    except ValueError:
        print("无效输入，使用默认值: threshold=0.5, duration=5")
        static_threshold, static_duration = 0.5, 5
    
    # 采样率设置
    sample_rate_input = input("请输入采样率 [默认: 100 Hz]: ").strip()
    try:
        sample_rate = float(sample_rate_input) if sample_rate_input else 100.0
    except ValueError:
        sample_rate = 100.0
    
    # 输出文件夹
    output_folder = os.path.join(folder_path, 'hybrid_world_coordinates')
    
    # 是否计算速度和位移
    calc_kinematics = input("是否计算速度和位移? (y/n) [默认: n]: ").lower().strip()
    calc_kinematics = calc_kinematics == 'y'
    
    print(f"\n开始处理...")
    print(f"使用算法: 混合算法 (静态初始化+Mahony动态更新)")
    print(f"Mahony参数: kp={kp}, ki={ki}")
    print(f"运动检测参数: threshold={static_threshold} m/s², duration={static_duration}帧")
    print(f"采样率: {sample_rate} Hz")
    print(f"输出文件夹: {output_folder}")
    
    # 处理所有片段
    process_all_segments_hybrid(
        folder_path, 
        output_folder, 
        calculate_kinematics=calc_kinematics,
        sample_rate=sample_rate,
        kp=kp,
        ki=ki,
        static_threshold=static_threshold,
        static_duration=static_duration
    )


if __name__ == "__main__":
    main()
