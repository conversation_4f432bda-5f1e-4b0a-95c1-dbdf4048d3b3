# -*- coding:utf-8 -*-
"""
测试简化的速度检测方法
验证新的简化逻辑：
1. 最小检测时间阈值0.1s，不足0.1s按0.1s输出
2. 通过相邻样本点一正一负判断零速度点
3. 在0时刻到零速度点之间找速度绝对值最大值
4. 去除复杂的稳健性检测
"""

import numpy as np
import matplotlib.pyplot as plt
from motion_event_detector import MotionEventDetector

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def create_simple_motion_data(sample_rate=100):
    """
    创建简单的运动数据，包含明确的零速度点
    """
    duration = 2.5  # 2.5秒数据
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # X轴：简单的加速-减速过程，在1.5s回到零速度
    acc_x = np.zeros_like(t)
    
    # 加速阶段 (0.2-0.8s)
    mask_accel = (t >= 0.2) & (t < 0.8)
    t_accel = t[mask_accel] - 0.2
    acc_x[mask_accel] = 2.0 * np.sin(np.pi * t_accel / 0.6)
    
    # 减速阶段 (0.8-1.5s)
    mask_decel = (t >= 0.8) & (t < 1.5)
    t_decel = t[mask_decel] - 0.8
    acc_x[mask_decel] = -1.5 * np.sin(np.pi * t_decel / 0.7)
    
    # Y轴：更短的运动，在1.0s回到零速度
    acc_y = np.zeros_like(t)
    
    # 加速阶段 (0.3-0.6s)
    mask_accel_y = (t >= 0.3) & (t < 0.6)
    t_accel_y = t[mask_accel_y] - 0.3
    acc_y[mask_accel_y] = 1.5 * np.sin(np.pi * t_accel_y / 0.3)
    
    # 减速阶段 (0.6-1.0s)
    mask_decel_y = (t >= 0.6) & (t < 1.0)
    t_decel_y = t[mask_decel_y] - 0.6
    acc_y[mask_decel_y] = -1.2 * np.sin(np.pi * t_decel_y / 0.4)
    
    # Z轴：中等长度运动，在1.8s回到零速度
    acc_z = np.zeros_like(t)
    
    # 加速阶段 (0.4-0.9s)
    mask_accel_z = (t >= 0.4) & (t < 0.9)
    t_accel_z = t[mask_accel_z] - 0.4
    acc_z[mask_accel_z] = 1.8 * np.sin(np.pi * t_accel_z / 0.5)
    
    # 减速阶段 (0.9-1.8s)
    mask_decel_z = (t >= 0.9) & (t < 1.8)
    t_decel_z = t[mask_decel_z] - 0.9
    acc_z[mask_decel_z] = -1.3 * np.sin(np.pi * t_decel_z / 0.9)
    
    # 添加少量噪声
    noise_level = 0.02
    acc_x += noise_level * np.random.normal(0, 1, len(t))
    acc_y += noise_level * np.random.normal(0, 1, len(t))
    acc_z += noise_level * np.random.normal(0, 1, len(t))
    
    # 组合三轴数据
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    return acc_data, t

def test_simplified_detection():
    """
    测试简化的检测方法
    """
    print("=== 测试简化的速度检测方法 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_simple_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    methods = [
        ('velocity', '三轴合速度矢量'),
        ('velocity_individual', '三轴独立检测'),
        ('velocity_main_secondary', '主轴+副轴合成')
    ]
    
    print("测试场景: 简单的加速-减速-回零过程")
    print("预期行为: 通过一正一负判断零速度点，在0时刻到零速度点之间找最大速度")
    print("最小时间阈值: 0.1秒")
    print()
    
    results = {}
    
    for method_name, method_desc in methods:
        print(f"{'-'*50}")
        print(f"测试方法: {method_desc} ({method_name})")
        print(f"{'-'*50}")
        
        try:
            start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
                acc_data, only_acceleration_phase=True, use_integration=True, detection_method=method_name
            )
            
            duration = (end_idx - start_idx) / sample_rate
            results[method_name] = {
                'start_time': start_idx / sample_rate,
                'end_time': end_idx / sample_rate,
                'duration': duration,
                'confidence': confidence,
                'success': True
            }
            
            print(f"检测结果:")
            print(f"  起始时间: {start_idx/sample_rate:.3f}s")
            print(f"  结束时间: {end_idx/sample_rate:.3f}s")
            print(f"  持续时间: {duration:.3f}s")
            print(f"  置信度: {confidence:.3f}")
            
            # 验证最小时间阈值
            if duration >= 0.1:
                print(f"  ✅ 满足0.1秒最小时间阈值")
            else:
                print(f"  ❌ 不满足0.1秒最小时间阈值")
            
        except Exception as e:
            print(f"检测失败: {e}")
            results[method_name] = {
                'success': False,
                'error': str(e)
            }
        
        print()
    
    return acc_data, time_axis, results

def analyze_zero_crossing_detection():
    """
    分析零速度点检测的详细过程
    """
    print("=== 分析零速度点检测过程 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_simple_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    
    # 计算三轴速度
    window = 5
    smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
    smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
    smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
    
    combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
    threshold = 0.1 * np.max(combined_acc)
    start_candidates = np.where(combined_acc > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    dt = 1.0 / sample_rate
    velocity_x = np.cumsum(smoothed_x[start_idx:]) * dt
    velocity_y = np.cumsum(smoothed_y[start_idx:]) * dt
    velocity_z = np.cumsum(smoothed_z[start_idx:]) * dt
    velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)
    
    # 分析零速度点检测
    min_samples = int(0.1 * sample_rate)
    
    print(f"零速度点检测分析:")
    print(f"  时间阈值: 0.1秒 ({min_samples}个样本)")
    
    # 分析各轴的零速度点（一正一负检测）
    axis_names = ['X', 'Y', 'Z']
    axis_velocities = [velocity_x, velocity_y, velocity_z]
    
    for axis_name, velocity_axis in zip(axis_names, axis_velocities):
        print(f"\n  {axis_name}轴分析:")
        zero_point = detector._find_first_velocity_decrease_single_axis(velocity_axis, min_samples, axis_name)
        if zero_point is not None:
            zero_time = (start_idx + zero_point) / sample_rate
            print(f"    零速度点: 索引{zero_point}, 时间{zero_time:.3f}s")
            
            # 在零速度点之前找最大速度（绝对值）
            search_range = velocity_axis[:zero_point + 1]
            if len(search_range) > 0:
                max_idx = np.argmax(np.abs(search_range))
                max_time = (start_idx + max_idx) / sample_rate
                max_value = velocity_axis[max_idx]
                print(f"    零速度点前最大速度: 索引{max_idx}, 时间{max_time:.3f}s, 速度{max_value:.3f}")
        else:
            print(f"    未找到零速度点")
    
    # 分析合速度矢量的接近零点
    print(f"\n  合速度矢量分析:")
    zero_point = detector._find_first_zero_velocity_point_magnitude(velocity_magnitude, min_samples)
    if zero_point is not None:
        zero_time = (start_idx + zero_point) / sample_rate
        print(f"    接近零点: 索引{zero_point}, 时间{zero_time:.3f}s")
        
        # 在零点之前找最大速度
        search_range = velocity_magnitude[:zero_point + 1]
        if len(search_range) > 0:
            max_idx = np.argmax(search_range)
            max_time = (start_idx + max_idx) / sample_rate
            max_value = velocity_magnitude[max_idx]
            print(f"    零点前最大速度: 索引{max_idx}, 时间{max_time:.3f}s, 速度{max_value:.3f}")
    else:
        print(f"    未找到接近零点")
    
    return start_idx, velocity_x, velocity_y, velocity_z, velocity_magnitude

def visualize_simplified_detection():
    """
    可视化简化检测方法的效果
    """
    print("\n=== 生成简化检测方法可视化 ===")
    
    # 获取测试数据和结果
    acc_data, time_axis, results = test_simplified_detection()
    start_idx, velocity_x, velocity_y, velocity_z, velocity_magnitude = analyze_zero_crossing_detection()
    
    sample_rate = 100
    min_samples = int(0.1 * sample_rate)
    
    # 创建可视化
    fig, axes = plt.subplots(4, 1, figsize=(14, 12))
    
    # 子图1: 加速度数据
    axes[0].plot(time_axis, acc_data[:, 0], 'r-', label='X轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 1], 'g-', label='Y轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 2], 'b-', label='Z轴加速度', alpha=0.8)
    
    # 标记时间阈值线
    threshold_time = (start_idx + min_samples) / sample_rate
    axes[0].axvline(x=threshold_time, color='orange', linestyle='--', linewidth=2, label='0.1s时间阈值')
    
    axes[0].set_title('加速度数据（简单的加速-减速-回零过程）')
    axes[0].set_ylabel('加速度 (m/s²)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 子图2: 三轴速度分量
    time_vel = np.arange(start_idx, start_idx + len(velocity_x)) / sample_rate
    axes[1].plot(time_vel, velocity_x, 'r-', label='X轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_y, 'g-', label='Y轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_z, 'b-', label='Z轴速度', linewidth=2)
    
    # 标记零速度点和最大速度点
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    for velocity_axis, color, axis_name in zip([velocity_x, velocity_y, velocity_z], ['red', 'green', 'blue'], ['X', 'Y', 'Z']):
        # 找零速度点（一正一负）
        zero_point = detector._find_first_velocity_decrease_single_axis(velocity_axis, min_samples, axis_name)
        if zero_point is not None:
            zero_time = time_vel[zero_point]
            axes[1].axvline(x=zero_time, color=color, linestyle=':', alpha=0.7, label=f'{axis_name}轴零点')
            
            # 在零速度点前找最大速度
            search_range = velocity_axis[:zero_point + 1]
            if len(search_range) > 0:
                max_idx = np.argmax(np.abs(search_range))
                max_time = time_vel[max_idx]
                max_value = velocity_axis[max_idx]
                axes[1].plot(max_time, max_value, 'o', color=color, markersize=8, 
                           label=f'{axis_name}轴最大速度')
    
    axes[1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axes[1].set_title('三轴速度分量与零速度点检测（一正一负判断）')
    axes[1].set_ylabel('速度 (m/s)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 子图3: 合速度矢量
    axes[2].plot(time_vel, velocity_magnitude, 'purple', label='三轴合速度矢量', linewidth=3)
    
    # 标记合速度的接近零点和最大速度点
    zero_point = detector._find_first_zero_velocity_point_magnitude(velocity_magnitude, min_samples)
    if zero_point is not None:
        zero_time = time_vel[zero_point]
        axes[2].axvline(x=zero_time, color='purple', linestyle=':', alpha=0.7, label='合速度接近零点')
        
        # 在零点前找最大速度
        search_range = velocity_magnitude[:zero_point + 1]
        if len(search_range) > 0:
            max_idx = np.argmax(search_range)
            max_time = time_vel[max_idx]
            max_value = velocity_magnitude[max_idx]
            axes[2].plot(max_time, max_value, 'o', color='purple', markersize=10, 
                       label='合速度最大值')
    
    axes[2].set_title('三轴合速度矢量与接近零点检测')
    axes[2].set_ylabel('速度 (m/s)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    # 子图4: 检测结果总结
    axes[3].text(0.05, 0.8, '简化检测方法结果:', fontsize=12, fontweight='bold')
    
    y_pos = 0.7
    for method_name in ['velocity', 'velocity_individual', 'velocity_main_secondary']:
        method_labels = {'velocity': '三轴合速度', 'velocity_individual': '三轴独立', 'velocity_main_secondary': '主轴+副轴'}
        if results[method_name]['success']:
            result = results[method_name]
            duration = result['duration']
            status = "✅ 满足阈值" if duration >= 0.1 else "❌ 不满足阈值"
            axes[3].text(0.05, y_pos, f'{method_labels[method_name]}: {duration:.3f}s {status}', fontsize=10)
        else:
            axes[3].text(0.05, y_pos, f'{method_labels[method_name]}: 检测失败', fontsize=10, color='red')
        y_pos -= 0.1
    
    axes[3].text(0.05, 0.3, '简化特点:', fontsize=12, fontweight='bold')
    axes[3].text(0.05, 0.2, '• 最小时间阈值: 0.1秒，不足0.1秒按0.1秒输出', fontsize=10)
    axes[3].text(0.05, 0.1, '• 零速度点: 通过相邻样本点一正一负判断', fontsize=10)
    axes[3].text(0.05, 0.0, '• 最大速度: 在0时刻到零速度点之间找绝对值最大值', fontsize=10)
    
    axes[3].set_xlim(0, 1)
    axes[3].set_ylim(0, 1)
    axes[3].axis('off')
    
    plt.tight_layout()
    plt.savefig('simplified_velocity_detection.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("简化检测方法图已保存为: simplified_velocity_detection.png")

def test_minimum_threshold_enforcement():
    """
    测试最小时间阈值的强制执行
    """
    print("\n=== 测试最小时间阈值强制执行 ===")
    
    sample_rate = 100
    
    # 创建一个很短的运动信号（理论上只有0.05秒）
    duration = 1.5
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    acc_x = np.zeros_like(t)
    # 只在0.2-0.25秒有运动（0.05秒持续时间）
    mask = (t >= 0.2) & (t < 0.25)
    acc_x[mask] = 4.0 * np.sin(np.pi * (t[mask] - 0.2) / 0.05)
    
    acc_y = 0.1 * np.random.normal(0, 1, len(t))
    acc_z = 0.1 * np.random.normal(0, 1, len(t))
    
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    print("测试短时间运动信号（理论持续时间: 0.05秒）")
    print("预期: 检测持续时间应该被强制设置为0.1秒")
    print()
    
    try:
        start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
            acc_data, 
            only_acceleration_phase=True, 
            use_integration=True, 
            detection_method='velocity_individual'
        )
        
        duration = (end_idx - start_idx) / sample_rate
        
        print(f"检测结果:")
        print(f"  起始时间: {start_idx/sample_rate:.3f}s")
        print(f"  结束时间: {end_idx/sample_rate:.3f}s")
        print(f"  检测持续时间: {duration:.3f}s")
        print(f"  理论持续时间: 0.05s")
        
        if abs(duration - 0.1) < 0.01:
            print(f"  ✅ 最小时间阈值强制执行成功")
        else:
            print(f"  ❌ 最小时间阈值强制执行失败")
            
    except Exception as e:
        print(f"  ❌ 检测失败: {e}")

if __name__ == "__main__":
    print("简化的速度检测方法测试")
    print("=" * 60)
    
    print("简化说明:")
    print("1. 最小检测时间阈值: 0.1秒，不足0.1秒按0.1秒输出")
    print("2. 零速度点检测: 通过相邻样本点一正一负判断")
    print("3. 最大速度检测: 在0时刻到零速度点之间找绝对值最大值")
    print("4. 去除复杂验证: 不需要稳健性检测等复杂逻辑")
    print()
    
    # 测试简化检测方法
    test_simplified_detection()
    
    # 分析零速度点检测
    analyze_zero_crossing_detection()
    
    # 生成可视化
    visualize_simplified_detection()
    
    # 测试最小时间阈值强制执行
    test_minimum_threshold_enforcement()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n简化总结:")
    print("1. ✅ 实现了0.1秒最小时间阈值强制执行")
    print("2. ✅ 实现了一正一负的零速度点检测")
    print("3. ✅ 在0时刻到零速度点之间找绝对值最大速度")
    print("4. ✅ 去除了复杂的稳健性检测逻辑")
    print("5. ✅ 简化了检测流程，提高了效率")
    print("\n现在的检测方法更加简单直接！")
