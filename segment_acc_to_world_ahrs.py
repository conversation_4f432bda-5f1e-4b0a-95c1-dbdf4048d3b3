# -*- coding:utf-8 -*-
"""
作者：何志想
日期：2025年07月16日
功能：使用AHRS库的Madgwick和Mahony算法进行高精度姿态估计，将加速度转换到世界坐标系
依赖：pip install ahrs
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import re
import glob

# 导入AHRS库
try:
    from ahrs.filters import Madgwick, Mahony
    from ahrs.common import Quaternion
    AHRS_AVAILABLE = True
    print("成功导入AHRS库")
except ImportError as e:
    print(f"警告: 无法导入AHRS库: {e}")
    print("请安装AHRS库: pip install ahrs")
    AHRS_AVAILABLE = False

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 


def check_ahrs_dependency():
    """检查AHRS库依赖"""
    if not AHRS_AVAILABLE:
        print("错误: AHRS库未安装或导入失败")
        print("请运行以下命令安装: pip install ahrs")
        return False
    return True


def load_segment_files(segment_folder, segment_number=None):
    """
    加载特定片段的所有传感器数据文件
    
    Args:
        segment_folder: 存放片段数据的文件夹路径
        segment_number: 指定片段编号，如果为None则加载所有片段
        
    Returns:
        字典，包含每个片段的数据，格式为 {片段编号: {'acce': df_acc, 'grav': df_grav, 'mag': df_mag, 'gyro': df_gyro}}
    """
    # 查找所有片段文件
    all_files = glob.glob(os.path.join(segment_folder, "segment*_*.csv"))
    
    segment_data = {}
    # 正则表达式匹配文件名 - 格式为 segment数字_传感器类型_时间戳.csv
    pattern = r"segment(\d+)_(acce|grav|mag|gyro)_(\d+-\d+-\d+\.\d+)\.csv"
    
    for file_path in all_files:
        file_name = os.path.basename(file_path)
        match = re.match(pattern, file_name)
        
        if match:
            seg_num = int(match.group(1))
            sensor_type = match.group(2)
            timestamp = match.group(3)
            
            # 如果指定了片段编号，只处理该片段
            if segment_number is not None and seg_num != segment_number:
                continue
            
            # 读取数据
            try:
                df = pd.read_csv(file_path)
                # 保存文件路径到DataFrame的元数据中，方便后续引用
                df.name = file_path
                
                # 初始化字典
                if seg_num not in segment_data:
                    segment_data[seg_num] = {}
                
                # 保存数据
                segment_data[seg_num][sensor_type] = df
                
                print(f"加载文件: {file_name}, 形状: {df.shape}")
            except Exception as e:
                print(f"加载文件 {file_name} 错误: {e}")
    
    return segment_data


def time_to_seconds(time_str):
    """
    将HH-MM-SS.mmm格式转换为秒数
    
    Args:
        time_str: 时间字符串，格式为HH-MM-SS.mmm
        
    Returns:
        浮点数表示的秒数
    """
    if not isinstance(time_str, str):
        print(f"警告: 非字符串时间数据: {time_str}，类型: {type(time_str)}")
        return 0
    
    try:
        time_str = time_str.strip()
        if '.' in time_str:
            dt = datetime.strptime(time_str, '%H-%M-%S.%f')
        else:
            dt = datetime.strptime(time_str, '%H-%M-%S')
        return dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1e6
    except ValueError as e:
        print(f"无法解析时间字符串: {time_str}，错误: {e}")
        return 0


def preprocess_time_data(df):
    """预处理时间数据，将时间字符串转换为浮点数秒"""
    if 'time' in df.columns:
        print(f"数据列: {list(df.columns)}")
        print(f"数据示例:\n{df.head(2)}")
        df['time_seconds'] = df['time'].apply(time_to_seconds)
    return df


def smooth_data(data, window_size=10):
    """
    使用滑动窗口平均对数据进行平滑处理
    
    Args:
        data: 需要平滑的数据数组
        window_size: 滑动窗口大小
        
    Returns:
        平滑后的数据数组
    """
    if len(data) <= window_size:
        return data
    
    smoothed = np.zeros_like(data)
    for i in range(len(data)):
        start = max(0, i - window_size//2)
        end = min(len(data), i + window_size//2 + 1)
        smoothed[i] = np.mean(data[start:end], axis=0)
    
    return smoothed


def synchronize_sensor_data(acc_df, grav_df, mag_df, gyro_df=None):
    """
    同步所有传感器数据到统一的时间基准
    
    Args:
        acc_df: 加速度数据
        grav_df: 重力数据
        mag_df: 磁力计数据
        gyro_df: 陀螺仪数据（可选）
        
    Returns:
        同步后的传感器数据字典
    """
    # 使用加速度数据的时间戳作为参考
    timestamps = acc_df['time_seconds'].values
    
    synchronized_data = {
        'time': acc_df['time'].values,
        'time_seconds': timestamps,
        'accel': np.zeros((len(timestamps), 3)),
        'mag': np.zeros((len(timestamps), 3)),
        'grav': np.zeros((len(timestamps), 3))
    }
    
    if gyro_df is not None:
        synchronized_data['gyro'] = np.zeros((len(timestamps), 3))
    
    # 同步每个时间点的数据
    for i, time_sec in enumerate(timestamps):
        # 加速度数据（已经对齐）
        synchronized_data['accel'][i] = [acc_df.iloc[i]['x'], acc_df.iloc[i]['y'], acc_df.iloc[i]['z']]
        
        # 寻找最接近的重力数据
        if 'sensor_timestamp' in acc_df.columns and 'sensor_timestamp' in grav_df.columns:
            acc_sensor_time = acc_df.iloc[i]['sensor_timestamp']
            grav_diffs = np.abs(grav_df['sensor_timestamp'].values - acc_sensor_time)
        else:
            grav_diffs = np.abs(grav_df['time_seconds'].values - time_sec)
        grav_idx = np.argmin(grav_diffs)
        synchronized_data['grav'][i] = [grav_df.iloc[grav_idx]['x'], grav_df.iloc[grav_idx]['y'], grav_df.iloc[grav_idx]['z']]
        
        # 寻找最接近的磁力计数据
        if 'sensor_timestamp' in acc_df.columns and 'sensor_timestamp' in mag_df.columns:
            acc_sensor_time = acc_df.iloc[i]['sensor_timestamp']
            mag_diffs = np.abs(mag_df['sensor_timestamp'].values - acc_sensor_time)
        else:
            mag_diffs = np.abs(mag_df['time_seconds'].values - time_sec)
        mag_idx = np.argmin(mag_diffs)
        synchronized_data['mag'][i] = [mag_df.iloc[mag_idx]['x'], mag_df.iloc[mag_idx]['y'], mag_df.iloc[mag_idx]['z']]
        
        # 同步陀螺仪数据（如果存在）
        if gyro_df is not None:
            if 'sensor_timestamp' in acc_df.columns and 'sensor_timestamp' in gyro_df.columns:
                acc_sensor_time = acc_df.iloc[i]['sensor_timestamp']
                gyro_diffs = np.abs(gyro_df['sensor_timestamp'].values - acc_sensor_time)
            else:
                gyro_diffs = np.abs(gyro_df['time_seconds'].values - time_sec)
            gyro_idx = np.argmin(gyro_diffs)
            synchronized_data['gyro'][i] = [gyro_df.iloc[gyro_idx]['x'], gyro_df.iloc[gyro_idx]['y'], gyro_df.iloc[gyro_idx]['z']]
    
    return synchronized_data


def process_segment_data_ahrs(segment_data, segment_num, output_folder, 
                             algorithm='madgwick', window_size=10, sample_rate=100.0,
                             beta=0.1, kp=1.0, ki=0.3):
    """
    使用AHRS库算法处理一个片段的数据，转换加速度到世界坐标系
    
    Args:
        segment_data: 片段数据字典
        segment_num: 片段编号
        output_folder: 输出文件夹
        algorithm: 使用的算法 ('madgwick', 'mahony', 'simple')
        window_size: 平滑窗口大小
        sample_rate: 采样率 (Hz)
        beta: Madgwick算法的beta参数
        kp: Mahony算法的kp参数
        ki: Mahony算法的ki参数
        
    Returns:
        转换后的加速度数据DataFrame
    """
    if not check_ahrs_dependency() and algorithm in ['madgwick', 'mahony']:
        print(f"AHRS库不可用，使用简单方法替代")
        algorithm = 'simple'
    
    segment_info = segment_data[segment_num]
    
    # 检查必要的数据是否存在
    required_sensors = ['acce', 'grav', 'mag']
    if algorithm in ['madgwick', 'mahony'] and 'gyro' in segment_info:
        required_sensors.append('gyro')
    
    missing_sensors = [sensor for sensor in required_sensors if sensor not in segment_info]
    if missing_sensors:
        print(f"片段 {segment_num} 缺少必要的数据: {missing_sensors}")
        if 'gyro' in missing_sensors and algorithm in ['madgwick', 'mahony']:
            print(f"警告: 缺少陀螺仪数据，将使用仅加速度计+磁力计模式")
        elif len(missing_sensors) > 1 or 'acce' in missing_sensors:
            return None
    
    # 获取传感器数据
    acc_df = segment_info['acce'].copy()
    grav_df = segment_info['grav'].copy()
    mag_df = segment_info['mag'].copy()
    gyro_df = segment_info.get('gyro', None)
    if gyro_df is not None:
        gyro_df = gyro_df.copy()
    
    print(f"\n处理片段 {segment_num} - 使用 {algorithm.upper()} 算法 (AHRS库)")
    print(f"加速度数据: {acc_df.shape}")
    print(f"重力数据: {grav_df.shape}")
    print(f"磁力计数据: {mag_df.shape}")
    if gyro_df is not None:
        print(f"陀螺仪数据: {gyro_df.shape}")
    
    # 预处理时间数据
    acc_df = preprocess_time_data(acc_df)
    grav_df = preprocess_time_data(grav_df)
    mag_df = preprocess_time_data(mag_df)
    if gyro_df is not None:
        gyro_df = preprocess_time_data(gyro_df)
    
    # 同步传感器数据
    sync_data = synchronize_sensor_data(acc_df, grav_df, mag_df, gyro_df)
    
    # 估计采样率
    if len(sync_data['time_seconds']) > 1:
        time_diff = sync_data['time_seconds'][-1] - sync_data['time_seconds'][0]
        estimated_rate = len(sync_data['time_seconds']) / time_diff
        sample_rate = min(sample_rate, estimated_rate)  # 使用较小的值避免过高估计
    
    print(f"使用采样率: {sample_rate:.1f} Hz")
    
    # 初始化结果存储
    acc_world_all = []
    quaternions_all = []
    
    if algorithm == 'simple':
        # 使用简单方法（原始方法）
        print("使用简单坐标系构建方法...")
        for i in range(len(sync_data['time_seconds'])):
            # 构建世界坐标系
            gravity = sync_data['grav'][i]
            magnetic = sync_data['mag'][i]
            
            # 简单的坐标系构建
            up = gravity / np.linalg.norm(gravity) if np.linalg.norm(gravity) > 0 else np.array([0, 0, 1])
            east = np.cross(magnetic, up)
            east = east / np.linalg.norm(east) if np.linalg.norm(east) > 0 else np.array([1, 0, 0])
            north = np.cross(up, east)
            north = north / np.linalg.norm(north) if np.linalg.norm(north) > 0 else np.array([0, 1, 0])
            
            world_coord_system = np.array([east, north, up])
            
            # 转换加速度
            acc_world = np.dot(sync_data['accel'][i], world_coord_system.T)
            acc_world_all.append(acc_world)
            quaternions_all.append([1, 0, 0, 0])  # 占位符
    
    elif algorithm == 'madgwick' and AHRS_AVAILABLE:
        # 使用AHRS库的Madgwick算法
        # 关键修改：使用重力数据作为姿态估计输入，用计算的四元数转换加速度数据
        print(f"使用AHRS库的Madgwick算法 (beta={beta})...")
        print("姿态估计输入: 重力计数据 (grav)")
        print("坐标转换目标: 加速度数据 (acce)")
        
        # 准备数据矩阵 - 关键：使用重力数据进行姿态估计
        gravity_data = sync_data['grav']  # 使用重力数据进行姿态估计
        actual_accel_data = sync_data['accel']  # 实际要转换的加速度数据
        mag_data = sync_data['mag']
        
        print(f"重力数据形状: {gravity_data.shape} (用于姿态估计)")
        print(f"加速度数据形状: {actual_accel_data.shape} (用于坐标转换)")
        
        if gyro_df is not None:
            gyro_data = sync_data['gyro'] * np.pi / 180  # 转换为弧度
            print("使用陀螺仪+重力计+磁力计模式进行姿态估计")
            
            # 使用重力数据而不是加速度数据进行姿态估计
            madgwick = Madgwick(gyr=gyro_data, acc=gravity_data, mag=mag_data, 
                              frequency=sample_rate, beta=beta)
        else:
            print("使用重力计+磁力计模式进行姿态估计 (无陀螺仪)")
            # 创建零陀螺仪数据
            gyro_data = np.zeros_like(gravity_data)
            
            # 使用重力数据进行姿态估计
            madgwick = Madgwick(gyr=gyro_data, acc=gravity_data, mag=mag_data, 
                              frequency=sample_rate, beta=beta)
        
        # 获取基于重力数据计算的四元数
        quaternions_all = madgwick.Q
        print(f"基于重力数据计算得到 {len(quaternions_all)} 个四元数")
        
        # 使用计算的四元数转换实际加速度数据
        for i, q in enumerate(quaternions_all):
            # AHRS库的四元数格式是 [w, x, y, z]
            quat = Quaternion(q)
            rotation_matrix = quat.to_DCM()  # 方向余弦矩阵 (DCM)
            
            # 应用旋转矩阵到实际加速度数据
            acc_world = np.dot(rotation_matrix, actual_accel_data[i])
            acc_world_all.append(acc_world)
    
    elif algorithm == 'mahony' and AHRS_AVAILABLE:
        # 使用AHRS库的Mahony算法
        # 关键修改：使用重力数据作为姿态估计输入，用计算的四元数转换加速度数据
        print(f"使用AHRS库的Mahony算法 (kp={kp}, ki={ki})...")
        print("姿态估计输入: 重力计数据 (grav)")
        print("坐标转换目标: 加速度数据 (acce)")
        
        # 准备数据矩阵 - 关键：使用重力数据进行姿态估计
        gravity_data = sync_data['grav']  # 使用重力数据进行姿态估计
        actual_accel_data = sync_data['accel']  # 实际要转换的加速度数据
        mag_data = sync_data['mag']
        
        print(f"重力数据形状: {gravity_data.shape} (用于姿态估计)")
        print(f"加速度数据形状: {actual_accel_data.shape} (用于坐标转换)")
        
        if gyro_df is not None:
            gyro_data = sync_data['gyro'] * np.pi / 180  # 转换为弧度
            print("使用陀螺仪+重力计+磁力计模式进行姿态估计")
            
            # 使用重力数据而不是加速度数据进行姿态估计
            mahony = Mahony(gyr=gyro_data, acc=gravity_data, mag=mag_data, 
                           frequency=sample_rate, k_P=kp, k_I=ki)
        else:
            print("使用重力计+磁力计模式进行姿态估计 (无陀螺仪)")
            # 创建零陀螺仪数据
            gyro_data = np.zeros_like(gravity_data)
            
            # 使用重力数据进行姿态估计
            mahony = Mahony(gyr=gyro_data, acc=gravity_data, mag=mag_data, 
                           frequency=sample_rate, k_P=kp, k_I=ki)
        
        # 获取基于重力数据计算的四元数
        quaternions_all = mahony.Q
        print(f"基于重力数据计算得到 {len(quaternions_all)} 个四元数")
        
        # 使用计算的四元数转换实际加速度数据
        for i, q in enumerate(quaternions_all):
            # AHRS库的四元数格式是 [w, x, y, z]
            quat = Quaternion(q)
            rotation_matrix = quat.to_DCM()  # 方向余弦矩阵 (DCM)
            
            # 应用旋转矩阵到实际加速度数据
            acc_world = np.dot(rotation_matrix, actual_accel_data[i])
            acc_world_all.append(acc_world)
    
    else:
        print(f"不支持的算法: {algorithm} 或AHRS库不可用，回退到简单方法")
        return process_segment_data_ahrs(segment_data, segment_num, output_folder, 
                                       'simple', window_size, sample_rate)
    
    # 转换为numpy数组
    acc_world_all = np.array(acc_world_all)
    quaternions_all = np.array(quaternions_all)
    
    # 应用平滑滤波
    if len(acc_world_all) > window_size:
        acc_world_all = smooth_data(acc_world_all, window_size)
    
    # 创建结果DataFrame
    result_df = pd.DataFrame()
    result_df['time'] = sync_data['time']
    result_df['x'] = acc_world_all[:, 0]  # 东方向
    result_df['y'] = acc_world_all[:, 1]  # 北方向
    result_df['z'] = acc_world_all[:, 2]  # 上方向
    
    # 添加原始加速度数据（用于转换的实际加速度数据）
    result_df['x_raw'] = sync_data['accel'][:, 0]
    result_df['y_raw'] = sync_data['accel'][:, 1]
    result_df['z_raw'] = sync_data['accel'][:, 2]
    
    # 添加重力数据（用于姿态估计的输入数据）
    result_df['x_grav'] = sync_data['grav'][:, 0]
    result_df['y_grav'] = sync_data['grav'][:, 1]
    result_df['z_grav'] = sync_data['grav'][:, 2]
    
    # 添加四元数信息 (AHRS库格式: [w, x, y, z])
    result_df['qw'] = quaternions_all[:, 0]
    result_df['qx'] = quaternions_all[:, 1]
    result_df['qy'] = quaternions_all[:, 2]
    result_df['qz'] = quaternions_all[:, 3]
    
    # 生成输出文件名
    acc_filename = os.path.basename(acc_df.name) if hasattr(acc_df, 'name') else ""
    match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
    
    if match:
        timestamp = match.group(1)
    else:
        if 'time' in acc_df.columns and len(acc_df) > 0:
            first_time = acc_df['time'].iloc[0]
            timestamp = first_time.replace(':', '-') if isinstance(first_time, str) else datetime.now().strftime('%H-%M-%S.%f')[:-3]
        else:
            timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
    
    # 保存结果
    output_file = os.path.join(output_folder, f'segment{segment_num}_{algorithm}_ahrs_world_acce_{timestamp}.csv')
    result_df.to_csv(output_file, index=False)
    print(f"已保存世界坐标系加速度数据: {output_file}")
    
    # 保存四元数数据
    quat_output_file = os.path.join(output_folder, f'segment{segment_num}_{algorithm}_ahrs_quaternions_{timestamp}.csv')
    quat_df = pd.DataFrame({
        'time': sync_data['time'],
        'qw': quaternions_all[:, 0],
        'qx': quaternions_all[:, 1],
        'qy': quaternions_all[:, 2],
        'qz': quaternions_all[:, 3]
    })
    quat_df.to_csv(quat_output_file, index=False)
    print(f"已保存四元数数据: {quat_output_file}")
    
    return result_df


def visualize_acceleration_ahrs(segment_data, segment_num, world_acc_df, output_folder, algorithm):
    """
    可视化原始加速度和世界坐标系加速度（AHRS版本，包含四元数信息）
    
    Args:
        segment_data: 片段数据字典
        segment_num: 片段编号
        world_acc_df: 世界坐标系加速度数据
        output_folder: 输出文件夹
        algorithm: 使用的算法名称
    """
    if segment_num not in segment_data or 'acce' not in segment_data[segment_num]:
        print(f"片段 {segment_num} 没有加速度数据，无法可视化")
        return
    
    acc_df = segment_data[segment_num]['acce']
    
    # 创建时间轴
    time_seconds = np.arange(len(world_acc_df)) / 100  # 假设采样率为100Hz
    
    # 创建更复杂的可视化（添加重力数据显示）
    fig, axes = plt.subplots(4, 2, figsize=(15, 16))
    
    # 原始加速度数据（实际转换目标）
    axes[0, 0].plot(time_seconds, world_acc_df['x_raw'], 'r-', label='X轴', alpha=0.7)
    axes[0, 0].plot(time_seconds, world_acc_df['y_raw'], 'g-', label='Y轴', alpha=0.7)
    axes[0, 0].plot(time_seconds, world_acc_df['z_raw'], 'b-', label='Z轴', alpha=0.7)
    axes[0, 0].set_title(f'片段 {segment_num} - 加速度数据 (转换目标)')
    axes[0, 0].set_xlabel('时间 (秒)')
    axes[0, 0].set_ylabel('加速度 (m/s²)')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].legend()
    
    # 重力数据（姿态估计输入）
    if 'x_grav' in world_acc_df.columns:
        axes[0, 1].plot(time_seconds, world_acc_df['x_grav'], 'r--', label='X轴', alpha=0.7)
        axes[0, 1].plot(time_seconds, world_acc_df['y_grav'], 'g--', label='Y轴', alpha=0.7)
        axes[0, 1].plot(time_seconds, world_acc_df['z_grav'], 'b--', label='Z轴', alpha=0.7)
        axes[0, 1].set_title(f'重力计数据 (姿态估计输入)')
        axes[0, 1].set_xlabel('时间 (秒)')
        axes[0, 1].set_ylabel('重力 (m/s²)')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].legend()
    
    # 世界坐标系加速度（转换结果）
    axes[1, 0].plot(time_seconds, world_acc_df['x'], 'r-', label='东向(East/X轴)', alpha=0.7)
    axes[1, 0].plot(time_seconds, world_acc_df['y'], 'g-', label='北向(North/Y轴)', alpha=0.7)
    axes[1, 0].plot(time_seconds, world_acc_df['z'], 'b-', label='上向(Up/Z轴)', alpha=0.7)
    axes[1, 0].set_title(f'世界坐标系加速度 (AHRS-{algorithm.upper()})')
    axes[1, 0].set_xlabel('时间 (秒)')
    axes[1, 0].set_ylabel('加速度 (m/s²)')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].legend()
    
    # 四元数可视化
    if 'qw' in world_acc_df.columns:
        axes[1, 1].plot(time_seconds, world_acc_df['qw'], 'k-', label='qw (标量)', linewidth=2)
        axes[1, 1].plot(time_seconds, world_acc_df['qx'], 'r-', label='qx', alpha=0.7)
        axes[1, 1].plot(time_seconds, world_acc_df['qy'], 'g-', label='qy', alpha=0.7)
        axes[1, 1].plot(time_seconds, world_acc_df['qz'], 'b-', label='qz', alpha=0.7)
        axes[1, 1].set_title('姿态四元数 (基于重力数据计算)')
        axes[1, 1].set_xlabel('时间 (秒)')
        axes[1, 1].set_ylabel('四元数值')
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].legend()
        
        # 四元数模长
        quat_magnitude = np.sqrt(world_acc_df['qw']**2 + world_acc_df['qx']**2 + 
                                world_acc_df['qy']**2 + world_acc_df['qz']**2)
        axes[2, 0].plot(time_seconds, quat_magnitude, 'purple', linewidth=2)
        axes[2, 0].set_title('四元数模长 (应接近1)')
        axes[2, 0].set_xlabel('时间 (秒)')
        axes[2, 0].set_ylabel('模长')
        axes[2, 0].grid(True, alpha=0.3)
        axes[2, 0].axhline(y=1.0, color='red', linestyle='--', alpha=0.5, label='理想值=1')
        axes[2, 0].legend()
        
        # 统计信息
        mean_magnitude = np.mean(quat_magnitude)
        std_magnitude = np.std(quat_magnitude)
        axes[2, 0].text(0.05, 0.95, f'均值: {mean_magnitude:.6f}\n标准差: {std_magnitude:.6f}', 
                       transform=axes[2, 0].transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 加速度幅值比较
    raw_magnitude = np.sqrt(world_acc_df['x_raw']**2 + world_acc_df['y_raw']**2 + world_acc_df['z_raw']**2)
    world_magnitude = np.sqrt(world_acc_df['x']**2 + world_acc_df['y']**2 + world_acc_df['z']**2)
    
    axes[2, 1].plot(time_seconds, raw_magnitude, 'orange', label='原始加速度幅值', alpha=0.7)
    axes[2, 1].plot(time_seconds, world_magnitude, 'purple', label='世界坐标系加速度幅值', alpha=0.7)
    axes[2, 1].set_title('加速度幅值比较')
    axes[2, 1].set_xlabel('时间 (秒)')
    axes[2, 1].set_ylabel('加速度幅值 (m/s²)')
    axes[2, 1].grid(True, alpha=0.3)
    axes[2, 1].legend()
    
    # 重力分离效果（Z轴应主要包含重力）
    axes[3, 0].plot(time_seconds, world_acc_df['z'], 'b-', label='Z轴 (上向)', alpha=0.7)
    axes[3, 0].axhline(y=9.8, color='red', linestyle='--', alpha=0.5, label='标准重力 9.8 m/s²')
    axes[3, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axes[3, 0].set_title('重力分离效果 (Z轴)')
    axes[3, 0].set_xlabel('时间 (秒)')
    axes[3, 0].set_ylabel('加速度 (m/s²)')
    axes[3, 0].grid(True, alpha=0.3)
    axes[3, 0].legend()
    
    # Z轴统计信息
    z_mean = np.mean(world_acc_df['z'])
    z_std = np.std(world_acc_df['z'])
    axes[3, 0].text(0.05, 0.95, f'Z轴均值: {z_mean:.3f} m/s²\nZ轴标准差: {z_std:.3f} m/s²', 
                   transform=axes[3, 0].transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 数据处理策略说明
    strategy_text = f"""数据处理策略 (AHRS-{algorithm.upper()}):
    
• 姿态估计输入: 重力计数据 (grav)
• 坐标转换目标: 加速度数据 (acce)
• 算法流程:
  1. 用重力数据计算姿态四元数
  2. 用四元数转换加速度到世界坐标系
• 优势: 分离姿态估计和运动检测"""
    
    axes[3, 1].text(0.05, 0.95, strategy_text, transform=axes[3, 1].transAxes, 
                   verticalalignment='top', fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    axes[3, 1].set_title('算法策略说明')
    axes[3, 1].set_xticks([])
    axes[3, 1].set_yticks([])
    
    plt.tight_layout()
    
    # 保存图表
    acc_filename = os.path.basename(segment_data[segment_num]['acce'].name) if hasattr(segment_data[segment_num]['acce'], 'name') else ""
    match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
    
    if match:
        timestamp = match.group(1)
    else:
        timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
    
    output_file = os.path.join(output_folder, f'segment{segment_num}_{algorithm}_ahrs_acceleration_analysis_{timestamp}.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"已保存可视化图表: {output_file}")


def calculate_velocity_and_displacement(world_acc_df, dt=0.01):
    """
    计算世界坐标系下的速度和位移
    
    Args:
        world_acc_df: 世界坐标系加速度数据
        dt: 时间步长，单位秒
        
    Returns:
        添加了速度和位移列的DataFrame
    """
    result_df = world_acc_df.copy()
    
    # 初始化速度和位移列
    for direction in ['x', 'y', 'z']:
        result_df[f'vel_{direction}'] = 0.0
        result_df[f'disp_{direction}'] = 0.0
    
    # 计算每个方向上的速度和位移
    for direction in ['x', 'y', 'z']:
        acc = result_df[direction].values
        vel = np.zeros_like(acc)
        disp = np.zeros_like(acc)
        
        for i in range(1, len(acc)):
            vel[i] = vel[i-1] + acc[i] * dt
            disp[i] = disp[i-1] + vel[i] * dt
        
        result_df[f'vel_{direction}'] = vel
        result_df[f'disp_{direction}'] = disp
    
    return result_df


def process_all_segments_ahrs(data_folder, output_folder=None, algorithm='madgwick', 
                             calculate_kinematics=False, sample_rate=100.0,
                             beta=0.1, kp=1.0, ki=0.3):
    """
    使用AHRS库算法处理文件夹中所有片段数据
    
    Args:
        data_folder: 存放片段数据的文件夹路径
        output_folder: 输出文件夹路径
        algorithm: 使用的算法 ('madgwick', 'mahony', 'simple')
        calculate_kinematics: 是否计算速度和位移
        sample_rate: 采样率 (Hz)
        beta: Madgwick算法的beta参数
        kp: Mahony算法的kp参数
        ki: Mahony算法的ki参数
    """
    if not check_ahrs_dependency() and algorithm in ['madgwick', 'mahony']:
        print("AHRS库不可用，无法继续处理")
        return
    
    if output_folder is None:
        output_folder = os.path.join(data_folder, f'ahrs_world_coordinates_{algorithm}')
    
    os.makedirs(output_folder, exist_ok=True)
    
    # 加载所有片段数据
    segment_data = load_segment_files(data_folder)
    
    if not segment_data:
        print(f"在文件夹 {data_folder} 中未找到任何片段数据")
        return
    
    print(f"发现 {len(segment_data)} 个片段，开始使用 AHRS-{algorithm.upper()} 算法处理...")
    if algorithm == 'madgwick':
        print(f"算法参数: beta={beta}")
    elif algorithm == 'mahony':
        print(f"算法参数: kp={kp}, ki={ki}")
    
    # 处理每个片段
    for segment_num in sorted(segment_data.keys()):
        print(f"\n{'='*60}")
        print(f"处理片段 {segment_num}...")
        
        # 处理数据
        world_acc_df = process_segment_data_ahrs(
            segment_data, segment_num, output_folder, 
            algorithm=algorithm, sample_rate=sample_rate,
            beta=beta, kp=kp, ki=ki
        )
        
        if world_acc_df is not None:
            # 可视化
            visualize_acceleration_ahrs(
                segment_data, segment_num, world_acc_df, output_folder, algorithm
            )
            
            # 如果需要，计算速度和位移
            if calculate_kinematics:
                print(f"计算片段 {segment_num} 的速度和位移...")
                
                # 推断采样率
                if len(world_acc_df) > 1:
                    try:
                        time_format = '%H-%M-%S.%f'
                        t1 = datetime.strptime(world_acc_df['time'].iloc[0], time_format)
                        t2 = datetime.strptime(world_acc_df['time'].iloc[-1], time_format)
                        total_time = (t2 - t1).total_seconds()
                        if total_time > 0:
                            sampling_rate = (len(world_acc_df) - 1) / total_time
                            dt = 1.0 / sampling_rate
                        else:
                            dt = 1.0 / sample_rate
                    except:
                        dt = 1.0 / sample_rate
                else:
                    dt = 1.0 / sample_rate
                
                print(f"使用采样时间步长: {dt:.6f}秒 (约 {1/dt:.1f} Hz)")
                
                # 计算速度和位移
                kinematics_df = calculate_velocity_and_displacement(world_acc_df, dt)
                
                # 提取时间戳
                acc_filename = os.path.basename(segment_data[segment_num]['acce'].name)
                match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
                
                if match:
                    timestamp = match.group(1)
                else:
                    timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
                
                # 保存运动学数据
                kinematics_file = os.path.join(output_folder, f'segment{segment_num}_{algorithm}_ahrs_kinematics_{timestamp}.csv')
                kinematics_df.to_csv(kinematics_file, index=False)
                print(f"已保存运动学数据: {kinematics_file}")
            
            print(f"片段 {segment_num} 处理完成")
    
    print(f"\n{'='*60}")
    print(f"所有片段处理完成！结果保存在: {output_folder}")
    print(f"使用算法: AHRS-{algorithm.upper()}")


def main():
    """主函数，提供交互式界面"""
    print("=" * 60)
    print("AHRS库加速度世界坐标系转换工具")
    print("使用标准AHRS库的 Madgwick 和 Mahony 算法")
    print("=" * 60)
    
    # 检查依赖
    if not check_ahrs_dependency():
        print("请先安装AHRS库后再运行此程序")
        return
    
    # 输入文件夹路径
    default_path = r"E:\Document\user001\Session_20250702_235136"
    folder_path = input(f"请输入数据文件夹路径 [默认: {default_path}]: ")
    folder_path = folder_path.strip() if folder_path.strip() else default_path
    
    # 确认文件夹存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹不存在: {folder_path}")
        return
    
    # 选择算法
    print("\n请选择姿态估计算法:")
    print("1. Madgwick算法 (AHRS库版本，推荐)")
    print("2. Mahony算法 (AHRS库版本，高精度)")
    print("3. 简单方法 (不使用AHRS库)")
    
    while True:
        choice = input("请输入选择 (1-3): ").strip()
        if choice == '1':
            algorithm = 'madgwick'
            break
        elif choice == '2':
            algorithm = 'mahony'
            break
        elif choice == '3':
            algorithm = 'simple'
            break
        else:
            print("无效选择，请重新输入")
    
    # 算法参数设置
    beta = 0.1
    kp = 1.0
    ki = 0.3
    
    if algorithm == 'madgwick':
        beta_input = input(f"请输入Madgwick算法beta参数 [默认: {beta}]: ").strip()
        try:
            beta = float(beta_input) if beta_input else beta
        except ValueError:
            print(f"无效输入，使用默认值: {beta}")
    
    elif algorithm == 'mahony':
        kp_input = input(f"请输入Mahony算法kp参数 [默认: {kp}]: ").strip()
        ki_input = input(f"请输入Mahony算法ki参数 [默认: {ki}]: ").strip()
        try:
            kp = float(kp_input) if kp_input else kp
            ki = float(ki_input) if ki_input else ki
        except ValueError:
            print(f"无效输入，使用默认值: kp={kp}, ki={ki}")
    
    # 采样率设置
    sample_rate_input = input("请输入采样率 [默认: 100 Hz]: ").strip()
    try:
        sample_rate = float(sample_rate_input) if sample_rate_input else 100.0
    except ValueError:
        sample_rate = 100.0
    
    # 输出文件夹
    output_folder = os.path.join(folder_path, f'ahrs_world_coordinates_{algorithm}')
    
    # 是否计算速度和位移
    calc_kinematics = input("是否计算速度和位移? (y/n) [默认: n]: ").lower().strip()
    calc_kinematics = calc_kinematics == 'y'
    
    print(f"\n开始处理...")
    print(f"使用算法: AHRS-{algorithm.upper()}")
    if algorithm == 'madgwick':
        print(f"算法参数: beta={beta}")
    elif algorithm == 'mahony':
        print(f"算法参数: kp={kp}, ki={ki}")
    print(f"采样率: {sample_rate} Hz")
    print(f"输出文件夹: {output_folder}")
    
    # 处理所有片段
    process_all_segments_ahrs(
        folder_path, 
        output_folder, 
        algorithm=algorithm,
        calculate_kinematics=calc_kinematics,
        sample_rate=sample_rate,
        beta=beta,
        kp=kp,
        ki=ki
    )


if __name__ == "__main__":
    main()
