import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
from interpolation import sort_and_clean_data

"""
人工确定时间计算位移
"""

def time_to_seconds(time_str):
    """将HH-MM-SS.mmm格式转换为秒数"""
    parts = time_str.split('.')
    time_parts = parts[0].split('-')
    hours = int(time_parts[0])
    minutes = int(time_parts[1])
    seconds = int(time_parts[2])
    microseconds = int(float('0.' + parts[1]) * 1000000)
    return hours * 3600 + minutes * 60 + seconds + microseconds / 1000000

def calculate_displacement(velocity_data, time_seconds):
    """通过速度积分计算位移"""
    dt = np.diff(time_seconds)
    dt = np.append(dt, dt[-1])
    displacement = np.zeros_like(velocity_data)
    
    # 使用积分法计算位移
    for i in range(1, len(velocity_data)):
        displacement[i] = displacement[i-1] + velocity_data[i] * dt[i-1] 
    
    return displacement

def process_segment_displacements(input_dir, plot_results=True):
    """批量处理分段数据并计算位移"""
    # 查找所有速度数据文件
    velocity_files = [f for f in os.listdir(input_dir) 
                     if f.startswith('acce_data') and f.endswith('_simple_gravity_removed.csv')]
    
    print(f"找到 {len(velocity_files)} 个速度数据文件")
    
    for file in velocity_files:
        input_path = os.path.join(input_dir, file)
        output_path = input_path.replace('_velocity.csv', '_displacement.csv')
        
        print(f"\n处理文件: {file}")
        
        # 读取速度数据
        df = pd.read_csv(input_path)
        
        # 计算相对时间（秒）
        time_seconds = np.array([time_to_seconds(t) for t in df['time']])
        time_seconds = time_seconds - time_seconds[0]
        
        # 计算三轴位移（厘米）
        disp_x = calculate_displacement(df['vel_x'].values, time_seconds)
        disp_y = calculate_displacement(df['vel_y'].values, time_seconds)
        disp_z = calculate_displacement(df['vel_z'].values, time_seconds)
        
        # 保存位移数据
        displacement_df = pd.DataFrame({
            'time': df['time'],
            'disp_x': disp_x,
            'disp_y': disp_y,
            'disp_z': disp_z
        })
        displacement_df.to_csv(output_path, index=False)
        print(f"已保存位移数据: {output_path}")
        
        # 绘制位移图
        if plot_results:
            plt.figure(figsize=(12, 8))
            
            plt.subplot(3, 1, 1)
            plt.plot(time_seconds, disp_x, label='X-axis')
            plt.title('X-axis Displacement')
            plt.ylabel('Displacement (cm)')
            plt.grid(True)
            plt.legend()
            
            plt.subplot(3, 1, 2)
            plt.plot(time_seconds, disp_y, label='Y-axis')
            plt.title('Y-axis Displacement')
            plt.ylabel('Displacement (cm)')
            plt.grid(True)
            plt.legend()
            
            plt.subplot(3, 1, 3)
            plt.plot(time_seconds, disp_z, label='Z-axis')
            plt.title('Z-axis Displacement')
            plt.xlabel('Time (s)')
            plt.ylabel('Displacement (cm)')
            plt.grid(True)
            plt.legend()
            
            plt.tight_layout()
            plt.show()
            
            # 打印最大位移信息
            print(f"\n最大位移:")
            print(f"X轴: {np.max(np.abs(disp_x)):.2f} cm")
            print(f"Y轴: {np.max(np.abs(disp_y)):.2f} cm")
            print(f"Z轴: {np.max(np.abs(disp_z)):.2f} cm")

def normalize_time_format(time_str):
    """标准化时间格式，确保小时为两位数"""
    parts = time_str.split('-')
    if len(parts[0]) == 1:
        time_str = f"0{time_str}"
    return time_str

def calculate_specific_displacement(file_path, start_time, end_time, plot_results=True):
    """计算指定时间范围内的位移"""
    print(f"处理文件: {file_path}")
    
    # 标准化时间格式
    start_time = normalize_time_format(start_time)
    end_time = normalize_time_format(end_time)
    print(f"时间范围: {start_time} 至 {end_time}")
    
    # 读取数据
    df = pd.read_csv(file_path)

    df = sort_and_clean_data(df)
    
    # 检查文件类型（是速度数据还是加速度数据）
    is_velocity_data = 'vel_x' in df.columns
    
    # 提取指定时间范围的数据
    mask = (df['time'] >= start_time) & (df['time'] <= end_time)
    segment_data = df.loc[mask].copy()
    
    if segment_data.empty:
        print("错误：未找到指定时间范围内的数据")
        return
    
    # 计算相对时间
    time_seconds = np.array([time_to_seconds(t) for t in segment_data['time']])
    time_seconds = time_seconds - time_seconds[0]
    
    # 根据输入数据类型选择处理方式
    if is_velocity_data:
        # 如果输入已经是速度数据
        vel_x = segment_data['vel_x'].values
        vel_y = segment_data['vel_y'].values
        vel_z = segment_data['vel_z'].values
    else:
        # 如果输入是加速度数据，需要先计算速度
        try:
            from calculate_velocity import calculate_velocity_axis
            segment_data = segment_data.rename(columns={'x': 'ax', 'y': 'ay', 'z': 'az'})
            vel_x, vel_y, vel_z = calculate_velocity_axis(segment_data, time_seconds)
        except ImportError:
            print("错误：未找到calculate_velocity模块")
            return
        except Exception as e:
            print(f"计算速度时发生错误：{str(e)}")
            return
    
    # 计算位移
    disp_x = calculate_displacement(vel_x, time_seconds)
    disp_y = calculate_displacement(vel_y, time_seconds)
    disp_z = calculate_displacement(vel_z, time_seconds)
    
    # 保存结果
    results_df = pd.DataFrame({
        'time': segment_data['time'],
        'vel_x': vel_x,
        'vel_y': vel_y,
        'vel_z': vel_z,
        'disp_x': disp_x,
        'disp_y': disp_y,
        'disp_z': disp_z
    })
    
    output_file = file_path.replace('.csv', f'_displacement_{start_time.replace(":", "-")}.csv')
    results_df.to_csv(output_file, index=False)
    print(f"已保存计算结果至: {output_file}")
    
    if plot_results:
        # 绘制速度图
        plt.figure(figsize=(12, 8))
        plt.subplot(3, 1, 1)
        plt.plot(time_seconds, vel_x, 'b-', label='X-axis')
        plt.title('X-axis Velocity')
        plt.ylabel('Velocity (cm/s)')
        plt.grid(True)
        plt.legend()
        
        plt.subplot(3, 1, 2)
        plt.plot(time_seconds, vel_y, 'g-', label='Y-axis')
        plt.title('Y-axis Velocity')
        plt.ylabel('Velocity (cm/s)')
        plt.grid(True)
        plt.legend()
        
        plt.subplot(3, 1, 3)
        plt.plot(time_seconds, vel_z, 'r-', label='Z-axis')
        plt.title('Z-axis Velocity')
        plt.xlabel('Time (s)')
        plt.ylabel('Velocity (cm/s)')
        plt.grid(True)
        plt.legend()
        
        plt.tight_layout()
        # plt.show()
        
        # 绘制位移图
        plt.figure(figsize=(12, 8))
        
        plt.subplot(3, 1, 1)
        plt.plot(time_seconds, disp_x, 'b-', label='X-axis')
        plt.title('X-axis Displacement')
        plt.ylabel('Displacement (cm)')
        plt.grid(True)
        plt.legend()
        
        plt.subplot(3, 1, 2)
        plt.plot(time_seconds, disp_y, 'g-', label='Y-axis')
        plt.title('Y-axis Displacement')
        plt.ylabel('Displacement (cm)')
        plt.grid(True)
        plt.legend()
        
        plt.subplot(3, 1, 3)
        plt.plot(time_seconds, disp_z, 'r-', label='Z-axis')
        plt.title('Z-axis Displacement')
        plt.xlabel('Time (s)')
        plt.ylabel('Displacement (cm)')
        plt.grid(True)
        plt.legend()
        
        plt.tight_layout()
        plt.show()
    
        # 打印速度分析结果
        print("\n速度分析结果:")
        print(f"X轴最大速度: {np.max(np.abs(vel_x)):.2f} cm/s")
        print(f"Y轴最大速度: {np.max(np.abs(vel_y)):.2f} cm/s")
        print(f"Z轴最大速度: {np.max(np.abs(vel_z)):.2f} cm/s")
    
    # 打印分析结果
    print("\n位移分析结果:")
    print(f"计算时间范围: {time_seconds[-1]:.3f} 秒")
    print(f"数据点数: {len(segment_data)}")

    final_x = disp_x[-1] 
    final_y = disp_y[-1]
    final_z = disp_z[-1]
    print("\n最终位移:")
    print(f"X轴: {final_x:.2f} cm")
    print(f"Y轴: {final_y:.2f} cm")
    print(f"Z轴: {final_z:.2f} cm")

if __name__ == "__main__":

    # 设置输入参数
    file_path = r"E:\Study\科研\data\平面估计\10\segment7_acce_22-22-45.411_kalman.csv"
    start_time = "22-22-47.541"  # 起始时间
    end_time = "22-22-48.190"    # 结束时间
    

    # 计算指定时间范围的位移
    calculate_specific_displacement(file_path, start_time, end_time, plot_results=True)
