#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
作者：
日期：2025年05月31日
功能：使用陀螺仪数据进行姿态解算，并基于姿态变化移除加速度数据中的重力分量
      可处理目录下的所有segment文件
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as R
from datetime import datetime
import re
import glob

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def load_segment_files(data_folder):
    """
    加载文件夹中所有segment的所有传感器数据文件
    
    Args:
        data_folder: 存放传感器数据的文件夹路径
        
    Returns:
        字典，包含每个片段的数据，格式为 {片段编号: {'acce': df_acc, 'grav': df_grav, 'gyro': df_gyro}}
    """
    # 查找所有传感器数据文件
    all_files = glob.glob(os.path.join(data_folder, "segment*_*.csv"))
    
    segment_data = {}
    # 正则表达式匹配文件名 - 格式为 segment数字_传感器类型_时间戳.csv
    pattern = r"segment(\d+)_(acce|grav|mag|gyro)_(\d+-\d+-\d+\.\d+)\.csv"
    
    for file_path in all_files:
        file_name = os.path.basename(file_path)
        match = re.match(pattern, file_name)
        
        if match:
            seg_num = int(match.group(1))
            sensor_type = match.group(2)
            timestamp = match.group(3)
            
            # 读取数据
            try:
                df = pd.read_csv(file_path)
                # 保存文件路径到DataFrame的元数据中，方便后续引用
                df.name = file_path
                
                # 初始化字典
                if seg_num not in segment_data:
                    segment_data[seg_num] = {}
                
                # 保存数据
                segment_data[seg_num][sensor_type] = df
                
                print(f"加载文件: {file_name}, 形状: {df.shape}")
            except Exception as e:
                print(f"加载文件 {file_name} 错误: {e}")
    
    return segment_data

def preprocess_time_data(df):
    """预处理时间数据，将时间字符串转换为浮点数秒"""
    if 'time' in df.columns:
        # 记录列结构用于调试
        print(f"数据列: {list(df.columns)}")
        print(f"数据示例:\n{df.head(2)}")
        
        df['time_seconds'] = df['time'].apply(time_to_seconds)
    return df

def time_to_seconds(time_str):
    """
    将HH-MM-SS.mmm格式转换为秒数
    
    Args:
        time_str: 时间字符串，格式为HH-MM-SS.mmm
        
    Returns:
        浮点数表示的秒数
    """
    # 首先检查输入是否为非字符串类型（比如NaN或None）
    if not isinstance(time_str, str):
        print(f"警告: 非字符串时间数据: {time_str}，类型: {type(time_str)}")
        return 0
    
    # 然后尝试解析时间字符串
    try:
        # 去除可能存在的空格
        time_str = time_str.strip()
        
        # 如果是带有毫秒的时间格式
        if '.' in time_str:
            time_parts, ms_part = time_str.split('.')
            ms = float('0.' + ms_part)  # 转换毫秒部分
        else:
            time_parts = time_str
            ms = 0
        
        # 分割时分秒
        hms_parts = time_parts.split('-')
        
        # 需要至少有3个部分（时、分、秒）
        if len(hms_parts) >= 3:
            hours = int(hms_parts[0])
            minutes = int(hms_parts[1])
            seconds = int(hms_parts[2])
            total_seconds = hours * 3600 + minutes * 60 + seconds + ms
            return total_seconds
        else:
            print(f"警告: 时间字符串格式不正确: {time_str}")
            return 0
    except Exception as e:
        print(f"警告: 解析时间字符串出错: {time_str}, 错误: {e}")
        return 0

def calculate_sampling_rate(df):
    """
    计算采样率和时间步长
    
    Args:
        df: 包含时间数据的DataFrame
        
    Returns:
        (采样率, 时间步长)
    """
    if 'time_seconds' in df.columns and len(df) > 1:
        # 计算相邻时间点的时间差
        time_diffs = np.diff(df['time_seconds'].values)
        
        # 过滤掉异常大的时间差（可能是由于数据中断导致的）
        valid_diffs = time_diffs[time_diffs < 1]  # 假设正常的时间差应该小于1秒
        
        if len(valid_diffs) > 0:
            avg_dt = np.mean(valid_diffs)
            avg_rate = 1 / avg_dt
            return avg_rate, avg_dt
    
    # 如果无法计算或数据不足，使用默认值
    print("警告: 无法计算采样率，使用默认值100Hz")
    return 100, 0.01

def quaternion_multiply(q1, q2):
    """
    四元数乘法
    
    Args:
        q1: 第一个四元数 [w, x, y, z]
        q2: 第二个四元数 [w, x, y, z]
        
    Returns:
        乘积四元数 [w, x, y, z]
    """
    w1, x1, y1, z1 = q1
    w2, x2, y2, z2 = q2
    
    w = w1 * w2 - x1 * x2 - y1 * y2 - z1 * z2
    x = w1 * x2 + x1 * w2 + y1 * z2 - z1 * y2
    y = w1 * y2 - x1 * z2 + y1 * w2 + z1 * x2
    z = w1 * z2 + x1 * y2 - y1 * x2 + z1 * w2
    
    return np.array([w, x, y, z])

def quaternion_from_angular_velocity(wx, wy, wz, dt):
    """
    从角速度计算增量四元数
    
    Args:
        wx, wy, wz: 三轴角速度
        dt: 时间步长
        
    Returns:
        增量四元数 [w, x, y, z]
    """
    # 计算旋转角度
    angle = np.sqrt(wx**2 + wy**2 + wz**2) * dt
    
    # 如果角度几乎为0，返回单位四元数
    if angle < 1e-8:
        return np.array([1, 0, 0, 0])
    
    # 计算旋转轴
    axis_x = wx / np.sqrt(wx**2 + wy**2 + wz**2)
    axis_y = wy / np.sqrt(wx**2 + wy**2 + wz**2)
    axis_z = wz / np.sqrt(wx**2 + wy**2 + wz**2)
    
    # 计算四元数
    half_angle = angle / 2
    sin_half_angle = np.sin(half_angle)
    
    qw = np.cos(half_angle)
    qx = axis_x * sin_half_angle
    qy = axis_y * sin_half_angle
    qz = axis_z * sin_half_angle
    
    return np.array([qw, qx, qy, qz])

def normalize_quaternion(q):
    """标准化四元数"""
    norm = np.sqrt(q[0]**2 + q[1]**2 + q[2]**2 + q[3]**2)
    if norm > 0:
        return q / norm
    return q

def integrate_gyro_quaternion(gyro_df, dt):
    """
    积分陀螺仪数据，计算姿态四元数序列
    
    Args:
        gyro_df: 陀螺仪数据DataFrame，包含x, y, z列
        dt: 时间步长
        
    Returns:
        四元数数组，Nx4，每行是一个四元数[w, x, y, z]
    """
    n = len(gyro_df)
    quaternions = np.zeros((n, 4))
    
    # 初始四元数为单位四元数
    q = np.array([1, 0, 0, 0])
    quaternions[0] = q
    
    for i in range(1, n):
        # 获取当前角速度
        wx = gyro_df.iloc[i]['x']
        wy = gyro_df.iloc[i]['y']
        wz = gyro_df.iloc[i]['z']
        
        # 计算增量四元数
        dq = quaternion_from_angular_velocity(wx, wy, wz, dt)
        
        # 更新姿态四元数
        q = quaternion_multiply(q, dq)
        
        # 标准化四元数
        q = normalize_quaternion(q)
        
        # 存储结果
        quaternions[i] = q
    
    return quaternions

def rotate_vector_by_quaternion(v, q):
    """
    使用四元数旋转向量
    
    Args:
        v: 3D向量
        q: 四元数 [w, x, y, z]
        
    Returns:
        旋转后的向量
    """
    qw, qx, qy, qz = q
    
    # 构造用于旋转的矩阵
    rotation_matrix = np.array([
        [1 - 2*qy**2 - 2*qz**2,     2*qx*qy - 2*qz*qw,     2*qx*qz + 2*qy*qw],
        [    2*qx*qy + 2*qz*qw, 1 - 2*qx**2 - 2*qz**2,     2*qy*qz - 2*qx*qw],
        [    2*qx*qz - 2*qy*qw,     2*qy*qz + 2*qx*qw, 1 - 2*qx**2 - 2*qy**2]
    ])
    
    # 执行旋转
    return np.dot(rotation_matrix, v)

def compensate_gravity_with_gyro(segment_data, segment_num, output_folder):
    """
    使用陀螺仪姿态解算，并补偿加速度数据中的重力
    
    Args:
        segment_data: 片段数据字典
        segment_num: 片段编号
        output_folder: 输出文件夹
        
    Returns:
        重力补偿后的加速度数据DataFrame
    """
    if not all(key in segment_data for key in ['acce', 'grav', 'gyro']):
        print(f"片段 {segment_num} 缺少必要的数据 (加速度/重力/陀螺仪)，跳过")
        return None
    
    acc_df = segment_data['acce'].copy()
    grav_df = segment_data['grav'].copy()
    gyro_df = segment_data['gyro'].copy()
    
    # 预处理时间数据
    acc_df = preprocess_time_data(acc_df)
    grav_df = preprocess_time_data(grav_df)
    gyro_df = preprocess_time_data(gyro_df)
    
    # 检查数据长度
    print(f"片段 {segment_num} - 加速度数据长度: {len(acc_df)}, 陀螺仪数据长度: {len(gyro_df)}, 重力数据长度: {len(grav_df)}")
    
    # 确保数据长度一致
    min_length = min(len(acc_df), len(gyro_df), len(grav_df))
    if min_length < len(acc_df):
        print(f"注意: 截断加速度数据从 {len(acc_df)} 到 {min_length}")
        acc_df = acc_df.iloc[:min_length]
    if min_length < len(gyro_df):
        print(f"注意: 截断陀螺仪数据从 {len(gyro_df)} 到 {min_length}")
        gyro_df = gyro_df.iloc[:min_length]
    if min_length < len(grav_df):
        print(f"注意: 截断重力数据从 {len(grav_df)} 到 {min_length}")
        grav_df = grav_df.iloc[:min_length]
    
    # 计算采样率
    sampling_rate, dt = calculate_sampling_rate(gyro_df)
    print(f"片段 {segment_num} - 陀螺仪采样率: {sampling_rate:.2f} Hz, 时间步长: {dt:.6f}秒")
    
    # 进行陀螺仪积分，获取四元数序列
    print(f"片段 {segment_num} - 计算姿态四元数...")
    quaternions = integrate_gyro_quaternion(gyro_df, dt)
    
    # 获取初始重力向量（第一个有效数据点）
    initial_gravity = np.array([grav_df.iloc[0]['x'], grav_df.iloc[0]['y'], grav_df.iloc[0]['z']])
    print(f"片段 {segment_num} - 初始重力向量: {initial_gravity}")
    
    # 创建结果DataFrame
    result_df = pd.DataFrame()
    result_df['time'] = acc_df['time'].values
    
    # 旋转重力向量并进行补偿
    print(f"片段 {segment_num} - 进行重力补偿...")
    gravity_vectors = np.zeros((len(acc_df), 3))
    gravity_compensated_acc = np.zeros((len(acc_df), 3))
    
    for i in range(len(acc_df)):
        # 获取当前加速度
        acc_current = np.array([acc_df.iloc[i]['x'], acc_df.iloc[i]['y'], acc_df.iloc[i]['z']])
        
        # 确保索引不会超出四元数数组的范围
        if i < len(quaternions):
            q_current = quaternions[i]
        else:
            # 如果索引超出范围，使用最后一个有效的四元数
            q_current = quaternions[-1]
            print(f"警告: 索引 {i} 超出四元数数组范围 {len(quaternions)}，使用最后一个有效四元数")
        
        # 使用当前姿态旋转初始重力向量，得到当前姿态下的重力方向
        rotated_gravity = rotate_vector_by_quaternion(initial_gravity, q_current)
        
        # 存储重力向量
        gravity_vectors[i] = rotated_gravity
        
        # 从加速度中减去重力分量
        compensated_acc = acc_current - rotated_gravity
        
        # 存储结果
        gravity_compensated_acc[i] = compensated_acc      # 只添加重力补偿后的三轴加速度数据
    
    # 确保补偿后的加速度数据长度与DataFrame长度一致
    data_length = min(len(gravity_compensated_acc), len(result_df))
    if data_length < len(result_df):
        print(f"警告: 截断结果DataFrame从 {len(result_df)} 到 {data_length}")
        result_df = result_df.iloc[:data_length].copy()
    
    result_df['x'] = gravity_compensated_acc[:data_length, 0]
    result_df['y'] = gravity_compensated_acc[:data_length, 1]
    result_df['z'] = gravity_compensated_acc[:data_length, 2]

    # 从原始加速度文件名中提取时间戳部分
    acc_filename = os.path.basename(acc_df.name) if hasattr(acc_df, 'name') else ""
    print(f"片段 {segment_num} - 提取时间戳的加速度文件名: {acc_filename}")
    match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
    
    if match:
        timestamp = match.group(1)
        print(f"片段 {segment_num} - 成功提取时间戳: {timestamp}")
    else:
        # 尝试使用数据中第一行的时间作为时间戳
        if 'time' in acc_df.columns and len(acc_df) > 0:
            first_time = acc_df['time'].iloc[0]
            timestamp = first_time.replace(':', '-') if isinstance(first_time, str) else datetime.now().strftime('%H-%M-%S.%f')[:-3]
            print(f"片段 {segment_num} - 无法从文件名提取时间戳，使用数据第一行时间: {timestamp}")
        else:
            timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
            print(f"片段 {segment_num} - 无法从文件名提取时间戳，且数据中无时间列，使用当前时间: {timestamp}")
    
    # 保存处理后的数据 - 使用与原始加速度文件相同的命名格式
    output_file = os.path.join(output_folder, f'segment{segment_num}_acce_{timestamp}.csv')
    result_df.to_csv(output_file, index=False)
    print(f"已保存片段 {segment_num} 重力补偿后的加速度数据: {output_file}")
    
    return result_df

def visualize_gravity_compensation(original_acc, compensated_acc, segment_num, output_folder):
    """
    可视化原始加速度和重力补偿后的加速度
    
    Args:
        original_acc: 原始加速度DataFrame
        compensated_acc: 重力补偿后的加速度DataFrame
        segment_num: 片段编号
        output_folder: 输出文件夹
    """
    # 确保数据长度一致
    min_length = min(len(original_acc), len(compensated_acc))
    if min_length < len(original_acc):
        print(f"可视化: 截断原始加速度数据从 {len(original_acc)} 到 {min_length}")
        original_acc = original_acc.iloc[:min_length].copy()
    if min_length < len(compensated_acc):
        print(f"可视化: 截断补偿后加速度数据从 {len(compensated_acc)} 到 {min_length}")
        compensated_acc = compensated_acc.iloc[:min_length].copy()
    
    # 创建时间轴
    if 'time_seconds' in original_acc.columns:
        time_seconds = original_acc['time_seconds'].values
    else:
        time_seconds = np.arange(len(original_acc)) / 100  # 假设采样率为100Hz
    
    # 确保时间轴长度与数据长度一致
    time_seconds = time_seconds[:min_length]
    
    # 绘制原始加速度和重力补偿后的加速度
    plt.figure(figsize=(12, 8))
    
    # 原始加速度
    plt.subplot(2, 1, 1)
    plt.plot(time_seconds, original_acc['x'], 'r-', label='X轴')
    plt.plot(time_seconds, original_acc['y'], 'g-', label='Y轴')
    plt.plot(time_seconds, original_acc['z'], 'b-', label='Z轴')
    plt.title(f'片段 {segment_num} - 原始加速度')
    plt.xlabel('时间 (秒)')
    plt.ylabel('加速度 (m/s²)')
    plt.grid(True)
    plt.legend()
    
    # 重力补偿后的加速度
    plt.subplot(2, 1, 2)
    plt.plot(time_seconds, compensated_acc['x'], 'r-', label='X轴')
    plt.plot(time_seconds, compensated_acc['y'], 'g-', label='Y轴')
    plt.plot(time_seconds, compensated_acc['z'], 'b-', label='Z轴')
    plt.title(f'片段 {segment_num} - 重力补偿后的加速度')
    plt.xlabel('时间 (秒)')
    plt.ylabel('加速度 (m/s²)')
    plt.grid(True)
    plt.legend()
    
    plt.tight_layout()
    
    # 提取时间戳，使用与原始加速度数据相同的时间戳
    acc_filename = os.path.basename(original_acc.name) if hasattr(original_acc, 'name') else ""
    match = re.search(r'segment\d+_acce_(\d+-\d+-\d+\.\d+)\.csv', acc_filename)
    
    if match:
        timestamp = match.group(1)
        print(f"可视化：成功提取时间戳: {timestamp}")
    else:
        # 尝试使用加速度数据中第一行的时间作为时间戳
        if 'time' in original_acc.columns and len(original_acc) > 0:
            first_time = original_acc['time'].iloc[0]
            timestamp = first_time.replace(':', '-') if isinstance(first_time, str) else datetime.now().strftime('%H-%M-%S.%f')[:-3]
        else:
            timestamp = datetime.now().strftime('%H-%M-%S.%f')[:-3]
    
    output_file = os.path.join(output_folder, f'segment{segment_num}_acceleration_comparison_{timestamp}.png')
    plt.savefig(output_file, dpi=300)
    plt.close()
    print(f"已保存片段 {segment_num} 的可视化图表: {output_file}")

def get_segment_number_from_filename(filename):
    """
    从文件名中提取片段编号
    
    Args:
        filename: 文件名
        
    Returns:
        片段编号，如果无法提取则返回None
    """
    match = re.search(r'segment(\d+)_', filename)
    if match:
        return int(match.group(1))
    return None

def process_all_segments(data_folder, output_folder=None):
    """
    处理文件夹中所有片段数据
    
    Args:
        data_folder: 存放片段数据的文件夹路径
        output_folder: 输出文件夹路径，如果为None则使用数据文件夹/world
    """
    if output_folder is None:
        output_folder = os.path.join(data_folder, 'world')
    
    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)
    
    # 加载所有片段数据
    segment_data = load_segment_files(data_folder)
    
    if not segment_data:
        print(f"在文件夹 {data_folder} 中未找到任何片段数据")
        return
    
    print(f"发现 {len(segment_data)} 个片段，开始处理...")
    
    # 处理每个片段
    for segment_num in sorted(segment_data.keys()):
        print(f"\n处理片段 {segment_num}...")
        
        # 检查是否有必要的数据
        if not all(key in segment_data[segment_num] for key in ['acce', 'grav', 'gyro']):
            print(f"片段 {segment_num} 缺少必要的数据 (加速度/重力/陀螺仪)，跳过")
            continue
        
        # 处理数据
        compensated_df = compensate_gravity_with_gyro(segment_data[segment_num], segment_num, output_folder)
        
        if compensated_df is not None:
            # 可视化
            visualize_gravity_compensation(segment_data[segment_num]['acce'], compensated_df, segment_num, output_folder)
            print(f"片段 {segment_num} 处理完成")
    
    print("\n所有片段处理完成!")

if __name__ == "__main__":
    print("=" * 50)
    print("陀螺仪姿态解算与重力补偿工具")
    print("=" * 50)
    
    # 输入文件夹路径
    default_path = r"E:\Document\user001\5.31\dudu\Session_20250531_193247 - 副本"
    folder_path = input(f"请输入数据文件夹路径 [默认: {default_path}]: ")
    folder_path = folder_path.strip() if folder_path.strip() else default_path
    
    # 确认文件夹存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹不存在: {folder_path}")
        exit(1)
    
    # 输出文件夹设置为 world
    output_folder = os.path.join(folder_path, 'world')
    
    # 处理所有片段数据
    process_all_segments(folder_path, output_folder)
