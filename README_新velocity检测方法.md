# 新的Velocity检测方法

## 概述

为velocity方法新增了两种截止时间检测方式，现在总共有三种velocity检测方法可供选择：

1. **`velocity`**: 三轴合速度矢量方法（原有方法）
2. **`velocity_individual`**: 三轴独立检测方法（新增）
3. **`velocity_main_secondary`**: 主轴+副轴合成方法（新增）

## 方法详解

### 1. 三轴独立检测方法 (`velocity_individual`)

#### 核心思路
分别检测X、Y、Z三轴的最大速度时间，选取最早的时间作为整个运动的velocity检测时间。

#### 算法流程
```python
def _find_acceleration_end_by_individual_axes(self, velocity_x, velocity_y, velocity_z, start_idx, min_duration_sec=0.2):
    # 1. 分别检测三轴的减小点和最大速度时间
    for axis_name, velocity_axis in [('X', velocity_x), ('Y', velocity_y), ('Z', velocity_z)]:
        decrease_point = self._find_first_velocity_decrease_single_axis(velocity_axis, min_samples, axis_name)
        max_velocity_time = find_max_before_decrease(velocity_axis, decrease_point, min_samples)
        axis_end_times.append((max_velocity_time, axis_name))
    
    # 2. 选择最早的时间
    earliest_time = min(axis_end_times, key=lambda x: x[0])
    return earliest_time
```

#### 适用场景
- **多轴协调运动**: 不同轴有不同的峰值时间
- **早期终止检测**: 需要捕捉最早的运动结束信号
- **轴间差异明显**: 各轴运动模式差异较大

### 2. 主轴+副轴合成方法 (`velocity_main_secondary`)

#### 核心思路
计算主运动轴，将剩余两轴速度合成，分别计算主运动轴和副轴合成速度的最大速度时间，选取较早的时间。

#### 算法流程
```python
def _find_acceleration_end_by_main_secondary_axes(self, velocity_x, velocity_y, velocity_z, main_axis, start_idx, min_duration_sec=0.2):
    # 1. 确定主轴和副轴
    main_velocity = axis_velocities[main_axis]
    secondary_velocities = [other two axes]
    secondary_magnitude = sqrt(secondary_velocities[0]² + secondary_velocities[1]²)
    
    # 2. 分别检测主轴和副轴合成的最大速度时间
    main_end_time = find_max_velocity_time(main_velocity)
    secondary_end_time = find_max_velocity_time(secondary_magnitude)
    
    # 3. 选择较早的时间
    return min(main_end_time, secondary_end_time)
```

#### 适用场景
- **主轴明确**: 有明显的主运动方向
- **主副分离**: 需要区分主要运动和次要运动
- **运动层次化**: 主要运动和辅助运动有不同的时间特征

## 使用方法

### 1. 基本调用

```python
from motion_event_detector import MotionEventDetector

detector = MotionEventDetector(sample_rate=100)

# 方法1: 三轴合速度矢量（原方法）
result1 = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='velocity'
)

# 方法2: 三轴独立检测（新方法）
result2 = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='velocity_individual'
)

# 方法3: 主轴+副轴合成（新方法）
result3 = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='velocity_main_secondary'
)
```

### 2. 测试验证

```bash
python test_new_velocity_methods.py
```

## 技术实现

### 1. 单轴减小点检测

```python
def _find_first_velocity_decrease_single_axis(self, velocity_axis, min_samples, axis_name):
    # 使用滑动窗口检测速度减小
    window_size = max(3, self.sample_rate // 50)
    
    for i in range(min_samples, len(velocity_axis) - window_size):
        current_velocity = abs(velocity_axis[i])
        future_velocity = np.mean(np.abs(velocity_axis[i+1:i+1+window_size]))
        
        # 检查是否开始减小（减小超过5%）
        if future_velocity < current_velocity * 0.95:
            if self._verify_velocity_decrease_single_axis(velocity_axis, i, window_size):
                return i
    
    return None
```

### 2. 显著性验证

```python
# 检查最大值是否足够显著
max_abs_velocity = np.max(np.abs(velocity_axis))
if abs(max_value) >= 0.1 * max_abs_velocity:  # 至少10%的最大速度
    # 认为是有效的峰值
    return max_idx
```

### 3. 主轴确定

```python
# 基于方差确定主运动轴
x_var = np.var(processed_data[:, 0])
z_var = np.var(processed_data[:, 2])
main_axis = 0 if x_var > z_var else 2  # X轴或Z轴
```

## 方法对比

### 检测结果对比

| 方法 | 优势 | 适用场景 | 检测特点 |
|------|------|----------|----------|
| `velocity` | 整体运动考虑 | 三轴协调运动 | 合成速度峰值 |
| `velocity_individual` | 最早终止检测 | 轴间差异明显 | 最早轴峰值 |
| `velocity_main_secondary` | 主副分离 | 主轴明确 | 主副轴较早值 |

### 性能特点

```
场景示例:
X轴峰值: 1.0s
Y轴峰值: 0.8s (最早)
Z轴峰值: 1.3s

velocity: 可能选择1.1s (合成峰值)
velocity_individual: 选择0.8s (Y轴最早)
velocity_main_secondary: 选择0.9s (主轴或副轴较早)
```

## 调试信息

### 1. 三轴独立检测

```
三轴独立检测最小加速持续时间: 0.2秒 (20个样本)
分别检测X、Y、Z三轴，选取最早的最大速度时间

检测X轴:
  X轴减小点: 索引100, 时间1.000s
  X轴最大速度点: 索引80, 时间0.800s, 速度1.234

检测Y轴:
  Y轴减小点: 索引70, 时间0.700s
  Y轴最大速度点: 索引60, 时间0.600s, 速度0.987

检测Z轴:
  Z轴减小点: 索引120, 时间1.200s
  Z轴最大速度点: 索引110, 时间1.100s, 速度1.567

选择最早的轴: Y轴
最早时间: 索引60, 持续时间0.600s, 速度0.987
```

### 2. 主轴+副轴合成检测

```
主轴+副轴合成检测最小加速持续时间: 0.2秒 (20个样本)
主运动轴: X轴
副轴: Y轴 + Z轴

检测主轴(X轴):
  主轴减小点: 索引100, 时间1.000s
  主轴最大速度点: 索引80, 时间0.800s, 速度1.234

检测副轴合成(Y+Z):
  副轴合成减小点: 索引90, 时间0.900s
  副轴合成最大速度点: 索引75, 时间0.750s, 速度1.123

选择较早的时间: 副轴(Y+Z)
最早时间: 索引75, 持续时间0.750s
```

## 参数配置

### 1. 检测参数

```python
min_duration_sec = 0.2      # 最小持续时间
decrease_threshold = 0.95   # 减小阈值（95%）
significance_threshold = 0.1 # 显著性阈值（10%最大速度）
stability_threshold = 0.6   # 稳定性阈值（60%一致性）
```

### 2. 窗口设置

```python
window_size = max(3, self.sample_rate // 50)  # 约0.02秒的检测窗口
verify_window = window_size * 3               # 验证窗口大小
```

## 故障排除

### 1. 检测失败

**问题**: 所有轴都检测失败
**解决**: 
- 检查数据质量和采样率
- 降低显著性阈值
- 调整最小持续时间

### 2. 检测结果异常

**问题**: 检测时间过早或过晚
**解决**:
- 调整减小阈值
- 检查轴的选择逻辑
- 验证运动模式

### 3. 主轴判断错误

**问题**: 主轴选择不正确
**解决**:
- 检查方差计算
- 手动指定主轴
- 使用三轴独立方法

## 选择建议

### 1. 推荐使用场景

- **`velocity`**: 标准的三轴协调运动
- **`velocity_individual`**: 需要最早终止检测的场景
- **`velocity_main_secondary`**: 有明确主运动方向的场景

### 2. 性能对比

```python
# 测试不同场景的适用性
scenarios = [
    ("X轴主导", "velocity_main_secondary"),
    ("多轴均匀", "velocity"),
    ("早期终止", "velocity_individual"),
    ("复杂运动", "velocity")
]
```

## 更新日志

- **v1.6**: 添加三轴独立检测和主轴+副轴合成检测方法
- **v1.5**: 第一次减小前最大速度检测方法
- **v1.4**: 三轴合速度矢量检测方法

现在有三种velocity检测方法，可以根据具体的运动特征和需求选择最适合的方法！
