# -*- coding:utf-8 -*-
"""
测试三轴合速度矢量检测方法
验证使用三轴速度合成的合速度矢量来判断最大速度时间的功能
"""

import numpy as np
import matplotlib.pyplot as plt
from motion_event_detector import MotionEventDetector

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def create_three_axis_motion_data(sample_rate=100):
    """
    创建三轴运动数据，每个轴都有不同的运动模式
    模拟真实的三维运动情况
    """
    duration = 3.0  # 3秒数据
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # X轴：主要的前后运动（加速-减速）
    acc_x = np.zeros_like(t)
    mask_x_accel = (t >= 0.2) & (t < 1.0)
    mask_x_decel = (t >= 1.0) & (t < 2.0)
    
    acc_x[mask_x_accel] = 2.0 * np.sin(np.pi * (t[mask_x_accel] - 0.2) / 0.8)
    acc_x[mask_x_decel] = -1.5 * np.sin(np.pi * (t[mask_x_decel] - 1.0) / 1.0)
    
    # Y轴：侧向运动（较小幅度）
    acc_y = np.zeros_like(t)
    mask_y = (t >= 0.3) & (t < 1.5)
    acc_y[mask_y] = 0.8 * np.sin(2 * np.pi * (t[mask_y] - 0.3) / 1.2)
    
    # Z轴：垂直运动（中等幅度）
    acc_z = np.zeros_like(t)
    mask_z = (t >= 0.4) & (t < 1.8)
    acc_z[mask_z] = 1.2 * np.sin(np.pi * (t[mask_z] - 0.4) / 1.4)
    
    # 添加噪声
    noise_level = 0.05
    acc_x += noise_level * np.random.normal(0, 1, len(t))
    acc_y += noise_level * np.random.normal(0, 1, len(t))
    acc_z += noise_level * np.random.normal(0, 1, len(t))
    
    # 组合三轴数据
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    return acc_data, t

def test_single_axis_vs_three_axis():
    """
    对比单轴检测和三轴合成检测的结果
    """
    print("=== 对比单轴检测 vs 三轴合成检测 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_three_axis_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    print("1. 使用三轴合速度矢量检测:")
    print("-" * 40)
    
    # 使用三轴合速度矢量检测
    start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
        acc_data, only_acceleration_phase=True, use_integration=True, detection_method='velocity'
    )
    
    three_axis_duration = (end_idx - start_idx) / sample_rate
    print(f"  起始时间: {start_idx/sample_rate:.3f}s")
    print(f"  结束时间: {end_idx/sample_rate:.3f}s")
    print(f"  持续时间: {three_axis_duration:.3f}s")
    print(f"  置信度: {confidence:.3f}")
    
    # 模拟原来的单轴检测方法
    print("\n2. 模拟单轴检测方法:")
    print("-" * 40)
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    
    # 确定主运动轴
    x_var = np.var(processed_data[:, 0])
    z_var = np.var(processed_data[:, 2])
    main_axis = 0 if x_var > z_var else 2
    main_axis_name = 'X' if main_axis == 0 else 'Z'
    
    print(f"  主运动轴: {main_axis_name}轴 (方差: {x_var:.4f} vs {z_var:.4f})")
    
    # 单轴检测
    main_axis_data = processed_data[:, main_axis]
    window = 5
    smoothed_data = np.convolve(main_axis_data, np.ones(window)/window, mode='same')
    
    # 找起始点
    threshold = 0.1 * np.max(np.abs(smoothed_data))
    start_candidates = np.where(np.abs(smoothed_data) > threshold)[0]
    single_start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    # 计算单轴速度
    dt = 1.0 / sample_rate
    single_velocity = np.cumsum(smoothed_data[single_start_idx:]) * dt
    
    # 找单轴速度最大点
    max_vel_idx = np.argmax(np.abs(single_velocity))
    single_end_idx = single_start_idx + max_vel_idx
    single_duration = (single_end_idx - single_start_idx) / sample_rate
    
    print(f"  起始时间: {single_start_idx/sample_rate:.3f}s")
    print(f"  结束时间: {single_end_idx/sample_rate:.3f}s")
    print(f"  持续时间: {single_duration:.3f}s")
    
    print("\n3. 结果对比:")
    print("-" * 40)
    print(f"三轴合成方法持续时间: {three_axis_duration:.3f}s")
    print(f"单轴检测方法持续时间: {single_duration:.3f}s")
    print(f"差异: {abs(three_axis_duration - single_duration):.3f}s")
    
    return acc_data, time_axis, start_idx, end_idx, single_start_idx, single_end_idx

def analyze_velocity_components():
    """
    分析三轴速度分量和合成速度的关系
    """
    print("\n=== 分析三轴速度分量 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_three_axis_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    
    # 平滑处理
    window = 5
    smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
    smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
    smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
    
    # 找起始点
    combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
    threshold = 0.1 * np.max(combined_acc)
    start_candidates = np.where(combined_acc > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    # 计算三轴速度
    dt = 1.0 / sample_rate
    velocity_x = np.cumsum(smoothed_x[start_idx:]) * dt
    velocity_y = np.cumsum(smoothed_y[start_idx:]) * dt
    velocity_z = np.cumsum(smoothed_z[start_idx:]) * dt
    
    # 计算合速度矢量
    velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)
    
    # 分析各轴贡献
    max_vel_x = np.max(np.abs(velocity_x))
    max_vel_y = np.max(np.abs(velocity_y))
    max_vel_z = np.max(np.abs(velocity_z))
    max_vel_mag = np.max(velocity_magnitude)
    
    print(f"各轴最大速度:")
    print(f"  X轴: {max_vel_x:.3f} m/s ({max_vel_x/max_vel_mag*100:.1f}%)")
    print(f"  Y轴: {max_vel_y:.3f} m/s ({max_vel_y/max_vel_mag*100:.1f}%)")
    print(f"  Z轴: {max_vel_z:.3f} m/s ({max_vel_z/max_vel_mag*100:.1f}%)")
    print(f"  合成: {max_vel_mag:.3f} m/s (100.0%)")
    
    # 找各轴和合成速度的峰值时间
    peak_time_x = (start_idx + np.argmax(np.abs(velocity_x))) / sample_rate
    peak_time_y = (start_idx + np.argmax(np.abs(velocity_y))) / sample_rate
    peak_time_z = (start_idx + np.argmax(np.abs(velocity_z))) / sample_rate
    peak_time_mag = (start_idx + np.argmax(velocity_magnitude)) / sample_rate
    
    print(f"\n各轴峰值时间:")
    print(f"  X轴峰值时间: {peak_time_x:.3f}s")
    print(f"  Y轴峰值时间: {peak_time_y:.3f}s")
    print(f"  Z轴峰值时间: {peak_time_z:.3f}s")
    print(f"  合成峰值时间: {peak_time_mag:.3f}s")
    
    return velocity_x, velocity_y, velocity_z, velocity_magnitude, start_idx

def visualize_three_axis_comparison():
    """
    可视化三轴检测对比
    """
    print("\n=== 生成三轴检测对比图 ===")
    
    # 获取测试数据和结果
    acc_data, time_axis, three_start, three_end, single_start, single_end = test_single_axis_vs_three_axis()
    velocity_x, velocity_y, velocity_z, velocity_magnitude, start_idx = analyze_velocity_components()
    
    sample_rate = 100
    
    # 创建可视化
    fig, axes = plt.subplots(4, 1, figsize=(14, 12))
    
    # 子图1: 三轴加速度数据
    axes[0].plot(time_axis, acc_data[:, 0], 'r-', label='X轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 1], 'g-', label='Y轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 2], 'b-', label='Z轴加速度', alpha=0.8)
    
    # 标记检测结果
    axes[0].axvspan(three_start/sample_rate, three_end/sample_rate, color='purple', alpha=0.3, label='三轴合成检测')
    axes[0].axvspan(single_start/sample_rate, single_end/sample_rate, color='orange', alpha=0.3, label='单轴检测')
    
    axes[0].set_title('三轴加速度数据与检测结果对比')
    axes[0].set_ylabel('加速度 (m/s²)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 子图2: 三轴速度分量
    time_vel = np.arange(start_idx, start_idx + len(velocity_x)) / sample_rate
    axes[1].plot(time_vel, velocity_x, 'r-', label='X轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_y, 'g-', label='Y轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_z, 'b-', label='Z轴速度', linewidth=2)
    
    axes[1].axvspan(three_start/sample_rate, three_end/sample_rate, color='purple', alpha=0.3)
    axes[1].axvspan(single_start/sample_rate, single_end/sample_rate, color='orange', alpha=0.3)
    
    axes[1].set_title('三轴速度分量')
    axes[1].set_ylabel('速度 (m/s)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 子图3: 合速度矢量对比
    axes[2].plot(time_vel, velocity_magnitude, 'purple', label='三轴合速度矢量', linewidth=3)
    
    # 标记峰值点
    max_mag_idx = np.argmax(velocity_magnitude)
    max_mag_time = time_vel[max_mag_idx]
    max_mag_value = velocity_magnitude[max_mag_idx]
    axes[2].plot(max_mag_time, max_mag_value, 'o', color='purple', markersize=10, label='合速度峰值')
    
    axes[2].axvspan(three_start/sample_rate, three_end/sample_rate, color='purple', alpha=0.3, label='三轴合成检测')
    axes[2].axvspan(single_start/sample_rate, single_end/sample_rate, color='orange', alpha=0.3, label='单轴检测')
    
    axes[2].set_title('三轴合速度矢量')
    axes[2].set_ylabel('合速度 (m/s)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    # 子图4: 检测结果总结
    axes[3].text(0.05, 0.8, '检测结果对比:', fontsize=12, fontweight='bold')
    axes[3].text(0.05, 0.7, f'三轴合成方法: {three_start/sample_rate:.3f}s - {three_end/sample_rate:.3f}s', 
                fontsize=10, color='purple')
    axes[3].text(0.05, 0.6, f'单轴检测方法: {single_start/sample_rate:.3f}s - {single_end/sample_rate:.3f}s', 
                fontsize=10, color='orange')
    
    three_duration = (three_end - three_start) / sample_rate
    single_duration = (single_end - single_start) / sample_rate
    
    axes[3].text(0.05, 0.4, '优势分析:', fontsize=12, fontweight='bold')
    axes[3].text(0.05, 0.3, '• 三轴合成考虑了所有方向的运动', fontsize=10)
    axes[3].text(0.05, 0.2, '• 更全面地反映整体运动状态', fontsize=10)
    axes[3].text(0.05, 0.1, f'• 持续时间差异: {abs(three_duration - single_duration):.3f}s', fontsize=10)
    
    axes[3].set_xlim(0, 1)
    axes[3].set_ylim(0, 1)
    axes[3].axis('off')
    
    plt.tight_layout()
    plt.savefig('three_axis_velocity_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("对比图已保存为: three_axis_velocity_comparison.png")

def test_different_motion_patterns():
    """
    测试不同运动模式下的三轴检测效果
    """
    print("\n=== 测试不同运动模式 ===")
    
    patterns = [
        ("单轴运动", lambda t: (2*np.sin(np.pi*t), 0*t, 0*t)),
        ("双轴运动", lambda t: (2*np.sin(np.pi*t), 1*np.cos(np.pi*t), 0*t)),
        ("三轴运动", lambda t: (2*np.sin(np.pi*t), 1*np.cos(np.pi*t), 0.8*np.sin(2*np.pi*t))),
        ("复杂运动", lambda t: (2*np.sin(np.pi*t), 1.5*np.sin(1.5*np.pi*t), 1.2*np.sin(0.8*np.pi*t)))
    ]
    
    sample_rate = 100
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    print("运动模式        三轴方法持续时间  主轴方差比    检测优势")
    print("-" * 60)
    
    for pattern_name, pattern_func in patterns:
        # 创建测试数据
        duration = 2.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        t_motion = t[(t >= 0.2) & (t <= 1.2)]  # 运动时间段
        
        acc_x, acc_y, acc_z = pattern_func(t_motion - 0.2)
        
        # 创建完整的加速度数组
        full_acc_x = np.zeros_like(t)
        full_acc_y = np.zeros_like(t)
        full_acc_z = np.zeros_like(t)
        
        motion_mask = (t >= 0.2) & (t <= 1.2)
        full_acc_x[motion_mask] = acc_x
        full_acc_y[motion_mask] = acc_y
        full_acc_z[motion_mask] = acc_z
        
        # 添加噪声
        noise = 0.05
        full_acc_x += noise * np.random.normal(0, 1, len(t))
        full_acc_y += noise * np.random.normal(0, 1, len(t))
        full_acc_z += noise * np.random.normal(0, 1, len(t))
        
        acc_data = np.column_stack([full_acc_x, full_acc_y, full_acc_z])
        
        try:
            # 使用三轴方法检测
            start_idx, end_idx, confidence, pattern_result = detector.detect_single_motion_event(
                acc_data, only_acceleration_phase=True, use_integration=True, detection_method='velocity'
            )
            
            duration_detected = (end_idx - start_idx) / sample_rate
            
            # 计算主轴方差比
            processed_data = detector.preprocess_data(acc_data)
            x_var = np.var(processed_data[:, 0])
            z_var = np.var(processed_data[:, 2])
            variance_ratio = max(x_var, z_var) / (min(x_var, z_var) + 1e-6)
            
            # 评估检测优势
            if variance_ratio > 5:
                advantage = "单轴主导"
            elif variance_ratio > 2:
                advantage = "三轴有优势"
            else:
                advantage = "三轴明显优势"
            
            print(f"{pattern_name:12} {duration_detected:12.3f}s {variance_ratio:10.2f}   {advantage}")
            
        except Exception as e:
            print(f"{pattern_name:12} {'检测失败':>12} {'N/A':>10}   错误")

if __name__ == "__main__":
    print("三轴合速度矢量检测方法测试")
    print("=" * 60)
    
    print("改进说明:")
    print("- 原方法: 使用主运动轴的速度最大点")
    print("- 新方法: 使用三轴速度合成的合速度矢量最大点")
    print("- 优势: 更全面地反映整体运动状态")
    print()
    
    # 对比单轴和三轴方法
    test_single_axis_vs_three_axis()
    
    # 分析速度分量
    analyze_velocity_components()
    
    # 生成可视化对比
    visualize_three_axis_comparison()
    
    # 测试不同运动模式
    test_different_motion_patterns()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n改进总结:")
    print("1. ✅ 实现了三轴合速度矢量计算")
    print("2. ✅ 使用合速度幅值进行峰值检测")
    print("3. ✅ 保持了最小时间阈值功能")
    print("4. ✅ 提供了详细的可视化对比")
    print("5. ✅ 适用于复杂的三维运动模式")
    print("\n现在velocity方法能更准确地反映整体运动状态！")
