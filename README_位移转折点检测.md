# 基于速度转折点的位移检测方法

## 问题描述

在原来的位移检测方法中，存在以下问题：

- **整体最大值问题**: 简单地找整个过程的位移最大点
- **速度反向影响**: 减速过程中速度反向导致位移也反向
- **检测不准确**: 可能检测到减速阶段的位移点，而不是真正的加速阶段结束点

### 具体场景
```
运动过程: 加速 -> 减速 -> 反向加速
位移变化: 0 -> 正向最大 -> 减小 -> 负向最大
问题: 原方法可能选择负向最大点，而不是正向最大点
```

## 解决方案

### 核心思路
1. **先找速度转折点**: 检测速度方向改变的时刻
2. **限定搜索范围**: 只在转折点之前寻找位移最大点
3. **避免反向影响**: 确保检测到的是正向运动的位移最大点

### 算法流程

```python
def _find_acceleration_end_by_displacement(self, displacement, start_idx, min_duration_sec=0.2):
    # 1. 计算位移的速度（一阶导数）
    velocity = np.gradient(displacement) / dt
    
    # 2. 找到速度转折点
    turning_point = self._find_velocity_turning_point(velocity, min_samples)
    
    # 3. 在转折点之前寻找位移最大点
    if turning_point is not None:
        search_displacement = displacement[:turning_point]
    else:
        search_displacement = displacement
    
    # 4. 在限定范围内找位移极值
    return find_displacement_peak(search_displacement)
```

## 技术实现

### 1. 速度转折点检测

```python
def _find_velocity_turning_point(self, velocity, min_samples):
    # 确定初始运动方向
    initial_velocity = np.mean(velocity[min_samples:min_samples + window])
    initial_direction = 1 if initial_velocity > 0 else -1
    
    # 使用滑动窗口检测方向变化
    for i in range(min_samples + window_size, len(velocity) - window_size):
        current_velocity = np.mean(velocity[i:i + window_size])
        current_direction = 1 if current_velocity > 0 else -1
        
        # 检查方向是否改变并验证稳定性
        if current_direction != initial_direction:
            if self._verify_direction_change(velocity, i, window_size, initial_direction):
                return i
    
    return None
```

### 2. 方向改变验证

```python
def _verify_direction_change(self, velocity, change_point, window_size, initial_direction):
    # 检查改变点之后的方向一致性
    post_change_velocity = velocity[change_point:change_point + verify_window]
    
    # 统计新方向的一致性
    new_direction_count = 0
    for vel in post_change_velocity:
        if abs(vel) > 1e-6:
            vel_direction = 1 if vel > 0 else -1
            if vel_direction != initial_direction:
                new_direction_count += 1
    
    # 新方向占主导地位（>60%）才认为是稳定的改变
    consistency_ratio = new_direction_count / len(post_change_velocity)
    return consistency_ratio > 0.6
```

### 3. 备用策略

当没有找到明显转折点时：
- **零点检测**: 寻找速度接近零的稳定点
- **全范围搜索**: 在整个位移数组中寻找极值
- **时间阈值**: 确保结果满足最小持续时间要求

## 使用方法

### 1. 默认使用

新的转折点检测功能已集成到位移方法中：

```python
from motion_event_detector import MotionEventDetector

detector = MotionEventDetector(sample_rate=100)
start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='displacement'  # 自动使用转折点检测
)
```

### 2. 测试验证

运行测试脚本验证改进效果：

```bash
python test_displacement_turning_point.py
```

## 效果对比

### 改进前（原方法）
```
运动场景: 加速(0.2-1.2s) -> 减速(1.2-2.5s) -> 反向加速(2.5-3.5s)
检测结果: 可能选择3.5s的反向位移最大点
问题: 检测到的不是正向运动的加速阶段
```

### 改进后（新方法）
```
运动场景: 加速(0.2-1.2s) -> 减速(1.2-2.5s) -> 反向加速(2.5-3.5s)
转折点检测: 在2.5s检测到速度方向改变
检测结果: 在2.5s之前找到1.2s的正向位移最大点
改进: 准确检测到正向运动的加速阶段结束
```

## 关键特性

### 1. 智能转折点检测
- **方向识别**: 自动识别初始运动方向
- **稳定性验证**: 确保方向改变是稳定的，不是噪声
- **窗口平滑**: 使用滑动窗口减少噪声影响

### 2. 多层验证机制
```python
# 时间阈值验证
if peak_idx < min_samples:
    continue

# 转折点范围验证  
if peak_idx >= turning_point:
    continue

# 显著性验证
if peak_value < threshold:
    continue
```

### 3. 鲁棒性保证
- **备用策略**: 转折点检测失败时的多重备用方案
- **边界处理**: 处理极端情况和边界条件
- **噪声抑制**: 通过多重过滤减少噪声影响

## 调试信息

新方法提供详细的调试输出：

```
位移方法最小加速持续时间: 0.2秒 (20个样本)
初始速度方向: 正向 (速度: 0.234)
检测到速度方向改变: 索引250, 时间2.500s
  初始方向: 正向
  新方向: 负向
  方向改变验证: 新方向一致性 0.75 (稳定)
找到转折点前的正向位移最大值点: 索引120, 持续时间1.200s, 位移值1.456
```

## 参数调优

### 1. 窗口大小设置
```python
window_size = max(5, self.sample_rate // 20)  # 约0.05秒
```

### 2. 一致性阈值
```python
consistency_ratio > 0.6  # 60%一致性要求
```

### 3. 零点阈值
```python
zero_threshold = 0.1 * np.max(np.abs(velocity))
```

## 适用场景

### 1. 推荐使用
- **复杂运动模式**: 包含加速-减速-反向的运动
- **长时间运动**: 运动持续时间较长，包含多个阶段
- **精确边界要求**: 需要准确区分加速和减速阶段

### 2. 注意事项
- **短时间运动**: 对于很短的运动，转折点可能不明显
- **噪声较大**: 高噪声环境可能影响转折点检测
- **单调运动**: 对于单纯的加速运动，效果与原方法相似

## 故障排除

### 1. 未检测到转折点
- 检查运动是否包含方向改变
- 调整窗口大小和一致性阈值
- 查看调试输出了解检测过程

### 2. 转折点检测错误
- 增加稳定性验证的一致性要求
- 调整滑动窗口大小
- 检查输入数据的质量

### 3. 检测结果不理想
- 对比速度方法的检测结果
- 检查最小时间阈值设置
- 验证运动模式是否符合预期

## 更新日志

- **v1.3**: 添加基于速度转折点的位移检测方法
- **v1.2**: 添加最小时间阈值功能
- **v1.1**: 基础的速度和位移检测方法
- **v1.0**: 原始的零点检测方法

现在的位移检测方法能够智能识别速度转折点，准确检测正向运动的加速阶段！
