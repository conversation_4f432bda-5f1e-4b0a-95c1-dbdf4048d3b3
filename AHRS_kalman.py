import os
import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt
from interpolation import sort_and_clean_data # 保持导入，但不修改其内部

from ahrs.filters import Madgwick
from ahrs.common.quaternion import QuaternionArray
# from ahrs.common.constants import GRAVITY # 使用 ahrs 的重力常数

"""
使用AHRS库对文件夹下的所有IMU数据片段进行处理，
配对acce和gyro文件，估计姿态并移除重力。
修改以适应 sort_and_clean_data 对字符串时间的需求，同时内部使用数值时间。
"""

def process_segment_files_with_ahrs_paired(input_dir, plot_results=True):
    """批量处理分割的、成对的acce和gyro数据片段"""

    # --- 1. 文件发现与配对 (此部分逻辑不变) ---
    all_files_in_dir = os.listdir(input_dir)
    segment_data_map = {}
    file_pattern = re.compile(r"^(segment\d+)_(acce|gyro)_(\d{2}-\d{2}-\d{2}\.\d{3}\.csv)$")
    for filename in all_files_in_dir:
        match = file_pattern.match(filename)
        if match:
            segment_id = match.group(1)
            data_type = match.group(2)
            if segment_id not in segment_data_map:
                segment_data_map[segment_id] = {}
            segment_data_map[segment_id][data_type + '_file'] = os.path.join(input_dir, filename)
    print(f"发现的Segment数据映射: {segment_data_map}")

    for segment_id, files_info in segment_data_map.items():
        if 'acce_file' not in files_info or 'gyro_file' not in files_info:
            print(f"Segment {segment_id} 缺少加速度计或陀螺仪文件，跳过...")
            continue

        acce_input_path = files_info['acce_file']
        gyro_input_path = files_info['gyro_file']
        
        base_output_name = f"{segment_id}_ahrs_linear_accel"
        output_csv_path = os.path.join(input_dir, base_output_name + ".csv")
        output_plot_path = os.path.join(input_dir, base_output_name + "_plot.png")

        print(f"\n处理 Segment: {segment_id}")
        print(f"  加速度计文件: {os.path.basename(acce_input_path)}")
        print(f"  陀螺仪文件: {os.path.basename(gyro_input_path)}")

        # --- 2. 数据读取及为 sort_and_clean_data 和后续数值处理做准备 ---
        try:
            df_acce_orig = pd.read_csv(acce_input_path)
            df_gyro_orig = pd.read_csv(gyro_input_path)

            # 检查必需的列是否存在
            # sort_and_clean_data 需要 'time' (字符串), 'x', 'y', 'z'
            # 我们需要 'time_seconds' (数值)
            required_cols_for_s_and_c = ['time', 'x', 'y', 'z']
            required_numeric_time_col = 'time_seconds'

            for df_orig, name, path_str in [(df_acce_orig, "Acce", acce_input_path), (df_gyro_orig, "Gyro", gyro_input_path)]:
                if not all(col in df_orig.columns for col in required_cols_for_s_and_c):
                    raise ValueError(f"{name} 文件 {os.path.basename(path_str)} 缺少 sort_and_clean_data 所需列 ('time', 'x', 'y', 'z')")
                if required_numeric_time_col not in df_orig.columns:
                    raise ValueError(f"{name} 文件 {os.path.basename(path_str)} 缺少数值时间列 '{required_numeric_time_col}'")

            # --- 为 sort_and_clean_data 准备输入 ---
            # 传递包含原始字符串'time'列和x,y,z的DataFrame副本
            df_acce_input_for_s_and_c = df_acce_orig[required_cols_for_s_and_c].copy()
            df_gyro_input_for_s_and_c = df_gyro_orig[required_cols_for_s_and_c].copy()
            
            # --- 调用 sort_and_clean_data ---
            # 它将使用其内部逻辑处理（并可能对字符串'time'列执行strptime）
            df_acce_processed_by_s_and_c = sort_and_clean_data(df_acce_input_for_s_and_c)
            df_gyro_processed_by_s_and_c = sort_and_clean_data(df_gyro_input_for_s_and_c)

            # --- 构建用于后续数值处理的 DataFrame ---
            # 我们需要从 df_acce_processed_by_s_and_c (和 gyro 对应版本) 中获取清理后的 x, y, z
            # 并从 df_acce_orig (和 gyro 对应版本) 中获取对应的 'time_seconds'
            # 关键：假设 df_..._processed_by_s_and_c 返回的DataFrame的索引
            #       对应于 df_..._input_for_s_and_c (也就是 df_..._orig 的子集) 的索引。
            
            df_acce_cleaned_final = pd.DataFrame()
            # 使用 sort_and_clean_data 返回结果的索引，从原始DataFrame中提取 'time_seconds'
            df_acce_cleaned_final['time'] = pd.to_numeric(df_acce_orig.loc[df_acce_processed_by_s_and_c.index, required_numeric_time_col], errors='raise')
            df_acce_cleaned_final['acce_x'] = pd.to_numeric(df_acce_processed_by_s_and_c['x'], errors='raise')
            df_acce_cleaned_final['acce_y'] = pd.to_numeric(df_acce_processed_by_s_and_c['y'], errors='raise')
            df_acce_cleaned_final['acce_z'] = pd.to_numeric(df_acce_processed_by_s_and_c['z'], errors='raise')
            # 确保索引是干净的，以防万一
            df_acce_cleaned_final.reset_index(drop=True, inplace=True)

            df_gyro_cleaned_final = pd.DataFrame()
            df_gyro_cleaned_final['time'] = pd.to_numeric(df_gyro_orig.loc[df_gyro_processed_by_s_and_c.index, required_numeric_time_col], errors='raise')
            df_gyro_cleaned_final['gyro_x'] = pd.to_numeric(df_gyro_processed_by_s_and_c['x'], errors='raise')
            df_gyro_cleaned_final['gyro_y'] = pd.to_numeric(df_gyro_processed_by_s_and_c['y'], errors='raise')
            df_gyro_cleaned_final['gyro_z'] = pd.to_numeric(df_gyro_processed_by_s_and_c['z'], errors='raise')
            df_gyro_cleaned_final.reset_index(drop=True, inplace=True)

            # 移除 'time' 列（源自 time_seconds）可能因原始行丢失而产生的NaN
            df_acce_cleaned_final.dropna(subset=['time'], inplace=True)
            df_gyro_cleaned_final.dropna(subset=['time'], inplace=True)

        except Exception as e:
            print(f"处理 Segment {segment_id} 的数据读取、sort_and_clean_data 调用或最终 DataFrame 构建时出错: {e}")
            if 'df_acce_orig' in locals(): print(f"Acce Orig Head:\n{df_acce_orig.head(2)}\n{df_acce_orig.dtypes}\n")
            if 'df_acce_input_for_s_and_c' in locals(): print(f"Acce Input for S&C Head:\n{df_acce_input_for_s_and_c.head(2)}\n{df_acce_input_for_s_and_c.dtypes}\n")
            if 'df_acce_processed_by_s_and_c' in locals(): print(f"Acce Processed by S&C Head:\n{df_acce_processed_by_s_and_c.head(2)}\n{df_acce_processed_by_s_and_c.dtypes}\n")
            continue

        # --- 3. 数据同步与合并 ---
        # df_acce_cleaned_final 和 df_gyro_cleaned_final 现在包含数值型 'time' 列
        try:
            # 再次确保 'time' 列是浮点数，以防万一 (pd.to_numeric 应该已经处理了)
            df_acce_cleaned_final['time'] = df_acce_cleaned_final['time'].astype(float)
            df_gyro_cleaned_final['time'] = df_gyro_cleaned_final['time'].astype(float)

            df_acce_cleaned_final.set_index('time', inplace=True)
            df_gyro_cleaned_final.set_index('time', inplace=True)

            df_imu_merged = pd.concat([df_acce_cleaned_final[['acce_x', 'acce_y', 'acce_z']],
                                       df_gyro_cleaned_final[['gyro_x', 'gyro_y', 'gyro_z']]],
                                      axis=1)
            df_imu_merged.interpolate(method='linear', axis=0, limit_direction='both', inplace=True)
            df_imu_merged.dropna(inplace=True) # 移除插值无法填充的行 (通常是开头或结尾)

            if df_imu_merged.empty:
                print(f"Segment {segment_id} 在合并和插值后无有效数据，跳过...")
                continue
            
            df_imu_merged.reset_index(inplace=True) # 'time' 列现在是第一列
            
            # --- >>> 修改开始 <<< ---
            # 1. 确保合并后的 'time' 列是数值类型 (再次检查)
            try:
                df_imu_merged['time'] = pd.to_numeric(df_imu_merged['time'], errors='raise')
            except ValueError as e:
                print(f"Segment {segment_id} 合并后的 'time' 列再次检查时发现非数值: {e}")
                continue # 跳过此 segment
            
            # 2. 按时间排序，以防合并或插值打乱了顺序或引入了非单调性
            df_imu_merged.sort_values(by='time', inplace=True)
            
            # 3. 移除重复的时间戳，保留第一个出现的记录
            #    这对于防止 np.diff 产生0至关重要
            len_before_drop_duplicates = len(df_imu_merged)
            df_imu_merged.drop_duplicates(subset=['time'], keep='first', inplace=True)
            len_after_drop_duplicates = len(df_imu_merged)

            if len_before_drop_duplicates != len_after_drop_duplicates:
                print(f"DEBUG ({segment_id}): 移除了 {len_before_drop_duplicates - len_after_drop_duplicates} 个重复时间戳。")

            # 4. 经过排序和去重后，再次检查数据点是否足够
            if len(df_imu_merged) < 2: # np.diff 需要至少两个点
                print(f"Segment {segment_id} 在排序和去重后数据点不足 (<2)，跳过...")
                continue
            # --- >>> 修改结束 <<< ---
            
        except Exception as e:
            print(f"同步或合并 Segment {segment_id} 数据时出错（或最终时间处理失败）: {e}")
            continue

        # --- 后续处理 (提取 time_data, acc_data, gyro_data, AHRS, 等) ---
        time_data = df_imu_merged['time'].values # 源自 time_seconds
        acc_data = df_imu_merged[['acce_x', 'acce_y', 'acce_z']].values
        gyro_data = df_imu_merged[['gyro_x', 'gyro_y', 'gyro_z']].values

        gyro_data = np.deg2rad(gyro_data) # 假设陀螺仪原始单位是度/秒

        # 此处的 len(time_data) < 2 检查在上面已经做过，但保留也无妨
        if len(time_data) < 2:
            print(f"Segment {segment_id} 最终数据点太少 (<2)，无法计算采样率。跳过...")
            continue
            
        time_diffs = np.diff(time_data)
        
        # 增加详细调试信息，以防问题仍然出现
        if np.any(time_diffs <= 0):
            print(f"错误调试 ({segment_id}): 检测到时间差小于等于0。")
            problem_indices = np.where(time_diffs <= 0)[0]
            for idx in problem_indices[:5]: # 最多打印5个问题点
                print(f"  - 问题点索引 (diff数组): {idx}")
                print(f"    time_data[{idx}]    : {time_data[idx]:.9f}") # 使用格式化输出浮点数
                print(f"    time_data[{idx+1}]  : {time_data[idx+1]:.9f}")
                print(f"    diff             : {time_diffs[idx]:.9f}")
            print(f"Segment {segment_id} 时间戳不单调递增或存在零间隔。跳过...") # 这是你看到的错误
            continue
            
        sample_rate = 1.0 / np.mean(time_diffs)
        # 检查计算出的采样率是否有效
        if sample_rate <= 0 or not np.isfinite(sample_rate):
            print(f"Segment {segment_id} 计算得到的采样率无效 ({sample_rate:.2f})。平均时间差: {np.mean(time_diffs):.9f}。跳过...")
            continue
        
        try:
            attitude_estimator = Madgwick(gyr=gyro_data, acc=acc_data, frequency=sample_rate, gain=0.033)
            quaternions_np = attitude_estimator.Q
        except Exception as e:
            print(f"AHRS姿态估计失败于 Segment {segment_id}: {e}")
            continue

        try:
            attitude_estimator = Madgwick(gyr=gyro_data, acc=acc_data, frequency=sample_rate, gain=0.033)
            quaternions_np = attitude_estimator.Q # Q 是一个 (N, 4) 的 NumPy 数组
        except Exception as e:
            print(f"AHRS姿态估计失败于 Segment {segment_id}: {e}")
            continue

        # --- 将加速度转换到世界坐标系并移除重力 ---
        acc_world_frame_all_samples = None # 初始化

        try:
            # --- >>> 尝试向量化旋转 (ahrs 0.3.1 的首选方案) <<< ---
            # 检查输入数据的维度和形状是否符合预期
            if not (isinstance(quaternions_np, np.ndarray) and quaternions_np.ndim == 2 and quaternions_np.shape[1] == 4):
                print(f"错误 ({segment_id}): quaternions_np 不是预期的 (N, 4) NumPy 数组。形状: {quaternions_np.shape if isinstance(quaternions_np, np.ndarray) else type(quaternions_np)}")
                continue
            if not (isinstance(acc_data, np.ndarray) and acc_data.ndim == 2 and acc_data.shape[1] == 3):
                print(f"错误 ({segment_id}): acc_data 不是预期的 (N, 3) NumPy 数组。形状: {acc_data.shape if isinstance(acc_data, np.ndarray) else type(acc_data)}")
                continue
            if quaternions_np.shape[0] != acc_data.shape[0]:
                print(f"错误 ({segment_id}): 四元数 ({quaternions_np.shape[0]}) 和加速度 ({acc_data.shape[0]}) 样本数量不匹配。")
                continue
            if quaternions_np.shape[0] == 0: # 没有数据点
                 print(f"警告 ({segment_id}): 没有数据点可供旋转。")
                 acc_world_frame_all_samples = np.array([]) # 创建空数组以进行后续处理
            else:
                q_array_object = QuaternionArray(quaternions_np) # 从NumPy数组创建QuaternionArray对象
                acc_world_frame_all_samples = q_array_object.rotate(acc_data)
                # print(f"DEBUG ({segment_id}): 成功使用 QuaternionArray.rotate()") # 可选的调试信息

        except AttributeError as ae:
            if "'QuaternionArray' object has no attribute 'rotate'" in str(ae):
                # 导入 ahrs 模块以尝试获取版本信息
                import ahrs
                print(f"警告 ({segment_id}): 您的 'ahrs' 库 (已安装版本: {ahrs.__version__ if hasattr(ahrs, '__version__') else '未知'}) 的 QuaternionArray 对象没有 'rotate' 方法。这对于版本 0.3.1 来说不寻常。")
                print(f"INFO ({segment_id}): 转而尝试使用迭代方式进行四元数旋转。")
                from ahrs.common.quaternion import Quaternion # 为迭代方法导入单个Quaternion对象

                # 再次检查数量匹配 (理论上上面的检查已经覆盖)
                if quaternions_np.shape[0] != acc_data.shape[0]:
                    print(f"错误 ({segment_id}): 迭代旋转前样本数量不匹配。跳过。")
                    continue

                acc_world_frame_list = []
                if quaternions_np.shape[0] > 0: # 仅当有数据时才迭代
                    for i in range(quaternions_np.shape[0]):
                        q_single_np = quaternions_np[i]
                        acc_single_sensor = acc_data[i]
                        try:
                            q_object = Quaternion(q_single_np)
                            acc_single_world = q_object.rotate(acc_single_sensor)
                            acc_world_frame_list.append(acc_single_world)
                        except Exception as iter_e:
                            print(f"错误 ({segment_id}): 在迭代旋转的第 {i} 个样本时出错: {iter_e}")
                            # 决定是跳过这个样本还是整个segment
                            # 为简单起见，如果单个旋转失败，我们可以选择不添加它，或者标记错误并跳过segment
                            # 这里我们选择不添加，后续检查 acc_world_frame_list 是否为空
                            pass # 或者直接 raise iter_e 导致跳过segment
                
                if not acc_world_frame_list and quaternions_np.shape[0] > 0: # 有输入但迭代后列表为空
                    print(f"错误 ({segment_id}): acc_world_frame_list 在迭代后为空（可能所有单个旋转都失败了）。跳过。")
                    continue
                elif not acc_world_frame_list: # 没有数据或所有都失败
                    acc_world_frame_all_samples = np.array([])
                else:
                    acc_world_frame_all_samples = np.array(acc_world_frame_list)
            else:
                # 捕获到不是关于 'rotate' 方法的 AttributeError
                print(f"使用四元数旋转向量时发生预料之外的 AttributeError 于 Segment {segment_id}: {ae}")
                continue # 跳过此 segment
        except Exception as e:
            # 捕获其他所有在旋转过程中可能发生的错误
            print(f"使用四元数旋转向量时发生一般错误于 Segment {segment_id}: {e}")
            print(f"  四元数数组形状: {quaternions_np.shape if 'quaternions_np' in locals() else '未定义'}")
            print(f"  加速度数据形状: {acc_data.shape if 'acc_data' in locals() else '未定义'}")
            continue # 跳过此 segment

        # --- 检查旋转结果的有效性 ---
        if acc_world_frame_all_samples is None: # 如果旋转逻辑完全失败且未给 acc_world_frame_all_samples 赋值
            print(f"错误 ({segment_id}): 四元数旋转步骤未能生成结果。跳过。")
            continue
        
        # 确保 acc_world_frame_all_samples 是一个numpy数组并且有正确的形状
        # (如果 acc_world_frame_all_samples 是空数组 np.array([]), .ndim 会是1, .size是0)
        is_valid_array = isinstance(acc_world_frame_all_samples, np.ndarray)
        is_correct_shape = (acc_world_frame_all_samples.ndim == 2 and acc_world_frame_all_samples.shape[1] == 3)
        
        if not is_valid_array or (acc_world_frame_all_samples.size > 0 and not is_correct_shape):
            print(f"错误 ({segment_id}): acc_world_frame_all_samples 形状不正确或不是有效数组。Shape: {acc_world_frame_all_samples.shape if is_valid_array else type(acc_world_frame_all_samples)}. 跳过...")
            continue
        
        # 如果有加速度数据输入，但旋转后结果为空（例如迭代中所有单个旋转都失败且被跳过）
        if acc_data.size > 0 and acc_world_frame_all_samples.size == 0:
            print(f"错误 ({segment_id}): 旋转后加速度数据为空，但有输入数据。检查旋转逻辑。跳过...")
            continue
        elif acc_data.size == 0 and acc_world_frame_all_samples.size == 0: # 如果本来就没有输入数据
             print(f"警告 ({segment_id}): 没有加速度数据进行重力移除（输入为空）。")
             # 这种情况下，后续的 result_df 会是空的，或者你可以选择不保存文件
        
        # 只有当有旋转后的数据时，才进行重力移除和后续操作
        if acc_world_frame_all_samples.size > 0:
            gravity_world_frame = np.array([0.0, 0.0, 9.81]) # 或使用 GRAVITY
            linear_accel_world_all_samples = acc_world_frame_all_samples - gravity_world_frame
            
            # 确保所有参与构建DataFrame的数组长度一致
            num_samples_processed = linear_accel_world_all_samples.shape[0]
            
            result_df = pd.DataFrame({
                'time': time_data[:num_samples_processed],
                'linear_acce_x_world': linear_accel_world_all_samples[:, 0],
                'linear_acce_y_world': linear_accel_world_all_samples[:, 1],
                'linear_acce_z_world': linear_accel_world_all_samples[:, 2],
                'q_w': quaternions_np[:num_samples_processed, 0],
                'q_x': quaternions_np[:num_samples_processed, 1],
                'q_y': quaternions_np[:num_samples_processed, 2],
                'q_z': quaternions_np[:num_samples_processed, 3]
            })
            result_df.to_csv(output_csv_path, index=False)
            print(f"已保存 Segment {segment_id} AHRS处理后的线性加速度数据: {output_csv_path}")

            if plot_results:
                # ... (绘图代码，同样要注意长度匹配)
                min_len_plot = min(len(time_data), acc_data.shape[0], num_samples_processed)
                if min_len_plot > 0: # 仅当有数据点时绘图
                    plt.figure(figsize=(15, 10))
                    # ... (补充完整的绘图代码，使用 [:min_len_plot] 进行切片)
                    plt.suptitle(f"IMU Data Processing for {segment_id}", fontsize=16)
                    for i, axis_label in enumerate(['x', 'y', 'z']):
                        plt.subplot(3, 2, 2*i + 1)
                        plt.plot(time_data[:min_len_plot], acc_data[:min_len_plot, i], label=f'Sensor Accel {axis_label}', alpha=0.7)
                        plt.title(f'Original Sensor Accel {axis_label}')
                        plt.xlabel('Time (s)')
                        plt.ylabel('Accel (m/s^2)')
                        plt.legend()
                        plt.grid(True)

                        plt.subplot(3, 2, 2*i + 2)
                        # result_df['time'] 已经是正确长度的
                        plt.plot(result_df['time'], result_df[f'linear_acce_{axis_label}_world'], label=f'World Linear Accel {axis_label}', color='r')
                        plt.title(f'World Linear Accel {axis_label}')
                        plt.xlabel('Time (s)')
                        plt.ylabel('Linear Accel (m/s^2)')
                        plt.legend()
                        plt.grid(True)
                    
                    plt.tight_layout(rect=[0, 0, 1, 0.96])
                    plt.savefig(output_plot_path)
                    plt.close()
                else:
                    print(f"警告 ({segment_id}): 没有足够的数据点进行绘图。")
        elif acc_data.size > 0 : # 有输入但旋转后结果为空
            print(f"错误 ({segment_id}): 旋转结果为空，无法进行重力移除和保存。")
        else: # 输入数据本身为空
            print(f"警告 ({segment_id}): 无输入数据，不进行重力移除和保存。")

if __name__ == "__main__":
    data_dir = r"E:\Document\user001\5.21\arhs_test" 

    if not os.path.isdir(data_dir):
        print(f"错误: 目录 '{data_dir}' 不存在。请设置正确的数据目录。")
    else:
        process_segment_files_with_ahrs_paired(data_dir, plot_results=True)
        print("\n完成所有Segments的AHRS数据处理")