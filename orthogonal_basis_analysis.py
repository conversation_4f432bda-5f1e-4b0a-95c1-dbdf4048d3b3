import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import glob
import re
from sklearn.decomposition import PCA
from least_squares_plane_fit import find_optimal_projection_plane_least_squares, project_vector_to_plane
from plane_based_coordinates import create_plane_based_coordinates, transform_vectors, read_accelerometer_data, extract_gravity_vector, create_watch_to_world_matrix, transform_to_world_coordinates

"""
    最小二乘法找出平面上的长轴和短轴基向量
"""

def find_orthogonal_basis_on_plane(vectors, plane_normal):
    """
    在拟合平面上找出两个正交的基向量作为矩形平面的长轴和短轴
    
    参数:
    vectors: 原始向量集合，形状为(n, 3)
    plane_normal: 平面法向量
    
    返回:
    major_axis: 平面上的长轴向量 (数据变化最大的方向)
    minor_axis: 平面上的短轴向量 (与长轴正交的方向)
    projected_vectors: 投影到平面上的向量
    importance: 主成分解释的方差比例 (长轴和短轴的重要性比例)
    projection_lengths: 每个向量在长轴和短轴上的投影长度
    """
    # 确保平面法向量是单位向量
    plane_normal = plane_normal / np.linalg.norm(plane_normal)
    
    # 将所有向量投影到平面上
    projected_vectors = np.zeros_like(vectors)
    for i, v in enumerate(vectors):
        # 向量与法向量的点积，表示在法向量方向上的分量
        dot_product = np.dot(v, plane_normal)
        # 从原始向量中减去法向量方向的分量，得到投影向量
        projected_vectors[i] = v - dot_product * plane_normal
    
    # 过滤掉长度太小的向量
    valid_indices = []
    for i, v in enumerate(projected_vectors):
        if np.linalg.norm(v) > 1e-6:
            valid_indices.append(i)
    
    valid_projected_vectors = projected_vectors[valid_indices]
    
    # 使用PCA在平面上找出主要方向
    pca = PCA(n_components=2)
    pca.fit(valid_projected_vectors)
    
    # 获取平面上的两个主方向作为长轴和短轴
    major_axis = pca.components_[0]  # 数据变化最大的方向 - 长轴
    minor_axis = pca.components_[1]  # 数据变化次大的方向 - 短轴
    
    # 确保轴向量是单位向量
    major_axis = major_axis / np.linalg.norm(major_axis)
    minor_axis = minor_axis / np.linalg.norm(minor_axis)
    
    # 确保短轴与长轴正交（使用Gram-Schmidt正交化）
    minor_axis = minor_axis - np.dot(minor_axis, major_axis) * major_axis
    minor_axis = minor_axis / np.linalg.norm(minor_axis)
    
    # 检查与平面法向量的正交性并修正
    major_normal_dot = np.dot(major_axis, plane_normal)
    minor_normal_dot = np.dot(minor_axis, plane_normal)
    
    if abs(major_normal_dot) > 1e-6 or abs(minor_normal_dot) > 1e-6:
        # 如果基向量与法向量不完全正交，进行调整
        major_axis = major_axis - major_normal_dot * plane_normal
        major_axis = major_axis / np.linalg.norm(major_axis)
        
        minor_axis = minor_axis - minor_normal_dot * plane_normal
        minor_axis = minor_axis / np.linalg.norm(minor_axis)
        
        # 再次确保短轴与长轴正交
        minor_axis = minor_axis - np.dot(minor_axis, major_axis) * major_axis
        minor_axis = minor_axis / np.linalg.norm(minor_axis)
    
    # 计算每个主成分解释的方差比例
    importance = pca.explained_variance_ratio_
    
    # 计算每个向量在长轴和短轴上的投影长度
    projection_lengths = np.zeros((len(vectors), 2))
    for i, v in enumerate(projected_vectors):
        if np.linalg.norm(v) > 1e-6:
            projection_lengths[i, 0] = abs(np.dot(v, major_axis))  # 长轴投影长度
            projection_lengths[i, 1] = abs(np.dot(v, minor_axis))  # 短轴投影长度
    
    return major_axis, minor_axis, projected_vectors, importance, projection_lengths

def analyze_vector_relationships(projected_vectors, major_axis, minor_axis):
    """
    分析投影向量与长轴、短轴之间的关系
    
    参数:
    projected_vectors: 投影到平面上的向量
    major_axis: 长轴基向量
    minor_axis: 短轴基向量
    
    返回:
    angles: 每个向量与长轴的夹角（度）
    magnitudes: 每个向量在平面上的长度
    major_components: 每个向量在长轴上的分量
    minor_components: 每个向量在短轴上的分量
    aspect_ratios: 长轴分量与短轴分量的比值 (长度比)
    """
    num_vectors = projected_vectors.shape[0]
    angles = np.zeros(num_vectors)
    magnitudes = np.zeros(num_vectors)
    major_components = np.zeros(num_vectors)
    minor_components = np.zeros(num_vectors)
    aspect_ratios = np.zeros(num_vectors)
    
    for i, v in enumerate(projected_vectors):
        # 计算投影向量的长度
        magnitudes[i] = np.linalg.norm(v)
        
        # 如果向量太短，跳过计算
        if magnitudes[i] < 1e-6:
            angles[i] = 0
            aspect_ratios[i] = 1.0
            continue
        
        # 计算向量在长轴和短轴上的分量
        major_components[i] = np.dot(v, major_axis)
        minor_components[i] = np.dot(v, minor_axis)
        
        # 计算向量与长轴的夹角（弧度）
        angle_rad = np.arctan2(minor_components[i], major_components[i])
        # 转换为度，并确保角度在0-360度范围内
        angles[i] = np.degrees(angle_rad) % 360
        
        # 计算长轴分量与短轴分量的比值 (长度比)
        if abs(minor_components[i]) > 1e-6:
            aspect_ratios[i] = abs(major_components[i] / minor_components[i])
        else:
            aspect_ratios[i] = float('inf')  # 避免除以零
    
    return angles, magnitudes, major_components, minor_components, aspect_ratios

def extract_segment_number(filename):
    """从文件名中提取segment编号"""
    match = re.search(r'segment(\d+)', os.path.basename(filename))
    if match:
        return int(match.group(1))
    return None

def visualize_orthogonal_basis(csv_folder, acc_data=None, output_file=None):
    """
    可视化平面上的长轴和短轴基向量以及向量投影分析
    
    参数:
    csv_folder: 包含位移数据的文件夹
    acc_data: 加速度数据，用于确定世界坐标系
    output_file: 输出图像的文件路径
    
    返回:
    major_axis: 长轴基向量
    minor_axis: 短轴基向量
    """
    # 使用最小二乘法拟合平面，并获取文件路径信息
    plane_normal, d, displacement_endpoints, source_files = find_optimal_projection_plane_least_squares(csv_folder, return_source_files=True)
    
    if plane_normal is None:
        print("无法拟合平面")
        return None, None
    
    # 提取每个向量对应的segment编号
    segment_numbers = []
    for file_path in source_files:
        segment_num = extract_segment_number(file_path)
        segment_numbers.append(segment_num if segment_num is not None else -1)
    
    # 创建图形
    fig = plt.figure(figsize=(18, 15))
    
    # 添加3D坐标系视图
    ax1 = fig.add_subplot(221, projection='3d')
    ax2 = fig.add_subplot(222, projection='3d')
    
    # 添加平面上2D视图和数据分析视图
    ax3 = fig.add_subplot(223)
    ax4 = fig.add_subplot(224)
    
    # 分配颜色
    colors = plt.cm.jet(np.linspace(0, 1, len(displacement_endpoints)))
    
    # 世界坐标系相关处理
    # ...existing code...
    
    final_normal = plane_normal
    final_d = d
    final_endpoints = displacement_endpoints
    
    # 寻找平面上的矩形长轴和短轴
    major_axis, minor_axis, projected_vectors, importance, projection_lengths = find_orthogonal_basis_on_plane(
        final_endpoints, final_normal
    )
    
    # 计算Z轴（应与平面法向量一致）
    z_axis = np.cross(major_axis, minor_axis)
    z_axis = z_axis / np.linalg.norm(z_axis)
    
    # 验证基向量的正交性
    print("\n基向量正交性验证:")
    print(f"长轴·短轴 = {np.dot(major_axis, minor_axis):.6f} (应接近0)")
    print(f"短轴·Z轴 = {np.dot(minor_axis, z_axis):.6f} (应接近0)")
    print(f"Z轴·长轴 = {np.dot(z_axis, major_axis):.6f} (应接近0)")
    print(f"Z轴·法向量 = {np.dot(z_axis, final_normal):.6f} (应接近±1)")
    
    # 输出主成分解释的方差比例
    print(f"\n主成分解释的方差比例:")
    print(f"长轴: {importance[0]:.4f} (应显著大于短轴)")
    print(f"短轴: {importance[1]:.4f}")
    print(f"长轴/短轴比例: {importance[0]/importance[1]:.4f}")
    
    # 确定绘图范围
    max_range = max(
        np.max(np.abs(final_endpoints[:, 0])),
        np.max(np.abs(final_endpoints[:, 1])),
        np.max(np.abs(final_endpoints[:, 2]))
    ) * 1.2
    
    # 创建平面网格
    xx, yy = np.meshgrid(
        np.linspace(-max_range, max_range, 10),
        np.linspace(-max_range, max_range, 10)
    )
    
    # 计算并绘制平面
    a, b, c = final_normal
    zz = (-a * xx - b * yy - final_d) / c if abs(c) > 1e-5 else np.zeros_like(xx)
    ax1.plot_surface(xx, yy, zz, alpha=0.2, color='blue')
    
    # 绘制原始向量，添加segment标签到图例中
    for i, endpoint in enumerate(final_endpoints):
        label = f"Segment {segment_numbers[i]}" if segment_numbers[i] >= 0 else f"Vector {i+1}"
        ax1.quiver(0, 0, 0, endpoint[0], endpoint[1], endpoint[2], 
                  color=colors[i], arrow_length_ratio=0.1, alpha=0.7, label=label)
    
    # 绘制矩形轴向量（在原坐标系中）
    axis_length = max_range * 0.5
    # ax1.quiver(0, 0, 0, major_axis[0] * axis_length, major_axis[1] * axis_length, major_axis[2] * axis_length,
    #            color='red', arrow_length_ratio=0.1, alpha=1, label='X')
    # ax1.quiver(0, 0, 0, minor_axis[0] * axis_length, minor_axis[1] * axis_length, minor_axis[2] * axis_length,
    #            color='green', arrow_length_ratio=0.1, alpha=1, label='Y')
    # ax1.quiver(0, 0, 0, z_axis[0] * axis_length, z_axis[1] * axis_length, z_axis[2] * axis_length,
    #            color='blue', arrow_length_ratio=0.1, alpha=1, label='Z')
    
    # 创建平面坐标系转换矩阵（长轴为X，短轴为Y）
    rotation_matrix = np.vstack([major_axis, minor_axis, z_axis]).T
    
    # 转换向量到平面坐标系
    transformed_endpoints = transform_vectors(final_endpoints, rotation_matrix)
    
    # 在平面坐标系中绘制向量，同样添加segment标签
    for i, endpoint in enumerate(transformed_endpoints):
        label = f"Segment {segment_numbers[i]}" if segment_numbers[i] >= 0 else f"Vector {i+1}"
        ax2.quiver(0, 0, 0, endpoint[0], endpoint[1], endpoint[2],
                  color=colors[i], arrow_length_ratio=0.1, alpha=0.7, label=label)
    
    # 在平面坐标系中绘制XY平面
    ax2.plot_surface(xx, yy, np.zeros_like(xx), alpha=0.2, color='green')
    
    # 在平面坐标系中绘制坐标轴
    # ax2.quiver(0, 0, 0, axis_length, 0, 0, color='red', arrow_length_ratio=0.1, alpha=1, label='X')
    # ax2.quiver(0, 0, 0, 0, axis_length, 0, color='green', arrow_length_ratio=0.1, alpha=1, label='Y')
    # ax2.quiver(0, 0, 0, 0, 0, axis_length, color='blue', arrow_length_ratio=0.1, alpha=1, label='Z')
    
    # 分析向量与长轴、短轴的关系
    angles, magnitudes, major_comps, minor_comps, aspect_ratios = analyze_vector_relationships(
        transformed_endpoints, [1, 0, 0], [0, 1, 0]
    )
    
    # 在第三个子图中，使用世界坐标系中的X轴方向和与其垂直的方向来绘制平面投影
    # 确定世界坐标系的X轴在平面上的投影方向
    # world_x = np.array([1, 0, 0])  # 世界坐标系的X轴
    
    # # 将世界X轴投影到平面上
    # world_x_proj = world_x - np.dot(world_x, final_normal) * final_normal
    
    # # 如果投影后的向量长度接近于0，则选择一个在平面上的备选方向
    # if np.linalg.norm(world_x_proj) < 1e-6:
    #     # 如果X轴几乎垂直于平面，使用Y轴
    #     world_x_proj = np.array([0, 1, 0]) - np.dot(np.array([0, 1, 0]), final_normal) * final_normal
    
    # # 归一化投影向量
    # world_x_proj = world_x_proj / np.linalg.norm(world_x_proj)
    
    # # 计算与投影X轴垂直且在平面内的Y轴
    # world_y_proj = np.cross(final_normal, world_x_proj)
    # world_y_proj = world_y_proj / np.linalg.norm(world_y_proj)
    
    # # 创建新的平面坐标系转换矩阵（世界X轴投影为X，与之垂直的方向为Y）
    # world_plane_matrix = np.vstack([world_x_proj, world_y_proj, final_normal]).T
    
    # # 转换向量到新的平面坐标系
    # world_plane_endpoints = transform_vectors(final_endpoints, world_plane_matrix)
    
    # # 清除ax3之前的内容
    # ax3.clear()
    world_y = np.array([1, 0, 0])  # 世界坐标系的Y轴
    world_z = np.array([0, 0, 1])  # 世界坐标系的Z轴

    # 将世界Y轴投影到平面上作为新的X轴
    new_x_proj = world_y - np.dot(world_y, final_normal) * final_normal

    # 如果投影后的向量长度接近于0，使用X轴作为替代
    if np.linalg.norm(new_x_proj) < 1e-6:
        world_x = np.array([1, 0, 0])
        new_x_proj = world_x - np.dot(world_x, final_normal) * final_normal

    # 归一化X轴投影向量
    new_x_proj = new_x_proj / np.linalg.norm(new_x_proj)

    # 将世界Z轴投影到平面上
    new_y_proj = world_z - np.dot(world_z, final_normal) * final_normal

    # 如果投影后的向量长度接近于0，计算与X轴垂直的向量作为Y轴
    if np.linalg.norm(new_y_proj) < 1e-6:
        new_y_proj = np.cross(final_normal, new_x_proj)
    else:
        # 确保Y轴与X轴正交（使用Gram-Schmidt正交化）
        new_y_proj = new_y_proj - np.dot(new_y_proj, new_x_proj) * new_x_proj

    # 归一化Y轴投影向量
    new_y_proj = new_y_proj / np.linalg.norm(new_y_proj)

    # 创建新的平面坐标系转换矩阵（Y轴投影为X，Z轴投影为Y）
    world_plane_matrix = np.vstack([new_x_proj, new_y_proj, final_normal]).T

    # 转换向量到新的平面坐标系
    world_plane_endpoints = transform_vectors(final_endpoints, world_plane_matrix)
    
    # 在新的平面坐标系中绘制向量投影
    for i, endpoint in enumerate(world_plane_endpoints):
        label = f"Segment {segment_numbers[i]}" if segment_numbers[i] >= 0 else f"Vector {i+1}"
        ax3.arrow(0, 0, endpoint[0], endpoint[1], color=colors[i], head_width=0.05, head_length=0.1, alpha=0.7, label=label)
    
    # 计算平均投影长度和比例
    valid_indices = [i for i, m in enumerate(magnitudes) if m > 1e-6]
    avg_x_proj = np.mean([abs(world_plane_endpoints[i][0]) for i in valid_indices]) if valid_indices else 0
    avg_y_proj = np.mean([abs(world_plane_endpoints[i][1]) for i in valid_indices]) if valid_indices else 0
    avg_ratio = np.mean([abs(world_plane_endpoints[i][0])/abs(world_plane_endpoints[i][1]) 
                         if abs(world_plane_endpoints[i][1]) > 1e-6 else float('inf') 
                         for i in valid_indices]) if valid_indices else 0
    
    print(f"\n世界坐标系投影分析:")
    print(f"X轴平均投影长度: {avg_x_proj:.4f}")
    print(f"Y轴平均投影长度: {avg_y_proj:.4f}")
    print(f"X轴/Y轴投影比例: {avg_ratio:.4f}")
    
    # 绘制矩形示意主要分布范围
    rect_width = avg_x_proj * 2
    rect_height = avg_y_proj * 2
    rect = plt.Rectangle((-rect_width/2, -rect_height/2), rect_width, rect_height, 
                          fill=False, edgecolor='orange', linestyle='--', linewidth=2)
    ax3.add_patch(rect)
    
    # 设置标题和标签
    ax1.set_title('Original 3D')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    ax2.set_title('Plane Coordinates')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    
    ax3.set_title('Plane Projection (World X direction)')
    ax3.set_xlabel('X projection')
    ax3.set_ylabel('Y projection')
    ax3.set_aspect('equal')
    
    ax4.set_title('X/Y Ratio')
    ax4.set_xlabel('Segment')
    ax4.set_ylabel('X/Y Ratio')
    ax4.set_yscale('log')  # 使用对数尺度以便查看较大范围的比例
    
    # 设置坐标轴范围
    ax1.set_xlim([-max_range, max_range])
    ax1.set_ylim([-max_range, max_range])
    ax1.set_zlim([-max_range, max_range])
    
    ax2.set_xlim([-max_range, max_range])
    ax2.set_ylim([-max_range, max_range])
    ax2.set_zlim([-max_range, max_range])
    
    # 设置2D视图的范围
    x_limit = max(np.max(np.abs([p[0] for p in world_plane_endpoints])), 1) * 1.2
    y_limit = max(np.max(np.abs([p[1] for p in world_plane_endpoints])), 1) * 1.2
    # ax3.set_xlim([-x_limit, x_limit])
    # ax3.set_ylim([-y_limit, y_limit])
    ax3.set_xlim([-6, 6])  # ±6cm
    ax3.set_ylim([-6, 6])  # ±6cm
    
    # 添加网格
    ax3.grid(True)
    ax4.grid(True)
    
    # 为防止图例过于拥挤，仅显示前10个向量的图例
    if len(segment_numbers) > 10:
        handles1, labels1 = ax1.get_legend_handles_labels()
        ax1.legend(handles1[:10], labels1[:10], loc='upper right', title='Segments (top 10)')
        handles2, labels2 = ax2.get_legend_handles_labels()
        ax2.legend(handles2[:10], labels2[:10], loc='upper right', title='Segments (top 10)')
        handles3, labels3 = ax3.get_legend_handles_labels()
        ax3.legend(handles3[:10], labels3[:10], loc='upper right', title='Segments (top 10)')
    else:
        # 添加图例
        ax1.legend(loc='upper right', title='Segments')
        ax2.legend(loc='upper right', title='Segments')
        ax3.legend(loc='upper right', title='Segments')
        ax4.legend(loc='upper right')
    
    # 设置视角
    ax1.view_init(elev=30, azim=45)
    ax2.view_init(elev=30, azim=45)
    
    plt.tight_layout()
    
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    plt.show()
    
    # 打印关键结果
    print("\n矩形平面基向量计算结果:")
    print(f"长轴 (主方向): [{major_axis[0]:.4f}, {major_axis[1]:.4f}, {major_axis[2]:.4f}]")
    print(f"短轴 (次方向): [{minor_axis[0]:.4f}, {minor_axis[1]:.4f}, {minor_axis[2]:.4f}]")
    print(f"法向量 (Z轴): [{z_axis[0]:.4f}, {z_axis[1]:.4f}, {z_axis[2]:.4f}]")
    
    return major_axis, minor_axis

if __name__ == "__main__":
    # 指定包含CSV文件的文件夹路径
    csv_folder = r"E:\Document\user001\6.30\Session_20250630_221020\output_world_coordinates"
    
    # 寻找加速度计数据文件
    acce_files = glob.glob(os.path.join(csv_folder, "acce_data*.csv"))
    if not acce_files:
        acce_files = glob.glob(os.path.join(csv_folder, "grav_data*.csv"))
    
    if not acce_files:
        print("未找到加速度计数据文件")
        acc_data = None
    else:
        # 读取第一个加速度计数据文件
        acc_data = read_accelerometer_data(acce_files[0])
        print(f"读取加速度计数据: {len(acc_data)} 个样本")
    
    # 输出文件路径
    output_file = os.path.join(csv_folder, "矩形平面基向量分析.png")
    
    # 执行分析和可视化
    major_axis, minor_axis = visualize_orthogonal_basis(
        csv_folder, 
        acc_data=acc_data,
        output_file=output_file
    )
    
    if major_axis is not None:
        print("\n矩形平面尺寸比例分析完成!")
