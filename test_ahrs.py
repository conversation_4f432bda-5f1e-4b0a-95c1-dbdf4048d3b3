# -*- coding:utf-8 -*-
"""
AHRS库测试脚本
测试Madgwick和Mahony算法是否可以正常工作
"""

import numpy as np

# 测试AHRS库导入
try:
    from ahrs.filters import Madgwick, Mahony
    from ahrs.common import Quaternion
    print("✓ AHRS库导入成功")
    print(f"Madgwick类: {Madgwick}")
    print(f"Mahony类: {Mahony}")
    print(f"Quaternion类: {Quaternion}")
    
    # 创建测试数据
    n_samples = 100
    frequency = 100  # Hz
    
    # 模拟传感器数据
    acc = np.random.normal(0, 0.1, (n_samples, 3))
    acc[:, 2] += 9.8  # 添加重力
    
    gyr = np.random.normal(0, 0.01, (n_samples, 3))
    
    mag = np.array([[0, 0.5, 0.5]] * n_samples)  # 简单的磁场
    
    print(f"\n测试数据形状:")
    print(f"加速度: {acc.shape}")
    print(f"陀螺仪: {gyr.shape}")
    print(f"磁力计: {mag.shape}")
    
    # 测试Madgwick算法
    print(f"\n测试Madgwick算法...")
    madgwick = Madgwick(gyr=gyr, acc=acc, mag=mag, frequency=frequency, beta=0.1)
    print(f"Madgwick四元数输出形状: {madgwick.Q.shape}")
    print(f"第一个四元数: {madgwick.Q[0]}")
    print(f"最后一个四元数: {madgwick.Q[-1]}")
    
    # 测试Mahony算法
    print(f"\n测试Mahony算法...")
    mahony = Mahony(gyr=gyr, acc=acc, mag=mag, frequency=frequency, k_P=1.0, k_I=0.3)
    print(f"Mahony四元数输出形状: {mahony.Q.shape}")
    print(f"第一个四元数: {mahony.Q[0]}")
    print(f"最后一个四元数: {mahony.Q[-1]}")
    
    # 测试四元数到旋转矩阵转换
    print(f"\n测试四元数转换...")
    q = madgwick.Q[0]
    quat_obj = Quaternion(q)
    rotation_matrix = quat_obj.to_DCM()
    print(f"旋转矩阵形状: {rotation_matrix.shape}")
    print(f"旋转矩阵:\n{rotation_matrix}")
    
    # 测试加速度转换
    test_acc = acc[0]
    transformed_acc = np.dot(rotation_matrix, test_acc)
    print(f"\n原始加速度: {test_acc}")
    print(f"转换后加速度: {transformed_acc}")
    
    print(f"\n✓ AHRS库功能测试全部通过！")
    
except ImportError as e:
    print(f"✗ AHRS库导入失败: {e}")
    print("请安装AHRS库: pip install ahrs")
except Exception as e:
    print(f"✗ AHRS库测试出错: {e}")
    import traceback
    traceback.print_exc()
