# 16个位移图片可视化功能

## 概述

`individual_motion_visualization.py` 已经升级，现在支持展示最多16个位移图片，采用优化的4x4网格布局。

## 主要改进

### 1. 扩展支持数量
- **原功能**: 支持8个位移图片
- **新功能**: 支持最多16个位移图片
- **布局优化**: 采用4x4网格布局

### 2. 标准方向配置
扩展了标准方向数组，现在包含16个预定义方向：

```python
custom_standard_directions = [
    (2.0, 0),       # 运动事件1：正东方向，2cm
    (0, -2.0),      # 运动事件2：正北方向，2cm  
    (-2.0, 0),      # 运动事件3：正西方向，2cm
    (0, 2.0),       # 运动事件4：正南方向，2cm
    (1.5, -1.5),    # 运动事件5：东北方向，约2.1cm
    (1.5, 1.5),     # 运动事件6：东南方向，约2.1cm
    (-1.5, -1.5),   # 运动事件7：西北方向，约2.1cm
    (-1.5, 1.5),    # 运动事件8：西南方向，约2.1cm
    (2.2, 0),       # 运动事件9：正东方向，2.2cm
    (0, -2.2),      # 运动事件10：正北方向，2.2cm
    (-2.2, 0),      # 运动事件11：正西方向，2.2cm
    (0, 2.2),       # 运动事件12：正南方向，2.2cm
    (1.8, -1.8),    # 运动事件13：东北方向，约2.5cm
    (1.8, 1.8),     # 运动事件14：东南方向，约2.5cm
    (-1.8, -1.8),   # 运动事件15：西北方向，约2.5cm
    (-1.8, 1.8),    # 运动事件16：西南方向，约2.5cm
]
```

### 3. 网格布局优化

根据运动事件数量自动选择最佳布局：

| 运动事件数量 | 网格布局 | 说明 |
|-------------|----------|------|
| 1-4个 | 2x2 | 小规模展示 |
| 5-9个 | 3x3 | 中等规模展示 |
| 10-16个 | 4x4 | 大规模展示（新增） |
| 17+个 | 动态计算 | 超大规模展示 |

### 4. 显示优化

针对16个图片的显示进行了优化：

- **总览图尺寸**: 从4x4英寸调整为3x3英寸每个子图
- **箭头大小**: 根据网格密度动态调整
- **标记大小**: 优化以保持在4x4网格中的可见性
- **字体大小**: 调整标题字体大小以适应较小的子图

### 5. 颜色扩展

扩展了颜色数组以支持16种不同颜色：

```python
basic_colors = [
    'red', 'blue', 'green', 'orange', 'purple', 'brown', 'yellow', 'gray', 
    'olive', 'cyan', 'magenta', 'lime', 'pink', 'navy', 'maroon', 'teal'
]
```

## 使用方法

### 基本使用

```python
from individual_motion_visualization import visualize_individual_motions

# 指定数据文件夹
csv_folder = "your_displacement_data_folder"

# 定义16个标准方向（可选）
standard_directions = [
    (2.0, 0), (0, -2.0), (-2.0, 0), (0, 2.0),
    # ... 更多方向
]

# 执行可视化
visualize_individual_motions(
    csv_folder=csv_folder,
    output_folder="output_16_motions",
    standard_directions=standard_directions
)
```

### 测试功能

运行测试脚本验证16个位移图片功能：

```bash
python test_16_motions.py
```

## 生成的文件

运行后会生成以下文件：

1. **总览图**: `motions_overview.png`
   - 4x4网格显示所有16个运动
   - 每个子图包含运动轨迹和标准方向

2. **统计图**: `motion_statistics.png`
   - 长度分布、角度分布
   - X轴和Y轴投影分布

3. **独立运动图**: `motion_001.png` ~ `motion_016.png`
   - 每个运动的详细单独图
   - 包含角度偏差信息

## 技术细节

### 动态尺寸调整

```python
# 根据运动数量调整显示参数
if num_motions <= 16:
    head_width = 0.12      # 较大的箭头头部
    linewidth = 3          # 较粗的线条
    marker_size = 4        # 较大的标记
    title_fontsize = 8     # 适中的字体
else:
    head_width = 0.08      # 较小的箭头头部
    linewidth = 2          # 较细的线条
    marker_size = 3        # 较小的标记
    title_fontsize = 7     # 较小的字体
```

### 网格布局逻辑

```python
# 优化的网格布局计算
if num_motions <= 4:
    cols, rows = 2, 2
elif num_motions <= 9:
    cols, rows = 3, 3
elif num_motions <= 16:
    cols, rows = 4, 4      # 新增的16个图片布局
else:
    cols = int(np.ceil(np.sqrt(num_motions)))
    rows = int(np.ceil(num_motions / cols))
```

## 兼容性

- ✅ **向后兼容**: 原有的8个图片功能完全保留
- ✅ **自动适应**: 根据实际运动数量自动选择最佳布局
- ✅ **标准方向循环**: 如果运动数量超过标准方向数量，会自动循环使用

## 使用建议

### 1. 数据准备
- 确保位移数据文件命名包含`segment`编号
- 文件格式：`displacement_segmentXXX.csv`
- 包含`time`, `x`, `y`, `z`列

### 2. 标准方向设置
- 为每个运动事件设置合适的标准方向
- 标准方向应在±2.5cm范围内以保持可见性
- 可以根据实际需求调整方向和长度

### 3. 输出优化
- 16个图片的总览图较大，建议使用高分辨率显示器查看
- 可以通过调整`figsize`参数进一步优化显示效果
- 独立图片文件便于详细分析单个运动

## 示例输出

运行16个位移图片可视化后，您将看到：

1. **4x4网格总览图**: 所有16个运动在一个图中的概览
2. **详细统计分析**: 运动长度、角度、投影的分布情况
3. **16个独立图片**: 每个运动的详细分析图

## 故障排除

### 常见问题

1. **图片显示太小**: 调整`figsize`参数
2. **箭头不清晰**: 检查`head_width`和`linewidth`设置
3. **标准方向不显示**: 确保标准方向在±2.5cm范围内
4. **颜色重复**: 检查运动数量是否超过16个

### 性能优化

- 对于大量运动事件，考虑分批处理
- 可以通过减少独立图片的生成来提高速度
- 调整图片分辨率以平衡质量和文件大小

## 更新日志

- **v2.0**: 支持16个位移图片展示
- **v1.0**: 原始8个位移图片功能
