import pandas as pd
import numpy as np


class KalmanFilter:
    def __init__(self, process_variance, measurement_variance, estimation_error, initial_estimate):
        """
        初始化卡尔曼滤波器
        :param process_variance: 过程噪声的方差
        :param measurement_variance: 测量噪声的方差
        :param estimation_error: 初始估计误差
        :param initial_estimate: 初始估计值
        """
        self.process_variance = process_variance  # 过程噪声的方差
        self.measurement_variance = measurement_variance  # 测量噪声的方差
        self.estimation_error = estimation_error  # 初始估计误差
        self.estimate = initial_estimate  # 初始估计值
        self.kalman_gain = 0  # 初始卡尔曼增益

    def update(self, measurement):
        """
        更新卡尔曼滤波器的估计值
        :param measurement: 当前测量值
        :return: 滤波后的估计值
        """
        # 更新卡尔曼增益
        self.kalman_gain = self.estimation_error / (self.estimation_error + self.measurement_variance)

        # 更新估计值
        self.estimate = self.estimate + self.kalman_gain * (measurement - self.estimate)

        # 更新估计误差
        self.estimation_error = (1 - self.kalman_gain) * self.estimation_error + abs(self.estimate) * self.process_variance

        return self.estimate


def apply_kalman_filter(input_file_path, output_file_path, process_variance=1e-5, measurement_variance=0.1):
    """
    对输入文件中的数据应用卡尔曼滤波，并将结果保存到新文件中
    :param input_file_path: 原始数据文件路径
    :param output_file_path: 滤波后数据保存路径
    :param process_variance: 过程噪声方差
    :param measurement_variance: 测量噪声方差
    """
    # 读取输入文件
    data = pd.read_csv(input_file_path, header=None, names=['time', 'acc_x', 'acc_y', 'acc_z', 'gyro_x', 'gyro_y', 'gyro_z'])

    # 确保将时间列转换为 datetime 格式
    data['time'] = pd.to_datetime(data['time'], format='%H:%M:%S.%f')

    # 提取加速度计的三轴数据
    acc_x = data['acc_x'].to_numpy()
    acc_y = data['acc_y'].to_numpy()
    acc_z = data['acc_z'].to_numpy()

    # 初始化卡尔曼滤波器对象
    kf_x = KalmanFilter(process_variance, measurement_variance, estimation_error=1.0, initial_estimate=0.0)
    kf_y = KalmanFilter(process_variance, measurement_variance, estimation_error=1.0, initial_estimate=0.0)
    kf_z = KalmanFilter(process_variance, measurement_variance, estimation_error=1.0, initial_estimate=0.0)

    # 对加速度计数据应用卡尔曼滤波
    filtered_acc_x = np.array([kf_x.update(x) for x in acc_x])
    filtered_acc_y = np.array([kf_y.update(y) for y in acc_y])
    filtered_acc_z = np.array([kf_z.update(z) for z in acc_z])

    # 计算去除重力后的动态加速度
    dynamic_acc_x = acc_x - filtered_acc_x
    dynamic_acc_y = acc_y - filtered_acc_y
    dynamic_acc_z = acc_z - filtered_acc_z

    # 将处理后的数据存储到新的DataFrame
    filtered_data = pd.DataFrame({
        'time': data['time'],
        'dynamic_acc_x': dynamic_acc_x,
        'dynamic_acc_y': dynamic_acc_y,
        'dynamic_acc_z': dynamic_acc_z,
        'gyro_x': data['gyro_x'],
        'gyro_y': data['gyro_y'],
        'gyro_z': data['gyro_z']
    })

    # 提取时间部分（不包括日期）
    filtered_data['time_only'] = filtered_data['time'].dt.time

    # 选择需要保存的列
    columns_to_save = ['time_only', 'dynamic_acc_x', 'dynamic_acc_y', 'dynamic_acc_z']
    filtered_data[columns_to_save].to_csv(output_file_path, index=False, header=False)


# 如果需要测试本模块，可以取消下面这行注释，并提供输入文件和输出文件路径
# apply_kalman_filter("input.csv", "output.csv")
