# -*- coding:utf-8 -*-
"""
测试修改后的AHRS算法 - 使用重力数据进行姿态估计
作者：何志想
日期：2025年07月16日
"""

import os
import sys
from segment_acc_to_world_ahrs import load_segment_files, process_segment_data_ahrs

def test_ahrs_with_gravity():
    """测试使用重力数据进行姿态估计的AHRS算法"""
    
    print("=" * 60)
    print("测试AHRS算法 - 重力数据姿态估计版本")
    print("=" * 60)
    
    # 检查是否有数据文件
    current_dir = os.getcwd()
    test_files = []
    
    # 查找测试数据文件
    for file in os.listdir(current_dir):
        if file.startswith('segment') and 'acce' in file and file.endswith('.csv'):
            test_files.append(file)
    
    if not test_files:
        print("未找到测试数据文件，请确保有segment_acce_*.csv文件")
        return False
    
    print(f"找到 {len(test_files)} 个加速度数据文件")
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    # 提取片段编号和时间戳
    import re
    match = re.search(r'segment(\d+)_acce_(\d+-\d+-\d+\.\d+)\.csv', test_file)
    if match:
        segment_num = int(match.group(1))
        timestamp = match.group(2)
        print(f"片段编号: {segment_num}, 时间戳: {timestamp}")
    else:
        print("无法从文件名提取片段信息，使用默认值")
        segment_num = 1
        timestamp = "test"
    
    # 查找对应的其他传感器文件
    base_pattern = f"segment{segment_num}_"
    grav_file = f"{base_pattern}grav_{timestamp}.csv"
    mag_file = f"{base_pattern}mag_{timestamp}.csv"
    gyro_file = f"{base_pattern}gyro_{timestamp}.csv"
    
    print(f"\n查找对应的传感器文件:")
    print(f"加速度文件: {test_file} {'✓' if os.path.exists(test_file) else '✗'}")
    print(f"重力文件: {grav_file} {'✓' if os.path.exists(grav_file) else '✗'}")
    print(f"磁力文件: {mag_file} {'✓' if os.path.exists(mag_file) else '✗'}")
    print(f"陀螺仪文件: {gyro_file} {'✓' if os.path.exists(gyro_file) else '✗'}")
    
    # 检查必要文件
    if not os.path.exists(grav_file):
        print(f"错误: 找不到重力数据文件 {grav_file}")
        return False
    
    if not os.path.exists(mag_file):
        print(f"错误: 找不到磁力数据文件 {mag_file}")
        return False
    
    try:
        print(f"\n开始测试AHRS算法处理...")
        
        # 加载片段数据
        print(f"加载片段 {segment_num} 的数据...")
        segment_data_dict = load_segment_files(current_dir, segment_num)
        
        if segment_num not in segment_data_dict:
            print(f"错误: 无法加载片段 {segment_num} 的数据")
            return False
        
        segment_data = {segment_num: segment_data_dict[segment_num]}
        print(f"成功加载片段数据，包含传感器: {list(segment_data_dict[segment_num].keys())}")
        
        # 确保有必要的传感器数据
        required_sensors = ['acce', 'grav', 'mag']
        missing_sensors = [sensor for sensor in required_sensors if sensor not in segment_data[segment_num]]
        if missing_sensors:
            print(f"错误: 缺少必要的传感器数据: {missing_sensors}")
            return False
        
        # 测试两种算法
        algorithms = ['madgwick', 'mahony']
        results = {}
        
        for algorithm in algorithms:
            print(f"\n" + "="*40)
            print(f"测试 {algorithm.upper()} 算法")
            print(f"策略: 用重力数据计算姿态，用四元数转换加速度")
            print(f"="*40)
            
            # 调用AHRS算法处理函数
            if algorithm == 'madgwick':
                result = process_segment_data_ahrs(
                    segment_data=segment_data,
                    segment_num=segment_num,
                    output_folder='output',
                    algorithm=algorithm,
                    sample_rate=100.0,
                    beta=0.1,
                    kp=1.0,
                    ki=0.3
                )
            else:  # mahony
                result = process_segment_data_ahrs(
                    segment_data=segment_data,
                    segment_num=segment_num,
                    output_folder='output',
                    algorithm=algorithm,
                    sample_rate=100.0,
                    beta=0.1,
                    kp=1.0,
                    ki=0.3
                )
            
            if result is not None:
                results[algorithm] = result
                print(f"✓ {algorithm.upper()} 算法处理成功!")
                print(f"处理结果统计:")
                print(f"- 总帧数: {len(result)}")
                
                # 检查重力数据是否包含在结果中
                if 'x_grav' in result.columns:
                    print(f"- 包含重力数据: ✓")
                else:
                    print(f"- 包含重力数据: ✗")
                
                # 检查四元数数据
                if 'qw' in result.columns:
                    import numpy as np
                    quat_magnitude = np.sqrt(result['qw']**2 + result['qx']**2 + 
                                           result['qy']**2 + result['qz']**2)
                    print(f"- 四元数模长均值: {np.mean(quat_magnitude):.6f} (应接近1.0)")
                    print(f"- 四元数模长标准差: {np.std(quat_magnitude):.6f}")
                
                # 检查输出文件
                output_files = []
                for file in os.listdir('output'):
                    if f"segment{segment_num}_{algorithm}" in file and timestamp in file:
                        output_files.append(file)
                
                print(f"- 生成输出文件: {len(output_files)} 个")
                for file in output_files:
                    print(f"  {file}")
                
            else:
                print(f"✗ {algorithm.upper()} 算法处理失败")
                return False
        
        if results:
            print(f"\n" + "="*60)
            print(f"🎉 AHRS重力数据姿态估计测试成功!")
            print(f"主要特性:")
            print(f"- ✓ 姿态估计: 使用重力计数据作为输入")
            print(f"- ✓ 坐标转换: 用计算的四元数转换加速度数据")
            print(f"- ✓ 数据分离: 姿态估计和运动检测完全分离")
            print(f"- ✓ 算法支持: Madgwick和Mahony算法")
            print(f"- ✓ 可视化: 包含重力数据和算法策略的详细图表")
            print("="*60)
            return True
        else:
            print("✗ 所有算法测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ahrs_with_gravity()
    if success:
        print("\n" + "="*60)
        print("🎯 测试总结:")
        print("重力数据作为AHRS算法输入的策略已成功实现:")
        print("1. Madgwick算法: 用重力数据计算姿态四元数")
        print("2. Mahony算法: 用重力数据计算姿态四元数") 
        print("3. 坐标转换: 用四元数将加速度数据转换到世界坐标系")
        print("4. 数据完整性: 保存重力数据和加速度数据供后续分析")
        print("5. 可视化增强: 显示数据处理策略和效果对比")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ 测试失败，请检查错误信息")
        print("="*60)
        sys.exit(1)
