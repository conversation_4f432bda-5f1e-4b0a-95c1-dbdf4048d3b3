#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
作者：${何志想}
日期：2025年5月12日
功能：检查音频数据采样率和时长
"""

import os
import numpy as np
import scipy.io.wavfile as wav
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def check_audio_properties(audio_file):
    """检查音频文件的属性"""
    print(f"检查音频文件: {audio_file}")
    
    # 检查文件是否存在
    if not os.path.exists(audio_file):
        print(f"错误: 文件不存在: {audio_file}")
        return
    
    try:
        # 读取音频文件
        sample_rate, data = wav.read(audio_file)
        
        # 获取文件名中的时间戳
        filename = os.path.basename(audio_file)
        timestamp_str = filename.split('_')[2].split('.')[0] + '.' + filename.split('_')[2].split('.')[1]
        
        # 计算音频时长
        duration = len(data) / sample_rate
        
        # 输出基本信息
        print(f"\n基本信息:")
        print(f"采样率: {sample_rate} Hz")
        print(f"数据点数: {len(data)}")
        print(f"音频时长: {duration:.6f} 秒")
        print(f"音频通道数: {data.shape[1] if len(data.shape) > 1 else 1}")
        print(f"数据类型: {data.dtype}")
        print(f"文件大小: {os.path.getsize(audio_file) / (1024*1024):.2f} MB")
        print(f"文件名中的时间戳: {timestamp_str}")
        
        # 画图显示音频波形
        plt.figure(figsize=(12, 6))
        
        # 计算时间轴
        time = np.arange(0, len(data)) / sample_rate
        
        # 如果是多通道, 只画第一个通道
        if len(data.shape) > 1:
            plt.plot(time, data[:, 0])
        else:
            plt.plot(time, data)
            
        plt.title(f"音频波形 - 采样率: {sample_rate}Hz, 时长: {duration:.2f}秒")
        plt.xlabel("时间 (秒)")
        plt.ylabel("振幅")
        plt.grid(True)
        
        # 设置X轴每秒一个刻度
        plt.xticks(np.arange(0, duration + 1, 1.0))
        
        # 保存图像
        output_dir = os.path.dirname(audio_file)
        output_file = os.path.join(output_dir, 'audio_waveform_check.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n已保存波形图: {output_file}")
        
        # 转换到加速度数据时间检查
        check_sync_with_acc_data(audio_file, sample_rate, duration)
        
        plt.show()
        
    except Exception as e:
        print(f"处理音频文件时出错: {e}")

def check_sync_with_acc_data(audio_file, sample_rate, audio_duration):
    """检查音频数据和加速度数据的同步情况"""
    # 构造对应的加速度数据文件路径
    acc_file = audio_file.replace('audio_', 'acce_').replace('.wav', '.csv')
    
    if not os.path.exists(acc_file):
        print(f"\n未找到对应的加速度数据文件: {acc_file}")
        return
    
    print(f"\n检查与加速度数据的同步:")
    print(f"加速度数据文件: {acc_file}")
    
    try:
        # 读取加速度数据的第一行和最后一行获取时间范围
        import pandas as pd
        
        df = pd.read_csv(acc_file)
        
        if len(df) == 0:
            print("警告: 加速度数据文件为空!")
            return
            
        # 获取第一条和最后一条记录的时间
        first_time = df['time'].iloc[0]
        last_time = df['time'].iloc[-1]
        
        # 提取文件名中的时间戳
        filename = os.path.basename(audio_file)
        audio_timestamp = filename.split('_')[2].split('.')[0] + '.' + filename.split('_')[2].split('.')[1]
        
        audio_time = datetime.strptime(audio_timestamp, '%H-%M-%S.%f')
        
        # 将加速度数据的时间转换为datetime对象
        acc_first_time = datetime.strptime(first_time, '%H-%M-%S.%f')
        acc_last_time = datetime.strptime(last_time, '%H-%M-%S.%f')
        
        # 计算时间差
        time_diff_start = (acc_first_time - audio_time).total_seconds()
        time_diff_end = (acc_last_time - audio_time).total_seconds()
        
        # 计算加速度数据的时长
        acc_duration = (acc_last_time - acc_first_time).total_seconds()
        
        print(f"音频开始时间: {audio_timestamp}")
        print(f"加速度数据开始时间: {first_time}")
        print(f"加速度数据结束时间: {last_time}")
        print(f"加速度数据时长: {acc_duration:.6f} 秒")
        print(f"音频时长: {audio_duration:.6f} 秒")
        print(f"音频开始到加速度开始的时间差: {time_diff_start:.6f} 秒")
        print(f"音频开始到加速度结束的时间差: {time_diff_end:.6f} 秒")
        
        # 创建时间线图比较两者
        plt.figure(figsize=(12, 4))
        
        # 计算相对于音频开始时间的刻度
        audio_end_time = audio_duration
        
        # 绘制时间线
        plt.hlines(y=1, xmin=0, xmax=audio_end_time, linewidth=4, color='blue', label='音频数据')
        plt.hlines(y=0, xmin=time_diff_start, xmax=time_diff_end, linewidth=4, color='red', label='加速度数据')
        
        # 添加标注
        plt.text(0, 1.1, f"开始:{audio_timestamp}", fontsize=10)
        plt.text(audio_end_time, 1.1, f"结束:{audio_duration:.2f}秒后", fontsize=10)
        plt.text(time_diff_start, 0.1, f"开始:{first_time}", fontsize=10)
        plt.text(time_diff_end, 0.1, f"结束:{last_time}", fontsize=10)
        
        plt.title("音频数据与加速度数据时间同步关系")
        plt.yticks([0, 1], ['加速度数据', '音频数据'])
        plt.xlabel("相对于音频开始的时间 (秒)")
        plt.grid(True, axis='x')
        plt.legend()
        
        # 保存图像
        output_dir = os.path.dirname(audio_file)
        output_file = os.path.join(output_dir, 'audio_acc_sync_check.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n已保存同步关系图: {output_file}")
        
    except Exception as e:
        print(f"检查同步时出错: {e}")

def inspect_window_calculations(audio_file, window_size=200):
    """检查窗口计算方法的正确性"""
    sample_rate, data = wav.read(audio_file)
    
    # 计算窗口的时间对应关系
    window_time_ms = (window_size / sample_rate) * 1000
    window_time_sec = window_time_ms / 1000
    total_windows = int(len(data) / window_size)
    total_time = len(data) / sample_rate
    
    print("\n窗口计算检查:")
    print(f"采样率: {sample_rate} Hz")
    print(f"窗口大小: {window_size} 样本点")
    print(f"每个窗口时长: {window_time_ms:.2f} 毫秒 ({window_time_sec:.6f} 秒)")
    print(f"总窗口数: {total_windows}")
    print(f"总时长: {total_time:.6f} 秒")
    
    # 验证窗口到时间的转换
    test_windows = [100, 500, 1000]
    print("\n验证窗口索引到时间的转换:")
    for window_idx in test_windows:
        window_time = (window_idx * window_size) / sample_rate
        print(f"窗口 {window_idx} -> 时间: {window_time:.6f} 秒")

if __name__ == "__main__":
    # 设置默认文件路径
    default_path = r"E:\Document\user001\5.12\Session_20250512_185532\audio_data_18-55-32.343.wav"
    
    # 获取用户输入
    file_path = input(f"请输入音频文件路径 [默认: {default_path}]: ")
    file_path = file_path.strip() if file_path.strip() else default_path
    
    # 检查音频属性
    check_audio_properties(file_path)
    
    # 设置窗口大小并检查窗口计算
    window_size = 200  # 默认窗口大小
    window_input = input(f"\n请输入窗口大小 [默认: {window_size}]: ")
    if window_input.strip():
        try:
            window_size = int(window_input)
        except ValueError:
            print(f"无效的窗口大小，使用默认值: {window_size}")
    
    inspect_window_calculations(file_path, window_size)
