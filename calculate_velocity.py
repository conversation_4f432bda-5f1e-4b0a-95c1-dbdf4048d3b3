import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import mplcursors

def standardize_time_format(time_str):
    """标准化时间格式"""
    if '.' not in time_str:
        time_str += '.0000'
    elif len(time_str.split('.')[1]) < 6:
        time_str = time_str.split('.')[0] + '.' + time_str.split('.')[1].ljust(6, '0')
    return time_str

def preprocess_data(file_path):
    """读取数据文件并进行预处理"""
    data = pd.read_csv(file_path, sep=',', header=None, names=['time', 'ax', 'ay', 'az', 'gx', 'gy', 'gz'])
    
    # 去除前50行Kalman滤波的异常数据
    # data = data.iloc[50:]
    data = data.iloc[500:]
    # data = data.iloc[50:350]
    # data = data.iloc[350:650] 

    data['time'] = data['time'].apply(standardize_time_format)
    data['time'] = pd.to_datetime(data['time'], format='%H-%M-%S.%f', errors='coerce')
    timestamps = (data['time'] - data['time'].min()).dt.total_seconds().to_numpy()
    data[['ax', 'ay', 'az']] = data[['ax', 'ay', 'az']].astype(float)
    
    return data, timestamps


def preprocess_data_segments(file_path):
    """读取数据文件并进行预处理"""
    data = pd.read_csv(file_path, sep=',', header=None, names=['time', 'ax', 'ay', 'az', 'gx', 'gy', 'gz'])
    
    # 去除前50行Kalman滤波的异常数据
    # data = data.iloc[50:]
    data1 = data.iloc[50:350]
    data2 = data.iloc[350:650]
    
    data1['time'] = data1['time'].apply(standardize_time_format)
    data1['time'] = pd.to_datetime(data1['time'], format='%H:%M:%S.%f', errors='coerce')
    timestamps1 = (data1['time'] - data1['time'].min()).dt.total_seconds().to_numpy()
    data1[['ax', 'ay', 'az']] = data1[['ax', 'ay', 'az']].astype(float)

    data2['time'] = data2['time'].apply(standardize_time_format)
    data2['time'] = pd.to_datetime(data2['time'], format='%H:%M:%S.%f', errors='coerce')
    timestamps2 = (data2['time'] - data2['time'].min()).dt.total_seconds().to_numpy()
    data2[['ax', 'ay', 'az']] = data2[['ax', 'ay', 'az']].astype(float)

    # data['time'] = data['time'].apply(standardize_time_format)
    # data['time'] = pd.to_datetime(data['time'], format='%H:%M:%S.%f', errors='coerce')
    # timestamps = (data['time'] - data['time'].min()).dt.total_seconds().to_numpy()
    # data[['ax', 'ay', 'az']] = data[['ax', 'ay', 'az']].astype(float)
    
    # return data, timestamps
    return data1, timestamps1, data2, timestamps2

def calculate_velocity(data, timestamps):
    """计算每一步速度矢量的模长"""
    ax, ay, az = data['ax'].to_numpy(), data['ay'].to_numpy(), data['az'].to_numpy()
    dt = np.diff(timestamps)
    dt = np.append(dt, dt[-1])
    vel_vectors = [np.array([0.0, 0.0, 0.0])]

    for i in range(1, len(timestamps)):
        delta_t = dt[i - 1]
        acc = np.array([ax[i], ay[i], az[i]])
        vel_vector = vel_vectors[-1] + acc * delta_t
        vel_vectors.append(vel_vector)

    velocities = [np.linalg.norm(v) * 100 for v in vel_vectors[1:]]
    return np.array(velocities)

def calculate_velocity_axis(data, timestamps):
    """计算每一步每一轴速度矢量的模长"""
    ax, ay, az = data['ax'].to_numpy(), data['ay'].to_numpy(), data['az'].to_numpy()
    dt = np.diff(timestamps)
    dt = np.append(dt, dt[-1])
    vel_vectors = [np.array([0.0, 0.0, 0.0])]

    for i in range(1, len(timestamps)):
        delta_t = dt[i - 1]
        acc = np.array([ax[i], ay[i], az[i]])
        vel_vector = vel_vectors[-1] + acc * delta_t
        vel_vectors.append(vel_vector)

    # velocities = [vel_vectors[i] * 100 for i in range(1, len(vel_vectors))]
    velocities = np.array(vel_vectors)

    # 计算每一轴的速度大小,取绝对值
    vel_x = velocities[:, 0] * 100
    vel_y = velocities[:, 1] * 100
    vel_z = velocities[:, 2] * 100
    # vel_x = np.abs(velocities[:, 0]) * 100
    # vel_y = np.abs(velocities[:, 1]) * 100
    # vel_z = np.abs(velocities[:, 2]) * 100

    return vel_x, vel_y, vel_z

def calculate_velocity_segments(data, timestamps, segment_size = 600):
    """计算每一步速度矢量的模长，每300个样本点重置速度初始值为0，返回n个长度为300的片段"""

    ax, ay, az = data['ax'].to_numpy(), data['ay'].to_numpy(), data['az'].to_numpy()
    dt = np.diff(timestamps)
    dt = np.append(dt, dt[-1])

    # 用于存储每个片段的速度模长数组
    segments_velocities = []

    # 按照 segment_size 划分数据并逐段计算速度
    for start_idx in range(0, len(timestamps), segment_size):
        end_idx = min(start_idx + segment_size, len(timestamps))

        # 初始化每个片段的速度向量为0
        segment_vel_vectors = [np.array([0.0, 0.0, 0.0])]

        # 计算当前片段内的速度
        for i in range(start_idx + 1, end_idx):
            delta_t = dt[i - 1]
            acc = np.array([ax[i], ay[i], az[i]])
            vel_vector = segment_vel_vectors[-1] + acc * delta_t
            segment_vel_vectors.append(vel_vector)

        # 计算当前片段的速度模长并乘以100
        segment_velocities = [np.linalg.norm(v) * 100 for v in segment_vel_vectors[1:]]

        # 将当前片段的速度模长数组添加到列表中
        segments_velocities.append(np.array(segment_velocities))

    return segments_velocities

def calculate_velocity_3d(data, timestamps):
    """计算每一步速度矢量并返回三维速度矢量数据"""
    ax, ay, az = data['ax'].to_numpy(), data['ay'].to_numpy(), data['az'].to_numpy()
    dt = np.diff(timestamps)
    dt = np.append(dt, dt[-1])  # 假设最后一段时间与前一段时间相同
    vel_vectors = [np.array([0.0, 0.0, 0.0])]  # 初始速度矢量

    for i in range(1, len(timestamps)):
        delta_t = dt[i - 1]
        acc = np.array([ax[i], ay[i], az[i]])  # 当前加速度矢量
        vel_vector = vel_vectors[-1] + acc * delta_t  # 速度矢量更新
        vel_vectors.append(vel_vector)

    # 转换为 numpy 数组
    vel_vectors = np.array(vel_vectors)

    # 返回速度矢量数据
    return vel_vectors

# 修改比例函数
def set_aspect_equal_3d(ax, scale=(10, 1, 1)):
    ax.get_proj = lambda: np.dot(Axes3D.get_proj(ax), np.diag(scale + (1,)))

def plot_velocity_3d(vel_vectors, base_spacing=100, factor=2):
    """
    绘制三维速度矢量图，每个矢量的起点按指定间隔和因子递增。
    
    Parameters:
        vel_vectors: np.array, 速度矢量
        base_spacing: int, 基础间隔
        factor: int, 间隔递增因子
    """
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')

    # 起点沿 X 轴按递增因子逐渐分开
    start_points = [np.array([i * base_spacing * factor, 0, 0]) for i in range(len(vel_vectors))]
    start_points = np.array(start_points)

    # 绘制速度矢量
    for i in range(len(vel_vectors)):
        ax.quiver(start_points[i, 0], start_points[i, 1], start_points[i, 2],
                  vel_vectors[i, 0], vel_vectors[i, 1], vel_vectors[i, 2],
                  length=np.linalg.norm(vel_vectors[i]), 
                  normalize=True, 
                  color='blue',
                  arrow_length_ratio=0.05)


    # 设置轴标签
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title('3D Velocity Vectors with Larger Intervals')

    

    plt.show()

def plot_velocity_axis(vel_x, vel_y, vel_z):
    """绘制每一轴速度大小"""
    fig, ax = plt.subplots(3, 1, figsize=(10, 12))

    # 控制三张表格之间的间隔
    plt.subplots_adjust(hspace=0.5)

    # 显示速度图
    ax[0].plot(range(len(vel_x)), vel_x, label='X Axis speed (cm/s)')
    ax[0].set_title('X Axis speed over Time')
    ax[0].set_xlabel('Data Index')
    ax[0].set_ylabel('speed (cm/s)')
    ax[0].legend()

    ax[1].plot(range(len(vel_y)), vel_y, label='Y Axis speed (cm/s)')
    ax[1].set_title('Y Axis speed over Time')
    ax[1].set_xlabel('Data Index')
    ax[1].set_ylabel('speed (cm/s)')
    ax[1].legend()

    ax[2].plot(range(len(vel_z)), vel_z, label='Z Axis speed (cm/s)')
    ax[2].set_title('Z Axis speed over Time')
    ax[2].set_xlabel('Data Index')
    ax[2].set_ylabel('speed (cm/s)')
    ax[2].legend()

    plt.show()



class KalmanFilter:
    def __init__(self, process_variance, measurement_variance, estimation_error, initial_estimate):
        self.process_variance = process_variance  # 过程噪声的方差
        self.measurement_variance = measurement_variance  # 测量噪声的方差
        self.estimation_error = estimation_error  # 初始估计误差
        self.estimate = initial_estimate  # 初始估计值
        self.kalman_gain = 0  # 初始卡尔曼增益

    def update(self, measurement):
        # 更新卡尔曼增益
        self.kalman_gain = self.estimation_error / (self.estimation_error + self.measurement_variance)

        # 更新估计值
        self.estimate = self.estimate + self.kalman_gain * (measurement - self.estimate)

        # 更新估计误差
        self.estimation_error = (1 - self.kalman_gain) * self.estimation_error + abs(self.estimate) * self.process_variance

        return self.estimate

def plot_acceleration_axis(file_path):
    """读取数据并可视化原始加速度和去除重力后的动态加速度"""
    # 读取数据
    # data = pd.read_csv(file_path, header=None, names=['time', 'acc_x', 'acc_y', 'acc_z', 'gyro_x', 'gyro_y', 'gyro_z'])
    data = pd.read_csv(file_path, header=None, names=['time', 'acc_x', 'acc_y', 'acc_z'])

    # 确保将时间列转换为 datetime 格式
    # data['time'] = pd.to_datetime(data['time'], format='%H:%M:%S.%f')
    data['time'] = data['time'].apply(lambda x: pd.to_datetime(x, unit='ms'))

    # 计算时间间隔（以秒为单位）
    data['time_diff'] = (data['time'] - data['time'].min()).dt.total_seconds()

    # 提取加速度计的三轴数据
    acc_x = data['acc_x'].to_numpy()
    acc_y = data['acc_y'].to_numpy()
    acc_z = data['acc_z'].to_numpy()

    # 初始化卡尔曼滤波器参数
    process_variance = 1e-5  # 预测值噪声方差
    measurement_variance = 0.1  # 测量值噪声方差
    initial_estimate = 0.0  # 初始估计值
    estimation_error = 1.0  # 初始估计误差

    # 初始化卡尔曼滤波器对象
    kf_x = KalmanFilter(process_variance, measurement_variance, estimation_error, initial_estimate)
    kf_y = KalmanFilter(process_variance, measurement_variance, estimation_error, initial_estimate)
    kf_z = KalmanFilter(process_variance, measurement_variance, estimation_error, initial_estimate)

    # 使用卡尔曼滤波
    filtered_acc_x = np.array([kf_x.update(x) for x in acc_x])
    filtered_acc_y = np.array([kf_y.update(y) for y in acc_y])
    filtered_acc_z = np.array([kf_z.update(z) for z in acc_z])

    # 计算去除重力后的动态加速度
    dynamic_acc_x = acc_x - filtered_acc_x
    dynamic_acc_y = acc_y - filtered_acc_y
    dynamic_acc_z = acc_z - filtered_acc_z

    # 可视化原始加速度和去除重力后的动态加速度
    plt.figure(figsize=(12, 6))
    plt.subplot(3, 1, 1)
    plt.plot(data['time_diff'], acc_x, label='Original acc_x', linestyle='--')
    plt.plot(data['time_diff'], dynamic_acc_x, label='Dynamic acc_x (filtered)')
    plt.legend()

    plt.subplot(3, 1, 2)
    plt.plot(data['time_diff'], acc_y, label='Original acc_y', linestyle='--')
    plt.plot(data['time_diff'], dynamic_acc_y, label='Dynamic acc_y (filtered)')
    plt.legend()

    plt.subplot(3, 1, 3)
    plt.plot(data['time_diff'], acc_z, label='Original acc_z', linestyle='--')
    plt.plot(data['time_diff'], dynamic_acc_z, label='Dynamic acc_z (filtered)')
    plt.legend()

    plt.tight_layout()
    plt.show()