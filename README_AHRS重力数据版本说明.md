# AHRS算法修改说明 - 重力数据姿态估计版本

## 修改日期
2025年7月17日

## 修改目标
将原本使用加速度数据进行姿态估计的AHRS算法修改为使用重力计数据进行姿态估计，然后用计算得到的四元数对加速度数据进行世界坐标系转换。

## 核心修改内容

### 1. 数据输入策略变更

**原策略：**
- 姿态估计输入：加速度数据 (acce)
- 坐标转换目标：同样是加速度数据 (acce)

**新策略：**
- 姿态估计输入：重力计数据 (grav) 
- 坐标转换目标：加速度数据 (acce)

### 2. 算法处理流程

#### Madgwick算法修改
```python
# 修改前
madgwick = Madgwick(gyr=gyro_data, acc=accel_data, mag=mag_data, 
                  frequency=sample_rate, beta=beta)

# 修改后  
gravity_data = sync_data['grav']  # 使用重力数据进行姿态估计
actual_accel_data = sync_data['accel']  # 实际要转换的加速度数据
madgwick = Madgwick(gyr=gyro_data, acc=gravity_data, mag=mag_data, 
                  frequency=sample_rate, beta=beta)
```

#### Mahony算法修改
```python
# 修改前
mahony = Mahony(gyr=gyro_data, acc=accel_data, mag=mag_data, 
               frequency=sample_rate, k_P=kp, k_I=ki)

# 修改后
gravity_data = sync_data['grav']  # 使用重力数据进行姿态估计  
actual_accel_data = sync_data['accel']  # 实际要转换的加速度数据
mahony = Mahony(gyr=gyro_data, acc=gravity_data, mag=mag_data, 
               frequency=sample_rate, k_P=kp, k_I=ki)
```

### 3. 坐标转换过程
```python
# 使用基于重力数据计算的四元数转换实际加速度数据
for i, q in enumerate(quaternions_all):
    quat = Quaternion(q)
    rotation_matrix = quat.to_DCM()  
    # 应用旋转矩阵到实际加速度数据
    acc_world = np.dot(rotation_matrix, actual_accel_data[i])
    acc_world_all.append(acc_world)
```

### 4. 数据保存增强
新增保存重力数据到结果文件中：
```python
# 添加重力数据（用于姿态估计的输入数据）
result_df['x_grav'] = sync_data['grav'][:, 0]
result_df['y_grav'] = sync_data['grav'][:, 1] 
result_df['z_grav'] = sync_data['grav'][:, 2]
```

### 5. 可视化改进
- 新增重力数据可视化面板
- 添加算法策略说明图表
- 扩展为4行2列布局显示更多信息
- 明确标注数据用途：姿态估计 vs 坐标转换

## 技术优势

### 1. 数据分离
- **姿态估计**：基于重力计数据，更稳定可靠
- **运动检测**：基于加速度数据，保留真实运动信息

### 2. 减少噪声影响
- 重力计数据通常比加速度计数据更稳定
- 避免运动加速度对姿态估计的干扰

### 3. 更高精度
- 姿态估计基于重力场参考，物理意义更明确
- 世界坐标系转换更准确

## 文件修改清单

### 主要修改文件
- `segment_acc_to_world_ahrs.py` - AHRS算法核心文件

### 新增测试文件  
- `test_ahrs_gravity.py` - 验证重力数据姿态估计的测试脚本

## 使用示例

### 数据文件要求
确保有以下格式的数据文件：
```
segment1_acce_22-10-23.144.csv  # 加速度数据（转换目标）
segment1_grav_22-10-23.149.csv  # 重力数据（姿态估计输入）
segment1_mag_22-10-23.149.csv   # 磁力数据
segment1_gyro_22-10-23.149.csv  # 陀螺仪数据（可选）
```

### 运行命令
```bash
# 运行AHRS算法
python segment_acc_to_world_ahrs.py

# 运行测试验证
python test_ahrs_gravity.py
```

### 输出文件
算法会生成以下文件：
- `segment1_madgwick_ahrs_world_acce_*.csv` - 世界坐标系加速度数据
- `segment1_madgwick_ahrs_quaternions_*.csv` - 四元数数据
- `segment1_madgwick_ahrs_acceleration_analysis_*.png` - 可视化图表

## 验证要点

### 1. 四元数质量检查
- 四元数模长应接近1.0
- 模长标准差应较小

### 2. 重力分离效果
- Z轴（上向）应主要包含重力分量
- X、Y轴应反映水平运动

### 3. 数据完整性
- 输出文件应包含重力数据列（x_grav, y_grav, z_grav）
- 可视化应显示算法策略说明

## 注意事项

1. **时间同步**：确保重力数据和加速度数据时间戳匹配
2. **采样率**：所有传感器数据应具有相同或兼容的采样率
3. **数据质量**：重力数据质量直接影响姿态估计精度
4. **算法参数**：可能需要根据具体应用调整Madgwick/Mahony参数

## 兼容性说明

- 完全兼容原有的简单算法模式
- 支持有/无陀螺仪的数据处理
- 保持原有的批处理和可视化功能

---

**作者**: 何志想  
**日期**: 2025年7月17日  
**版本**: AHRS重力数据姿态估计版本
