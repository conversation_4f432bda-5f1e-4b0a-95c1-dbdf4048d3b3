# AHRS库版本传感器融合工具使用说明

## 概述

`segment_acc_to_world_ahrs.py` 是一个使用标准AHRS库（Attitude and Heading Reference Systems）的高精度姿态估计和坐标系转换工具。它集成了Madgwick和Mahony算法的官方实现，用于将设备坐标系下的加速度数据转换到世界坐标系。

## 主要特点

### 1. **标准AHRS库支持**
- 使用官方AHRS库的Madgwick和Mahony算法实现
- 确保算法的准确性和标准化
- 支持完整的四元数姿态估计

### 2. **多种算法选择**
- **Madgwick算法**: 计算效率高，适合实时应用
- **Mahony算法**: 高精度，特别适合有陀螺仪的场景
- **简单方法**: 基础坐标系构建方法，无需AHRS库

### 3. **灵活的传感器配置**
- 支持加速度计+磁力计+陀螺仪（推荐）
- 支持加速度计+磁力计（无陀螺仪模式）
- 自动检测可用传感器并适配

### 4. **可调参数**
- **Madgwick算法**: beta参数控制收敛速度
- **Mahony算法**: kp（比例增益）和ki（积分增益）参数
- 采样率、平滑窗口等可配置

## 依赖要求

```bash
pip install ahrs numpy pandas matplotlib
```

### 验证安装

运行测试脚本确认环境正确：
```bash
python test_ahrs.py
```

## 使用方法

### 1. **交互式使用**

```bash
python segment_acc_to_world_ahrs.py
```

程序会引导您完成以下设置：
- 输入数据文件夹路径
- 选择算法（Madgwick/Mahony/简单方法）
- 设置算法参数
- 选择是否计算速度和位移

### 2. **编程接口使用**

```python
from segment_acc_to_world_ahrs import process_all_segments_ahrs

# 处理所有片段数据
process_all_segments_ahrs(
    data_folder="path/to/data",
    algorithm='madgwick',  # 或 'mahony', 'simple'
    beta=0.1,             # Madgwick参数
    kp=1.0, ki=0.3,       # Mahony参数
    sample_rate=100.0,    # 采样率
    calculate_kinematics=True  # 计算速度和位移
)
```

### 3. **单片段处理**

```python
from segment_acc_to_world_ahrs import (
    load_segment_files, 
    process_segment_data_ahrs
)

# 加载数据
segment_data = load_segment_files("data_folder")

# 处理特定片段
result_df = process_segment_data_ahrs(
    segment_data, 
    segment_num=1, 
    output_folder="output",
    algorithm='madgwick',
    beta=0.1
)
```

## 算法参数说明

### Madgwick算法参数

- **beta (默认: 0.1)**: 算法增益参数
  - 较大值：快速收敛，但可能有噪声
  - 较小值：平滑输出，但收敛较慢
  - 推荐范围：0.01 - 0.5

### Mahony算法参数

- **kp (默认: 1.0)**: 比例增益
  - 控制姿态估计的响应速度
  - 推荐范围：0.5 - 2.0

- **ki (默认: 0.3)**: 积分增益
  - 控制陀螺仪偏差的补偿
  - 推荐范围：0.0 - 1.0
  - 设为0则禁用积分项

## 输出文件说明

### 主要输出文件

1. **世界坐标系加速度**: `segment{N}_{algorithm}_ahrs_world_acce_{timestamp}.csv`
   - `x`: 东向加速度 (m/s²)
   - `y`: 北向加速度 (m/s²)
   - `z`: 上向加速度 (m/s²)
   - `x_raw`, `y_raw`, `z_raw`: 原始加速度
   - `qw`, `qx`, `qy`, `qz`: 姿态四元数

2. **四元数数据**: `segment{N}_{algorithm}_ahrs_quaternions_{timestamp}.csv`
   - 独立的四元数时间序列

3. **可视化图表**: `segment{N}_{algorithm}_ahrs_acceleration_analysis_{timestamp}.png`
   - 6个子图的综合分析

4. **运动学数据** (可选): `segment{N}_{algorithm}_ahrs_kinematics_{timestamp}.csv`
   - 包含速度和位移信息

### 可视化内容

1. **原始加速度** (设备坐标系)
2. **世界坐标系加速度** (东-北-上坐标系)
3. **姿态四元数时间序列**
4. **四元数模长验证** (应接近1)
5. **加速度幅值比较**
6. **重力分离效果** (Z轴分析)

## 与手写版本的对比

| 特性 | AHRS库版本 | 手写版本 |
|------|------------|----------|
| 算法实现 | 官方标准实现 | 自定义实现 |
| 精度 | 高（经过充分测试） | 中等 |
| 可信度 | 高（广泛使用） | 需要验证 |
| 性能 | 优化的C/Python混合 | 纯Python |
| 功能完整性 | 完整AHRS功能 | 基础功能 |
| 维护性 | 由社区维护 | 需要自己维护 |
| 依赖 | 需要ahrs库 | 无额外依赖 |

## 最佳实践建议

### 1. **算法选择**
- **有陀螺仪数据**: 推荐Madgwick算法，速度快精度好
- **高精度需求**: 使用Mahony算法，调整kp和ki参数
- **简单快速**: 使用简单方法，无需AHRS库

### 2. **参数调优**
- 从默认参数开始
- 观察四元数模长是否接近1
- 检查Z轴重力分离效果
- 根据应用场景微调

### 3. **数据质量检查**
- 确保传感器数据时间同步
- 检查磁力计数据是否有大的扰动
- 验证陀螺仪偏差是否合理

### 4. **结果验证**
- 检查四元数的连续性
- 验证世界坐标系Z轴的重力分量
- 比较不同算法的结果差异

## 故障排除

### 常见问题

1. **AHRS库导入失败**
   ```bash
   pip install ahrs
   ```

2. **四元数模长偏离1**
   - 检查传感器数据质量
   - 调整算法参数
   - 确认采样率设置正确

3. **结果不稳定**
   - 增加平滑窗口大小
   - 检查磁力计环境干扰
   - 降低算法增益参数

4. **处理速度慢**
   - 减少数据量
   - 使用Madgwick替代Mahony
   - 调整采样率

## 技术支持

如有问题，请检查：
1. 数据文件格式是否正确
2. AHRS库版本是否兼容
3. 传感器数据是否完整
4. 参数设置是否合理

## 更新日志

- **v1.0**: 初始版本，支持AHRS库Madgwick和Mahony算法
- 集成标准化姿态估计算法
- 支持完整的传感器融合流程
- 提供详细的可视化和统计分析
