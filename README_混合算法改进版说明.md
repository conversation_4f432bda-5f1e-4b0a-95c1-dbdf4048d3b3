# 混合算法说明文档
## 改进版 - 使用重力数据进行姿态估计

### 算法核心思想

这个改进的混合算法采用了一种创新的策略：**分离姿态估计与坐标转换**

#### 传统方法的问题
- 传统AHRS算法使用实际加速度数据进行姿态估计
- 运动产生的加速度会干扰重力方向的估计
- 导致姿态估计不准确，影响坐标转换效果

#### 改进方案的优势
- **姿态估计**: 使用重力计数据(grav)，不受运动加速度干扰
- **坐标转换**: 用计算的四元数转换实际加速度数据(acce)
- **结果**: 更准确的姿态估计，更精确的坐标转换

### 算法流程

#### 1. 静态初始化阶段
```
输入: 重力数据(grav) + 磁力计数据(mag)
方法: 静态方法3构建初始世界坐标系
输出: 初始旋转矩阵 + 初始四元数
```

#### 2. 动态更新阶段  
```
输入: 重力数据(grav) + 磁力计数据(mag) + 陀螺仪数据(gyro)
方法: Mahony算法基于重力数据计算姿态
输出: 动态四元数序列
```

#### 3. 坐标转换阶段
```
输入: 实际加速度数据(acce) + 计算的四元数
方法: 四元数旋转变换
输出: 世界坐标系下的加速度
```

### 数据流向图

```
原始数据:
├── segment1_grav_*.csv  → Mahony算法 → 四元数估计
├── segment1_mag_*.csv   → Mahony算法 → 
├── segment1_gyro_*.csv  → Mahony算法 → 
└── segment1_acce_*.csv  → 坐标转换 → 世界坐标系加速度

静止段: 重力+磁力 → 静态方法3 → 初始坐标系
运动段: 重力+磁力+陀螺仪 → Mahony → 动态四元数
转换: 加速度+四元数 → 旋转变换 → 结果
```

### 关键改进点

#### 1. **数据分离使用**
- **姿态估计数据**: `segment1_grav_*.csv` (重力计)
- **转换目标数据**: `segment1_acce_*.csv` (加速度计)
- **辅助数据**: `segment1_mag_*.csv` (磁力计), `segment1_gyro_*.csv` (陀螺仪)

#### 2. **抗干扰能力**
- 重力计数据相对稳定，不含运动加速度
- Mahony算法基于重力方向进行姿态估计
- 避免了运动加速度对算法的干扰

#### 3. **精度提升**
- 姿态估计更准确（基于纯重力方向）
- 坐标转换更精确（使用准确的四元数）
- 特别适合动态运动场景

### 输出文件格式

#### 主要输出文件
1. **世界坐标系加速度**: `segment{N}_hybrid_world_acce_{timestamp}.csv`
   ```csv
   time,x,y,z,x_raw,y_raw,z_raw,x_grav,y_grav,z_grav,qw,qx,qy,qz,motion_phase
   ```
   - `x,y,z`: 世界坐标系加速度 (东-北-上)
   - `x_raw,y_raw,z_raw`: 原始加速度数据 (acce)
   - `x_grav,y_grav,z_grav`: 重力数据 (grav)
   - `qw,qx,qy,qz`: 基于重力数据计算的四元数
   - `motion_phase`: 运动阶段标记 (static/dynamic)

2. **四元数数据**: `segment{N}_hybrid_quaternions_{timestamp}.csv`
3. **处理摘要**: `segment{N}_hybrid_summary_{timestamp}.txt`

#### 可视化图表
- 加速度vs重力数据对比
- 四元数变化曲线
- 算法切换点分析
- 运动阶段区分显示

### 参数说明

#### Mahony算法参数
- **kp**: 比例增益，控制姿态估计响应速度 (默认: 1.0)
- **ki**: 积分增益，控制陀螺仪偏差补偿 (默认: 0.3)

#### 运动检测参数
- **static_threshold**: 静止判断阈值 (默认: 0.5 m/s²)
- **static_duration**: 静止状态最小持续帧数 (默认: 5)

### 适用场景

#### 最佳适用场景
- **动态运动分析**: 人体运动、机器人运动等
- **高精度要求**: 需要准确姿态估计的应用
- **多传感器环境**: 有重力计、加速度计、磁力计、陀螺仪

#### 与其他版本对比
| 特性 | 改进混合版本 | 标准AHRS版本 | 手写版本 |
|------|-------------|-------------|----------|
| 抗运动干扰 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| 姿态精度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| 动态适应性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| 数据要求 | 高 | 中 | 中 |
| 计算复杂度 | 中 | 中 | 低 |

### 使用建议

#### 1. **数据质量要求**
- 确保重力计数据质量良好
- 磁力计环境干扰要小
- 陀螺仪偏差要在合理范围内

#### 2. **参数调优顺序**
1. 先调整运动检测参数（static_threshold, static_duration）
2. 再调整Mahony参数（kp, ki）
3. 最后微调采样率和平滑参数

#### 3. **结果验证方法**
1. 检查四元数模长是否接近1
2. 验证静态段的重力分离效果
3. 观察动态段的姿态变化连续性
4. 对比重力数据和加速度数据的差异

### 技术创新点

#### 1. **数据用途分离**
- 传统: 加速度数据既用于姿态估计，又用于坐标转换
- 改进: 重力数据用于姿态估计，加速度数据用于坐标转换

#### 2. **抗干扰设计**
- 重力计数据相对稳定，不受运动加速度影响
- Mahony算法基于稳定的重力方向进行计算

#### 3. **混合策略优化**
- 静态段用静态方法建立基准
- 动态段用Mahony算法跟踪变化
- 数据流向优化，避免误差累积

### 总结

这个改进的混合算法通过分离姿态估计和坐标转换的数据源，有效提高了算法的抗干扰能力和精度。特别适合于动态运动场景下的高精度姿态估计和坐标转换需求。

**核心优势**: 使用重力数据进行姿态估计，避免运动加速度干扰，提供更准确的世界坐标系转换结果。
