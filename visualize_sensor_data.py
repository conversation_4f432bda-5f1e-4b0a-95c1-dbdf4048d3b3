# -*- coding: utf-8 -*-
"""
传感器数据可视化工具
作者：何志想
日期：2025年7月2日
功能：可视化传感器数据（加速度计、陀螺仪、磁力计等），以时间为横坐标，传感器数值为纵坐标
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import glob
import re

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def time_to_seconds(time_str, base_time=None):
    """
    将时间字符串转换为相对秒数
    
    Args:
        time_str: 时间字符串，格式如 "21-52-56.527"
        base_time: 基准时间，如果为None则使用第一个时间点作为基准
        
    Returns:
        相对于基准时间的秒数
    """
    if not isinstance(time_str, str):
        return 0
    
    try:
        time_str = time_str.strip()
        if '.' in time_str:
            dt = datetime.strptime(time_str, '%H-%M-%S.%f')
        else:
            dt = datetime.strptime(time_str, '%H-%M-%S')
        
        total_seconds = dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1e6
        
        if base_time is not None:
            return total_seconds - base_time
        return total_seconds
        
    except ValueError as e:
        print(f"无法解析时间字符串: {time_str}，错误: {e}")
        return 0

def load_sensor_data(file_path):
    """
    加载传感器数据文件
    
    Args:
        file_path: 数据文件路径
        
    Returns:
        pandas DataFrame
    """
    try:
        df = pd.read_csv(file_path)
        print(f"成功加载文件: {os.path.basename(file_path)}")
        print(f"数据形状: {df.shape}")
        print(f"数据列: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"加载文件失败: {file_path}，错误: {e}")
        return None

def preprocess_data(df):
    """
    预处理数据，添加时间秒数列
    
    Args:
        df: 原始数据DataFrame
        
    Returns:
        处理后的DataFrame
    """
    if df is None or 'time' not in df.columns:
        return df
    
    # 计算基准时间（第一个时间点）
    base_time = time_to_seconds(df['time'].iloc[0])
    
    # 转换所有时间点为相对秒数
    df['time_seconds'] = df['time'].apply(lambda x: time_to_seconds(x, base_time))
    
    print(f"时间范围: {df['time_seconds'].min():.3f}s - {df['time_seconds'].max():.3f}s")
    print(f"数据时长: {df['time_seconds'].max() - df['time_seconds'].min():.3f}s")
    
    # 输出各轴统计信息
    print("\n各轴统计信息:")
    for axis in ['x', 'y', 'z']:
        data = df[axis].values
        mean_val = np.mean(data)
        std_val = np.std(data)
        min_val = np.min(data)
        max_val = np.max(data)
        print(f"  {axis.upper()}轴: 均值={mean_val:.4f}, 标准差={std_val:.4f}, 范围=[{min_val:.4f}, {max_val:.4f}]")
    
    return df

def detect_sensor_type(file_path):
    """
    从文件名检测传感器类型
    
    Args:
        file_path: 文件路径
        
    Returns:
        传感器类型字符串
    """
    filename = os.path.basename(file_path).lower()
    
    if 'acce' in filename:
        return 'accelerometer'
    elif 'gyro' in filename:
        return 'gyroscope'
    elif 'mag' in filename:
        return 'magnetometer'
    elif 'grav' in filename:
        return 'gravity'
    elif 'quat' in filename or 'rotvec' in filename or 'rotation' in filename:
        return 'quaternion'
    else:
        return 'unknown'

def get_sensor_info(sensor_type):
    """
    获取传感器相关信息
    
    Args:
        sensor_type: 传感器类型
        
    Returns:
        包含标题、单位、颜色等信息的字典
    """
    sensor_configs = {
        'accelerometer': {
            'title': '加速度计数据',
            'unit': 'm/s²',
            'colors': ['red', 'green', 'blue'],
            'labels': ['X轴加速度', 'Y轴加速度', 'Z轴加速度']
        },
        'gyroscope': {
            'title': '陀螺仪数据',
            'unit': 'rad/s',
            'colors': ['red', 'green', 'blue'],
            'labels': ['X轴角速度', 'Y轴角速度', 'Z轴角速度']
        },
        'magnetometer': {
            'title': '磁力计数据',
            'unit': 'μT',
            'colors': ['red', 'green', 'blue'],
            'labels': ['X轴磁场', 'Y轴磁场', 'Z轴磁场']
        },
        'gravity': {
            'title': '重力传感器数据',
            'unit': 'm/s²',
            'colors': ['red', 'green', 'blue'],
            'labels': ['X轴重力', 'Y轴重力', 'Z轴重力']
        },
        'quaternion': {
            'title': '四元数/旋转向量数据',
            'unit': '',
            'colors': ['red', 'green', 'blue', 'purple'],
            'labels': ['X分量', 'Y分量', 'Z分量', 'W分量/标量']
        },
        'unknown': {
            'title': '传感器数据',
            'unit': '',
            'colors': ['red', 'green', 'blue'],
            'labels': ['X轴数据', 'Y轴数据', 'Z轴数据']
        }
    }
    
    return sensor_configs.get(sensor_type, sensor_configs['unknown'])

def visualize_sensor_data(file_path, output_folder=None, show_plot=True):
    """
    可视化单个传感器数据文件
    
    Args:
        file_path: 数据文件路径
        output_folder: 输出图片的文件夹，如果为None则不保存
        show_plot: 是否显示图片
    """
    # 加载数据
    df = load_sensor_data(file_path)
    if df is None:
        return
    
    # 预处理数据
    df = preprocess_data(df)
    
    # 检查必要的列是否存在
    required_cols = ['x', 'y', 'z', 'time_seconds']
    if not all(col in df.columns for col in required_cols):
        print(f"数据文件缺少必要的列: {required_cols}")
        return
    
    # 检测传感器类型并选择合适的可视化方法
    sensor_type = detect_sensor_type(file_path)
    
    if sensor_type == 'quaternion':
        # 对于四元数/旋转向量数据，使用专门的可视化函数
        visualize_quaternion_data(file_path, output_folder, show_plot)
    else:
        # 对于其他传感器数据，使用通用可视化函数
        visualize_general_sensor_data(file_path, output_folder, show_plot)

def visualize_general_sensor_data(file_path, output_folder=None, show_plot=True):
    """
    通用传感器数据可视化函数（原visualize_sensor_data函数）
    
    Args:
        file_path: 数据文件路径
        output_folder: 输出图片的文件夹，如果为None则不保存
        show_plot: 是否显示图片
    """
    # 加载数据
    df = load_sensor_data(file_path)
    if df is None:
        return
    
    # 预处理数据
    df = preprocess_data(df)
    
    # 检查必要的列是否存在
    required_cols = ['x', 'y', 'z', 'time_seconds']
    if not all(col in df.columns for col in required_cols):
        print(f"数据文件缺少必要的列: {required_cols}")
        return
    
    # 检测传感器类型
    sensor_type = detect_sensor_type(file_path)
    sensor_info = get_sensor_info(sensor_type)
    
    # 进行详细统计分析
    stats = analyze_sensor_statistics(df, sensor_type)
    if stats:
        print_detailed_statistics(stats, sensor_info)
    
    # 创建图形 - 使用2x2的子图布局
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 获取时间数据
    time_data = df['time_seconds'].values
    
    # 绘制X轴数据
    ax1.plot(time_data, df['x'].values, color=sensor_info['colors'][0], 
             linewidth=1.5, alpha=0.8)
    ax1.set_title(f'{sensor_info["labels"][0]}', fontsize=12, fontweight='bold')
    ax1.set_xlabel('时间 (秒)', fontsize=10)
    ax1.set_ylabel(f'数值 ({sensor_info["unit"]})', fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 绘制Y轴数据
    ax2.plot(time_data, df['y'].values, color=sensor_info['colors'][1], 
             linewidth=1.5, alpha=0.8)
    ax2.set_title(f'{sensor_info["labels"][1]}', fontsize=12, fontweight='bold')
    ax2.set_xlabel('时间 (秒)', fontsize=10)
    ax2.set_ylabel(f'数值 ({sensor_info["unit"]})', fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 绘制Z轴数据
    ax3.plot(time_data, df['z'].values, color=sensor_info['colors'][2], 
             linewidth=1.5, alpha=0.8)
    ax3.set_title(f'{sensor_info["labels"][2]}', fontsize=12, fontweight='bold')
    ax3.set_xlabel('时间 (秒)', fontsize=10)
    ax3.set_ylabel(f'数值 ({sensor_info["unit"]})', fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    # 绘制三轴合成量
    magnitude = np.sqrt(np.power(df['x'], 2) + np.power(df['y'], 2) + np.power(df['z'], 2))
    ax4.plot(time_data, magnitude, color='purple', linewidth=2, alpha=0.8)
    ax4.set_title('三轴合成量', fontsize=12, fontweight='bold')
    ax4.set_xlabel('时间 (秒)', fontsize=10)
    ax4.set_ylabel(f'合成量 ({sensor_info["unit"]})', fontsize=10)
    ax4.grid(True, alpha=0.3)
    
    # 设置总体标题
    fig.suptitle(f'{sensor_info["title"]} - {os.path.basename(file_path)}', fontsize=16, fontweight='bold')
    
    # 计算各轴的统计信息
    x_data = np.array(df['x'].values)
    y_data = np.array(df['y'].values)
    z_data = np.array(df['z'].values)
    magnitude_data = np.sqrt(np.power(x_data, 2) + np.power(y_data, 2) + np.power(z_data, 2))
    
    x_mean, x_std = np.mean(x_data), np.std(x_data)
    y_mean, y_std = np.mean(y_data), np.std(y_data)
    z_mean, z_std = np.mean(z_data), np.std(z_data)
    mag_mean, mag_std = np.mean(magnitude_data), np.std(magnitude_data)
    
    # 在每个子图上添加统计信息
    # X轴统计信息
    x_stats_text = f"μ={x_mean:.3f}\nσ={x_std:.3f}"
    ax1.text(0.02, 0.98, x_stats_text, transform=ax1.transAxes, 
             fontsize=9, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))
    
    # Y轴统计信息
    y_stats_text = f"μ={y_mean:.3f}\nσ={y_std:.3f}"
    ax2.text(0.02, 0.98, y_stats_text, transform=ax2.transAxes, 
             fontsize=9, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
    
    # Z轴统计信息
    z_stats_text = f"μ={z_mean:.3f}\nσ={z_std:.3f}"
    ax3.text(0.02, 0.98, z_stats_text, transform=ax3.transAxes, 
             fontsize=9, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
    
    # 合成量统计信息和基本信息
    mag_stats_text = f"μ={mag_mean:.3f}\nσ={mag_std:.3f}\n\n"
    mag_stats_text += f"数据点数: {len(df)}\n"
    mag_stats_text += f"时长: {time_data[-1]:.2f}s\n"
    mag_stats_text += f"频率: {len(df)/(time_data[-1]-time_data[0]):.1f}Hz"
    ax4.text(0.02, 0.98, mag_stats_text, transform=ax4.transAxes, 
             fontsize=9, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='plum', alpha=0.7))
    
    plt.tight_layout()
    
    # 保存图片
    if output_folder:
        os.makedirs(output_folder, exist_ok=True)
        filename = os.path.splitext(os.path.basename(file_path))[0]
        output_path = os.path.join(output_folder, f'{filename}_visualization.png')
        fig.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"图片已保存: {output_path}")
    
    # 显示图片
    if show_plot:
        plt.show()
    else:
        plt.close(fig)

def visualize_multiple_files(folder_path, file_pattern="*.csv", output_folder=None):
    """
    批量可视化多个传感器数据文件
    
    Args:
        folder_path: 包含数据文件的文件夹路径
        file_pattern: 文件匹配模式，默认为所有CSV文件
        output_folder: 输出图片的文件夹
    """
    # 查找匹配的文件
    search_pattern = os.path.join(folder_path, file_pattern)
    files = glob.glob(search_pattern)
    
    if not files:
        print(f"在 {folder_path} 中未找到匹配 {file_pattern} 的文件")
        return
    
    print(f"找到 {len(files)} 个文件，开始批量可视化...")
    
    # 设置输出文件夹
    if output_folder is None:
        output_folder = os.path.join(folder_path, "sensor_visualizations")
    
    # 逐个处理文件
    for i, file_path in enumerate(sorted(files)):
        print(f"\n处理文件 {i+1}/{len(files)}: {os.path.basename(file_path)}")
        visualize_sensor_data(file_path, output_folder, show_plot=False)
    
    print(f"\n批量可视化完成！所有图片已保存到: {output_folder}")

def compare_sensor_data(file_paths, output_folder=None, show_plot=True):
    """
    比较多个传感器数据文件
    
    Args:
        file_paths: 数据文件路径列表
        output_folder: 输出图片的文件夹
        show_plot: 是否显示图片
    """
    if len(file_paths) < 2:
        print("至少需要两个文件进行比较")
        return
    
    plt.figure(figsize=(15, 10))
    
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
    
    for i, file_path in enumerate(file_paths):
        # 加载和预处理数据
        df = load_sensor_data(file_path)
        if df is None:
            continue
        
        df = preprocess_data(df)
        
        if not all(col in df.columns for col in ['x', 'y', 'z', 'time_seconds']):
            print(f"文件 {file_path} 缺少必要的列，跳过")
            continue
        
        # 获取文件名作为标签
        label = os.path.splitext(os.path.basename(file_path))[0]
        color = colors[i % len(colors)]
        
        # 绘制X轴数据（可以根据需要修改为其他轴）
        time_data = df['time_seconds'].values
        plt.plot(time_data, df['x'].values, color=color, label=f'{label} - X轴', 
                linewidth=1.5, alpha=0.7)
    
    plt.title('传感器数据比较 (X轴)', fontsize=14, fontweight='bold')
    plt.xlabel('时间 (秒)', fontsize=12)
    plt.ylabel('数值', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=10)
    plt.tight_layout()
    
    # 保存图片
    if output_folder:
        os.makedirs(output_folder, exist_ok=True)
        output_path = os.path.join(output_folder, 'sensor_data_comparison.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"比较图已保存: {output_path}")
    
    if show_plot:
        plt.show()
    else:
        plt.close()

def analyze_sensor_statistics(df, sensor_type):
    """
    分析传感器数据的详细统计信息
    
    Args:
        df: 包含传感器数据的DataFrame
        sensor_type: 传感器类型
        
    Returns:
        统计信息字典
    """
    if df is None or not all(col in df.columns for col in ['x', 'y', 'z']):
        return None
    
    stats = {}
    sensor_info = get_sensor_info(sensor_type)
    
    for axis in ['x', 'y', 'z']:
        data = df[axis].values
        stats[axis] = {
            'mean': np.mean(data),
            'std': np.std(data),
            'min': np.min(data),
            'max': np.max(data),
            'median': np.median(data),
            'rms': np.sqrt(np.mean(data**2)),  # 均方根值
            'peak_to_peak': np.max(data) - np.min(data)  # 峰峰值
        }
    
    # 计算三轴合成量的统计信息
    magnitude = np.sqrt(df['x']**2 + df['y']**2 + df['z']**2)
    stats['magnitude'] = {
        'mean': np.mean(magnitude),
        'std': np.std(magnitude),
        'min': np.min(magnitude),
        'max': np.max(magnitude),
        'median': np.median(magnitude)
    }
    
    return stats

def print_detailed_statistics(stats, sensor_info):
    """
    打印详细的统计信息
    
    Args:
        stats: 统计信息字典
        sensor_info: 传感器信息字典
    """
    if stats is None:
        return
    
    print("\n" + "="*60)
    print(f"详细统计分析 - {sensor_info['title']}")
    print("="*60)
    
    # 各轴详细统计
    axis_names = {'x': 'X轴', 'y': 'Y轴', 'z': 'Z轴'}
    for axis in ['x', 'y', 'z']:
        print(f"\n{axis_names[axis]} ({sensor_info['unit']}):")
        s = stats[axis]
        print(f"  均值 (Mean):      {s['mean']:>10.4f}")
        print(f"  标准差 (Std):     {s['std']:>10.4f}")
        print(f"  中位数 (Median):  {s['median']:>10.4f}")
        print(f"  最小值 (Min):     {s['min']:>10.4f}")
        print(f"  最大值 (Max):     {s['max']:>10.4f}")
        print(f"  均方根 (RMS):     {s['rms']:>10.4f}")
        print(f"  峰峰值 (P-P):     {s['peak_to_peak']:>10.4f}")
    
    # 三轴合成量统计
    print(f"\n三轴合成量 ({sensor_info['unit']}):")
    m = stats['magnitude']
    print(f"  均值 (Mean):      {m['mean']:>10.4f}")
    print(f"  标准差 (Std):     {m['std']:>10.4f}")
    print(f"  中位数 (Median):  {m['median']:>10.4f}")
    print(f"  最小值 (Min):     {m['min']:>10.4f}")
    print(f"  最大值 (Max):     {m['max']:>10.4f}")
    
    print("="*60)

def visualize_quaternion_data(file_path, output_folder=None, show_plot=True):
    """
    专门针对四元数数据的可视化函数
    
    Args:
        file_path: 数据文件路径
        output_folder: 输出图片的文件夹，如果为None则不保存
        show_plot: 是否显示图片
    """
    # 加载数据
    df = load_sensor_data(file_path)
    if df is None:
        return
    
    # 预处理数据
    df = preprocess_data(df)
    
    # 检查必要的列是否存在
    required_cols = ['x', 'y', 'z', 'time_seconds']
    if not all(col in df.columns for col in required_cols):
        print(f"数据文件缺少必要的列: {required_cols}")
        return
    
    # 检测传感器类型
    sensor_type = detect_sensor_type(file_path)
    sensor_info = get_sensor_info(sensor_type)
    
    # 进行详细统计分析
    stats = analyze_quaternion_statistics(df, sensor_type)
    if stats:
        print_quaternion_statistics(stats, sensor_info)
    
    # 创建图形 - 使用3x2的子图布局，专门为四元数设计
    fig = plt.figure(figsize=(16, 12))
    
    # 获取时间数据
    time_data = df['time_seconds'].values
    
    # 检查是否有W分量（四元数的标量部分）
    has_w = 'w' in df.columns
    
    # 设置子图布局
    if has_w:
        # 如果有W分量，使用3x2布局
        ax1 = plt.subplot(3, 2, 1)  # X分量
        ax2 = plt.subplot(3, 2, 2)  # Y分量
        ax3 = plt.subplot(3, 2, 3)  # Z分量
        ax4 = plt.subplot(3, 2, 4)  # W分量
        ax5 = plt.subplot(3, 2, 5)  # 四元数模长
        ax6 = plt.subplot(3, 2, 6)  # 欧拉角（如果可以计算）
    else:
        # 如果没有W分量（旋转向量），使用2x2布局
        ax1 = plt.subplot(2, 2, 1)  # X分量
        ax2 = plt.subplot(2, 2, 2)  # Y分量
        ax3 = plt.subplot(2, 2, 3)  # Z分量
        ax4 = plt.subplot(2, 2, 4)  # 旋转角度幅值
    
    # 绘制X分量
    ax1.plot(time_data, df['x'].values, color=sensor_info['colors'][0], 
             linewidth=1.5, alpha=0.8)
    ax1.set_title(f'{sensor_info["labels"][0]}', fontsize=12, fontweight='bold')
    ax1.set_xlabel('时间 (秒)', fontsize=10)
    ax1.set_ylabel('数值', fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 绘制Y分量
    ax2.plot(time_data, df['y'].values, color=sensor_info['colors'][1], 
             linewidth=1.5, alpha=0.8)
    ax2.set_title(f'{sensor_info["labels"][1]}', fontsize=12, fontweight='bold')
    ax2.set_xlabel('时间 (秒)', fontsize=10)
    ax2.set_ylabel('数值', fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 绘制Z分量
    ax3.plot(time_data, df['z'].values, color=sensor_info['colors'][2], 
             linewidth=1.5, alpha=0.8)
    ax3.set_title(f'{sensor_info["labels"][2]}', fontsize=12, fontweight='bold')
    ax3.set_xlabel('时间 (秒)', fontsize=10)
    ax3.set_ylabel('数值', fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    if has_w:
        # 绘制W分量（四元数的标量部分）
        ax4.plot(time_data, df['w'].values, color=sensor_info['colors'][3], 
                 linewidth=1.5, alpha=0.8)
        ax4.set_title('W分量（标量部分）', fontsize=12, fontweight='bold')
        ax4.set_xlabel('时间 (秒)', fontsize=10)
        ax4.set_ylabel('数值', fontsize=10)
        ax4.grid(True, alpha=0.3)
        
        # 计算并绘制四元数的模长
        quat_magnitude = np.sqrt(np.power(df['x'], 2) + np.power(df['y'], 2) + 
                                 np.power(df['z'], 2) + np.power(df['w'], 2))
        ax5.plot(time_data, quat_magnitude, color='orange', linewidth=2, alpha=0.8)
        ax5.set_title('四元数模长', fontsize=12, fontweight='bold')
        ax5.set_xlabel('时间 (秒)', fontsize=10)
        ax5.set_ylabel('模长', fontsize=10)
        ax5.grid(True, alpha=0.3)
        
        # 计算旋转角度（从四元数）
        rotation_angle = 2 * np.arccos(np.abs(np.clip(df['w'], -1, 1))) * 180 / np.pi
        ax6.plot(time_data, rotation_angle, color='brown', linewidth=2, alpha=0.8)
        ax6.set_title('旋转角度 (度)', fontsize=12, fontweight='bold')
        ax6.set_xlabel('时间 (秒)', fontsize=10)
        ax6.set_ylabel('角度 (度)', fontsize=10)
        ax6.grid(True, alpha=0.3)
        
    else:
        # 对于旋转向量，计算旋转角度幅值
        rotation_magnitude = np.sqrt(np.power(df['x'], 2) + np.power(df['y'], 2) + 
                                    np.power(df['z'], 2))
        ax4.plot(time_data, rotation_magnitude, color='purple', linewidth=2, alpha=0.8)
        ax4.set_title('旋转向量幅值', fontsize=12, fontweight='bold')
        ax4.set_xlabel('时间 (秒)', fontsize=10)
        ax4.set_ylabel('幅值 (rad)', fontsize=10)
        ax4.grid(True, alpha=0.3)
    
    # 设置总体标题
    fig.suptitle(f'{sensor_info["title"]} - {os.path.basename(file_path)}', 
                 fontsize=16, fontweight='bold')
    
    # 计算统计信息并添加到图表
    x_data = np.array(df['x'].values)
    y_data = np.array(df['y'].values)
    z_data = np.array(df['z'].values)
    
    x_mean, x_std = np.mean(x_data), np.std(x_data)
    y_mean, y_std = np.mean(y_data), np.std(y_data)
    z_mean, z_std = np.mean(z_data), np.std(z_data)
    
    # 添加统计信息到各个子图
    ax1.text(0.02, 0.98, f"μ={x_mean:.3f}\nσ={x_std:.3f}", 
             transform=ax1.transAxes, fontsize=9, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))
    
    ax2.text(0.02, 0.98, f"μ={y_mean:.3f}\nσ={y_std:.3f}", 
             transform=ax2.transAxes, fontsize=9, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
    
    ax3.text(0.02, 0.98, f"μ={z_mean:.3f}\nσ={z_std:.3f}", 
             transform=ax3.transAxes, fontsize=9, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
    
    if has_w:
        w_data = np.array(df['w'].values)
        w_mean, w_std = np.mean(w_data), np.std(w_data)
        ax4.text(0.02, 0.98, f"μ={w_mean:.3f}\nσ={w_std:.3f}", 
                 transform=ax4.transAxes, fontsize=9, verticalalignment='top', 
                 bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.7))
        
        # 添加模长统计
        mag_mean, mag_std = np.mean(quat_magnitude), np.std(quat_magnitude)
        ax5.text(0.02, 0.98, f"μ={mag_mean:.3f}\nσ={mag_std:.3f}", 
                 transform=ax5.transAxes, fontsize=9, verticalalignment='top', 
                 bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.7))
        
        # 添加旋转角度统计
        angle_mean, angle_std = np.mean(rotation_angle), np.std(rotation_angle)
        ax6.text(0.02, 0.98, f"μ={angle_mean:.1f}°\nσ={angle_std:.1f}°", 
                 transform=ax6.transAxes, fontsize=9, verticalalignment='top', 
                 bbox=dict(boxstyle='round', facecolor='lavender', alpha=0.7))
    else:
        # 旋转向量幅值统计
        mag_mean, mag_std = np.mean(rotation_magnitude), np.std(rotation_magnitude)
        ax4.text(0.02, 0.98, f"μ={mag_mean:.3f}\nσ={mag_std:.3f}", 
                 transform=ax4.transAxes, fontsize=9, verticalalignment='top', 
                 bbox=dict(boxstyle='round', facecolor='plum', alpha=0.7))
    
    plt.tight_layout()
    
    # 保存图片
    if output_folder:
        os.makedirs(output_folder, exist_ok=True)
        filename = os.path.splitext(os.path.basename(file_path))[0]
        output_path = os.path.join(output_folder, f'{filename}_quaternion_visualization.png')
        fig.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"四元数图片已保存: {output_path}")
    
    # 显示图片
    if show_plot:
        plt.show()
    else:
        plt.close(fig)

def analyze_quaternion_statistics(df, sensor_type):
    """
    分析四元数数据的详细统计信息
    
    Args:
        df: 包含四元数数据的DataFrame
        sensor_type: 传感器类型
        
    Returns:
        统计信息字典
    """
    if df is None or not all(col in df.columns for col in ['x', 'y', 'z']):
        return None
    
    stats = {}
    has_w = 'w' in df.columns
    
    # 分析各个分量
    for axis in ['x', 'y', 'z']:
        data = df[axis].values
        stats[axis] = {
            'mean': np.mean(data),
            'std': np.std(data),
            'min': np.min(data),
            'max': np.max(data),
            'median': np.median(data),
            'rms': np.sqrt(np.mean(data**2)),
            'peak_to_peak': np.max(data) - np.min(data)
        }
    
    if has_w:
        # 分析W分量
        w_data = df['w'].values
        stats['w'] = {
            'mean': np.mean(w_data),
            'std': np.std(w_data),
            'min': np.min(w_data),
            'max': np.max(w_data),
            'median': np.median(w_data),
            'rms': np.sqrt(np.mean(w_data**2)),
            'peak_to_peak': np.max(w_data) - np.min(w_data)
        }
        
        # 计算四元数模长
        magnitude = np.sqrt(df['x']**2 + df['y']**2 + df['z']**2 + df['w']**2)
        stats['magnitude'] = {
            'mean': np.mean(magnitude),
            'std': np.std(magnitude),
            'min': np.min(magnitude),
            'max': np.max(magnitude),
            'median': np.median(magnitude)
        }
        
        # 计算旋转角度
        rotation_angle = 2 * np.arccos(np.abs(np.clip(df['w'], -1, 1))) * 180 / np.pi
        stats['rotation_angle'] = {
            'mean': np.mean(rotation_angle),
            'std': np.std(rotation_angle),
            'min': np.min(rotation_angle),
            'max': np.max(rotation_angle),
            'median': np.median(rotation_angle)
        }
        
    else:
        # 对于旋转向量，计算幅值
        magnitude = np.sqrt(df['x']**2 + df['y']**2 + df['z']**2)
        stats['magnitude'] = {
            'mean': np.mean(magnitude),
            'std': np.std(magnitude),
            'min': np.min(magnitude),
            'max': np.max(magnitude),
            'median': np.median(magnitude)
        }
    
    return stats

def print_quaternion_statistics(stats, sensor_info):
    """
    打印四元数的详细统计信息
    
    Args:
        stats: 统计信息字典
        sensor_info: 传感器信息字典
    """
    if stats is None:
        return
    
    print("\n" + "="*60)
    print(f"四元数/旋转向量详细统计分析 - {sensor_info['title']}")
    print("="*60)
    
    # 各分量详细统计
    axis_names = {'x': 'X分量', 'y': 'Y分量', 'z': 'Z分量', 'w': 'W分量（标量）'}
    for axis in ['x', 'y', 'z']:
        print(f"\n{axis_names[axis]}:")
        s = stats[axis]
        print(f"  均值 (Mean):      {s['mean']:>10.4f}")
        print(f"  标准差 (Std):     {s['std']:>10.4f}")
        print(f"  中位数 (Median):  {s['median']:>10.4f}")
        print(f"  最小值 (Min):     {s['min']:>10.4f}")
        print(f"  最大值 (Max):     {s['max']:>10.4f}")
        print(f"  均方根 (RMS):     {s['rms']:>10.4f}")
        print(f"  峰峰值 (P-P):     {s['peak_to_peak']:>10.4f}")
    
    # W分量统计（如果存在）
    if 'w' in stats:
        print(f"\n{axis_names['w']}:")
        s = stats['w']
        print(f"  均值 (Mean):      {s['mean']:>10.4f}")
        print(f"  标准差 (Std):     {s['std']:>10.4f}")
        print(f"  中位数 (Median):  {s['median']:>10.4f}")
        print(f"  最小值 (Min):     {s['min']:>10.4f}")
        print(f"  最大值 (Max):     {s['max']:>10.4f}")
        print(f"  均方根 (RMS):     {s['rms']:>10.4f}")
        print(f"  峰峰值 (P-P):     {s['peak_to_peak']:>10.4f}")
    
    # 模长统计
    print(f"\n{'四元数模长' if 'w' in stats else '旋转向量幅值'}:")
    m = stats['magnitude']
    print(f"  均值 (Mean):      {m['mean']:>10.4f}")
    print(f"  标准差 (Std):     {m['std']:>10.4f}")
    print(f"  中位数 (Median):  {m['median']:>10.4f}")
    print(f"  最小值 (Min):     {m['min']:>10.4f}")
    print(f"  最大值 (Max):     {m['max']:>10.4f}")
    
    # 旋转角度统计（如果是四元数）
    if 'rotation_angle' in stats:
        print(f"\n旋转角度 (度):")
        r = stats['rotation_angle']
        print(f"  均值 (Mean):      {r['mean']:>10.2f}")
        print(f"  标准差 (Std):     {r['std']:>10.2f}")
        print(f"  中位数 (Median):  {r['median']:>10.2f}")
        print(f"  最小值 (Min):     {r['min']:>10.2f}")
        print(f"  最大值 (Max):     {r['max']:>10.2f}")
    
    print("="*60)

def main():
    """主函数，提供交互式界面"""
    print("=" * 60)
    print("传感器数据可视化工具")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 可视化单个文件（自动检测类型）")
        print("2. 专门可视化四元数/旋转向量文件")
        print("3. 批量可视化文件夹中的所有文件")
        print("4. 比较多个文件")
        print("5. 退出")
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == '1':
            # 单个文件可视化（自动检测类型）
            file_path = input("请输入文件路径: ").strip().strip('"')
            
            if not os.path.exists(file_path):
                print("文件不存在!")
                continue
            
            output_folder = input("请输入输出文件夹路径 (可选，直接回车跳过): ").strip().strip('"')
            output_folder = output_folder if output_folder else None
            
            visualize_sensor_data(file_path, output_folder)
        
        elif choice == '2':
            # 专门的四元数可视化
            file_path = input("请输入四元数/旋转向量文件路径: ").strip().strip('"')
            
            if not os.path.exists(file_path):
                print("文件不存在!")
                continue
            
            output_folder = input("请输入输出文件夹路径 (可选，直接回车跳过): ").strip().strip('"')
            output_folder = output_folder if output_folder else None
            
            visualize_quaternion_data(file_path, output_folder)
        
        elif choice == '3':
            # 批量可视化
            folder_path = input("请输入文件夹路径: ").strip().strip('"')
            
            if not os.path.exists(folder_path):
                print("文件夹不存在!")
                continue
            
            pattern = input("请输入文件匹配模式 [默认: *.csv]: ").strip()
            pattern = pattern if pattern else "*.csv"
            
            output_folder = input("请输入输出文件夹路径 (可选，直接回车使用默认): ").strip().strip('"')
            output_folder = output_folder if output_folder else None
            
            visualize_multiple_files(folder_path, pattern, output_folder)
        
        elif choice == '4':
            # 比较多个文件
            print("请输入要比较的文件路径，每行一个，输入空行结束:")
            file_paths = []
            while True:
                path = input().strip().strip('"')
                if not path:
                    break
                if os.path.exists(path):
                    file_paths.append(path)
                else:
                    print(f"文件不存在: {path}")
            
            if len(file_paths) < 2:
                print("至少需要两个文件进行比较!")
                continue
            
            output_folder = input("请输入输出文件夹路径 (可选，直接回车跳过): ").strip().strip('"')
            output_folder = output_folder if output_folder else None
            
            compare_sensor_data(file_paths, output_folder)
        
        elif choice == '5':
            print("退出程序")
            break
        
        else:
            print("无效选择，请重新输入!")

if __name__ == "__main__":
    # 可以直接调用main()进入交互模式，或者取消注释下面的代码进行快速测试
    
    # 快速测试示例（取消注释来使用）
    # 测试四元数/旋转向量数据
    test_file = r"e:\Document\user001\7.25\Session_20250725_225125 - 副本\output_world_coordinates11\segment13_world_acce_22-51-44.488_kalman.csv"
    print("正在测试四元数/旋转向量数据可视化...")
    visualize_sensor_data(test_file, output_folder="./output", show_plot=True)
    
    print("\n" + "="*60)
    print("四元数数据可视化功能说明:")
    print("="*60)
    print("1. 自动检测四元数/旋转向量数据（文件名包含'quat'、'rotvec'或'rotation'）")
    print("2. 针对四元数数据提供专门的可视化布局：")
    print("   - X、Y、Z分量的时间序列图")
    print("   - W分量（如果存在）的时间序列图")
    print("   - 四元数模长或旋转向量幅值")
    print("   - 旋转角度（对于四元数）")
    print("3. 提供详细的统计分析，包括各分量的均值、标准差等")
    print("4. 支持批量处理和文件比较功能")
    print("="*60)
    
    # main()
