# -*- coding: utf-8 -*-
"""
Created on Wed Apr 17 15:49:24 2024

@author: blueperiod
"""
import os
import matplotlib.pyplot as plt
import numpy as np
from scipy.signal import butter, lfilter


def read_data(file_path):
    with open(file_path, 'r') as file:
        lines = file.readlines()
        data = np.array([list(map(float, line.strip().split(','))) for line in lines])
    return data

            
def calcu_var(signal, window_size):
    signal_length = len(signal)
    num_windows = signal_length // window_size
    variance_list = []
    for i in range(num_windows):
        window = signal[i * window_size: (i + 1) * window_size]
        mean = np.mean(window)
        squared_diff = (window - mean) ** 2
        variance = np.sum(squared_diff) / (window_size - 1)
        variance_list.append(variance)
    return variance_list



def plot(plot_data):
        plt.plot(plot_data, label='fliter_Data', marker='x', linestyle='-.')
        # 添加图例
        plt.legend() 
        # 添加标题和轴标签
        plt.title(f'Data Normalization for Dimension {dim}')
        plt.xlabel('Index')
        plt.ylabel('Value')
        
def save_signal_samples(data, merged_segments, output_dir):
    for index, (start, end) in enumerate(merged_segments):
        # 提取当前段的数据样本
        sample = data[start:end+1]
        # 创建输出文件的名称和路径
        file_name = f"sample_{index}.txt"
        file_path = os.path.join(output_dir, file_name)
        
        # 将样本数据保存到文本文件中
        with open(file_path, 'w') as file:
            for value in sample:
                # 假设数据是浮点数，我们将其转换为字符串并保存
                file.write(f"{value:.6f}\n")  # 保存6位小数

def high_Path_Filter(data, cutoff_freq, sampling_rate):
    # 定义高通滤波器参数
    filter_order = 4  # 滤波器阶数
    # 设计高通滤波器
    nyquist_freq = 0.5 * sampling_rate
    cutoff = cutoff_freq / nyquist_freq
    b, a = butter(filter_order, cutoff, btype='highpass')
    # 对数据向量应用高通滤波器
    filtered_data = lfilter(b, a, data)
    return filtered_data
