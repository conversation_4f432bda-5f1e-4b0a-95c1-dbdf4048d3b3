# -*- coding:utf-8 -*-
"""
测试排序修复功能
验证segment编号是否按照1-16的正确顺序排列
"""

import os
import numpy as np
import pandas as pd
import re
from individual_motion_visualization import extract_segment_number, visualize_individual_motions

def create_test_files_with_mixed_order(output_folder="test_sorting"):
    """
    创建测试文件，故意使用混乱的文件名顺序来测试排序功能
    """
    os.makedirs(output_folder, exist_ok=True)
    
    # 创建16个文件，但是文件名顺序是混乱的（模拟实际情况）
    # 这样可以测试排序功能是否正常工作
    file_order = [10, 11, 12, 13, 14, 15, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9]  # 模拟错误的字符串排序
    
    print("创建测试文件（模拟混乱的排序）...")
    print(f"文件创建顺序: {file_order}")
    
    for i, segment_num in enumerate(file_order):
        # 创建简单的位移数据
        num_points = 20
        t = np.linspace(0, 1, num_points)
        
        # 每个segment有不同的运动模式
        angle = (segment_num - 1) * 22.5  # 每个segment相差22.5度
        radius = 1.5 + 0.1 * segment_num  # 半径略有不同
        
        x_traj = radius * np.cos(np.radians(angle)) * t
        y_traj = radius * np.sin(np.radians(angle)) * t
        z_traj = 0.5 * t  # 简单的Z轴运动
        
        # 创建时间戳
        timestamps = [f"00-00-{j*0.05:.3f}" for j in range(num_points)]
        
        # 创建DataFrame
        df = pd.DataFrame({
            'time': timestamps,
            'x': x_traj,
            'y': y_traj,
            'z': z_traj
        })
        
        # 保存文件
        filename = f"displacement_segment{segment_num:03d}.csv"
        filepath = os.path.join(output_folder, filename)
        df.to_csv(filepath, index=False)
        
        print(f"  创建文件: {filename} (segment {segment_num})")
    
    return output_folder

def test_extract_segment_number():
    """
    测试segment编号提取功能
    """
    print("\n=== 测试segment编号提取功能 ===")
    
    test_files = [
        "displacement_segment001.csv",
        "displacement_segment010.csv",
        "displacement_segment016.csv",
        "some_other_file.csv",
        "displacement_segment999.csv"
    ]
    
    for filename in test_files:
        segment_num = extract_segment_number(filename)
        print(f"文件: {filename:30} -> segment编号: {segment_num}")

def test_file_sorting():
    """
    测试文件排序功能
    """
    print("\n=== 测试文件排序功能 ===")
    
    # 创建测试数据
    test_folder = create_test_files_with_mixed_order()
    
    # 获取文件列表（模拟find_optimal_projection_plane_least_squares的行为）
    import glob
    csv_files = glob.glob(os.path.join(test_folder, "displacement_segment*.csv"))
    
    print(f"\n原始文件顺序（字符串排序）:")
    for i, file_path in enumerate(csv_files):
        segment_num = extract_segment_number(file_path)
        print(f"  {i+1:2d}. {os.path.basename(file_path):30} -> segment {segment_num}")
    
    # 模拟排序逻辑
    segment_numbers = []
    for file_path in csv_files:
        segment_num = extract_segment_number(file_path)
        segment_numbers.append(segment_num if segment_num is not None else -1)
    
    # 按segment编号排序
    valid_indices = []
    invalid_indices = []
    
    for i, segment_num in enumerate(segment_numbers):
        if segment_num >= 0:
            valid_indices.append((segment_num, i))
        else:
            invalid_indices.append((-1, i))
    
    # 按segment编号排序
    valid_indices.sort(key=lambda x: x[0])
    
    # 重新排列数据
    sorted_indices = [idx for _, idx in valid_indices] + [idx for _, idx in invalid_indices]
    
    # 重新排序
    sorted_files = [csv_files[i] for i in sorted_indices]
    sorted_segment_numbers = [segment_numbers[i] for i in sorted_indices]
    
    print(f"\n排序后的文件顺序（数字排序）:")
    for i, (file_path, segment_num) in enumerate(zip(sorted_files, sorted_segment_numbers)):
        print(f"  {i+1:2d}. {os.path.basename(file_path):30} -> segment {segment_num}")
    
    # 验证排序是否正确
    expected_order = list(range(1, 17))  # 1到16
    actual_order = sorted_segment_numbers[:16]  # 取前16个
    
    print(f"\n排序验证:")
    print(f"期望顺序: {expected_order}")
    print(f"实际顺序: {actual_order}")
    
    if actual_order == expected_order:
        print("✅ 排序正确！segment编号按1-16顺序排列")
    else:
        print("❌ 排序错误！")
        
    return test_folder

def test_visualization_with_correct_order():
    """
    测试可视化功能是否使用正确的排序
    """
    print("\n=== 测试可视化功能的排序 ===")
    
    # 创建测试数据
    test_folder = create_test_files_with_mixed_order("test_visualization_sorting")
    
    # 定义标准方向（1-16）
    standard_directions = [
        (2.0, 0), (0, -2.0), (-2.0, 0), (0, 2.0),
        (1.5, -1.5), (1.5, 1.5), (-1.5, -1.5), (-1.5, 1.5),
        (2.2, 0), (0, -2.2), (-2.2, 0), (0, 2.2),
        (1.8, -1.8), (1.8, 1.8), (-1.8, -1.8), (-1.8, 1.8)
    ]
    
    output_folder = os.path.join(test_folder, "visualization_results")
    
    print(f"执行可视化分析...")
    print(f"数据文件夹: {test_folder}")
    print(f"输出文件夹: {output_folder}")
    
    try:
        # 执行可视化
        visualize_individual_motions(
            csv_folder=test_folder,
            acc_data=None,
            output_folder=output_folder,
            standard_directions=standard_directions
        )
        
        print("✅ 可视化完成！请检查生成的图片，确认segment编号是否按1-16顺序排列")
        print(f"总览图路径: {os.path.join(output_folder, 'motions_overview.png')}")
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        import traceback
        traceback.print_exc()

def show_sorting_explanation():
    """
    显示排序问题的说明
    """
    print("=== 排序问题说明 ===")
    print("问题描述:")
    print("  原来的排序是按字符串排序: 1, 10, 11, 12, 13, 14, 15, 16, 2, 3, 4, 5, 6, 7, 8, 9")
    print("  期望的排序是按数字排序:   1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16")
    print()
    print("解决方案:")
    print("  1. 提取文件名中的segment编号")
    print("  2. 按数字大小排序而不是字符串排序")
    print("  3. 重新排列displacement_endpoints、source_files和segment_numbers数组")
    print("  4. 确保后续的可视化按正确顺序进行")
    print()

if __name__ == "__main__":
    print("测试segment编号排序修复功能")
    print("=" * 50)
    
    # 显示问题说明
    show_sorting_explanation()
    
    # 测试segment编号提取
    test_extract_segment_number()
    
    # 测试文件排序逻辑
    test_folder = test_file_sorting()
    
    # 测试完整的可视化功能
    test_visualization_with_correct_order()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n修复总结:")
    print("1. ✅ 添加了按segment编号的数字排序功能")
    print("2. ✅ 确保1-16按正确顺序排列")
    print("3. ✅ 修复了字符串排序导致的顺序错误")
    print("4. ✅ 保持了对无效segment编号的处理")
    print("\n现在segment编号将按照1, 2, 3, ..., 16的正确顺序显示！")
