# -*- coding:utf-8 -*-
"""
功能：绘制位移点图，可视化每个时刻的位移数据
将不同的segment用不同颜色区分显示
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib import cm
from mpl_toolkits.mplot3d import Axes3D
import glob
import re


plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 


def extract_segment_number(filename):
    """从文件名中提取segment编号"""
    match = re.search(r'segment(\d+)', os.path.basename(filename))
    if match:
        return int(match.group(1))
    return None

def read_displacement_data(file_path):
    """
    读取位移数据文件
    
    参数:
    file_path: 位移数据文件路径
    
    返回:
    位移数据DataFrame
    """
    try:
        df = pd.read_csv(file_path)
        # 保存文件路径到DataFrame的元数据中，方便后续引用
        df.name = file_path
        return df
    except Exception as e:
        print(f"读取文件 {file_path} 错误: {e}")
        return None

# 函数已合并到vector模式的实现中

def visualize_displacement_points(data_folder, output_file=None, connection_mode="scatter"):
    """
    可视化位移点图
    
    参数:
    data_folder: 包含位移数据的文件夹路径
    output_file: 输出图像的文件路径
    connection_mode: 连接模式
                    "scatter": 散点图(同一原点发散)
                    "line": 线图(每个segment内部点连接成线)
                    "relay": 接力模式(下一个segment起点是前一个segment终点)
                    "vector": 箭头图(从原点指向每个segment的终点位置)
    """
    # 查找所有包含displacement的CSV文件
    displacement_files = glob.glob(os.path.join(data_folder, "*displacement*.csv"))
    
    if not displacement_files:
        print(f"在文件夹 {data_folder} 中未找到任何displacement文件")
        return
    
    print(f"找到 {len(displacement_files)} 个位移数据文件")
    
    # 获取所有segment编号
    segment_numbers = []
    displacement_data = []
    
    for file_path in displacement_files:
        segment_num = extract_segment_number(file_path)
        if segment_num is not None :
            # and (segment_num == 1 or segment_num == 3):
            segment_numbers.append(segment_num)
            df = read_displacement_data(file_path)
            if df is not None:
                displacement_data.append(df)
                print(f"加载文件: {os.path.basename(file_path)}, 形状: {df.shape}")
    
    if not displacement_data:
        print("未能成功加载任何位移数据")
        return
      # 创建3D图形
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')    # 使用matplotlib的colormap从深蓝色到黄色    # 创建颜色映射 - 使用蓝到红的渐变
    num_segments = len(displacement_data)
    colors = []
    for i in range(num_segments):
        # 计算颜色插值比例
        ratio = i / max(1, num_segments - 1)
        if ratio < 0.25:
            # 从蓝色到青色
            r = 0
            g = 4 * ratio
            b = 1
        elif ratio < 0.5:
            # 从青色到绿色
            r = 0
            g = 1
            b = 1 - 4 * (ratio - 0.25)
        elif ratio < 0.75:
            # 从绿色到黄色
            r = 4 * (ratio - 0.5)
            g = 1
            b = 0
        else:
            # 从黄色到红色
            r = 1
            g = 1 - 4 * (ratio - 0.75)
            b = 0
        colors.append((r, g, b))
    
    # 保存每个segment的列信息
    segment_data_info = []
      # 处理接力模式的偏移
    offset_x, offset_y, offset_z = 0, 0, 0
    last_end_point = None
    
    # 绘制每个segment的位移点
    for i, df in enumerate(displacement_data):
        segment_num = segment_numbers[i]
        
        # 检查数据列
        x_col, y_col, z_col = None, None, None
        
        # 尝试找到x, y, z列
        for col in df.columns:
            if 'x' in col.lower():
                x_col = col
            elif 'y' in col.lower():
                y_col = col
            elif 'z' in col.lower():
                z_col = col
        
        if x_col is None or y_col is None or z_col is None:
            # 如果没有找到x, y, z列，尝试使用前三列
            if len(df.columns) >= 3:
                x_col, y_col, z_col = df.columns[0], df.columns[1], df.columns[2]
                print(f"警告: 在文件中未找到明确的x, y, z列, 使用前三列: {x_col}, {y_col}, {z_col}")
            else:
                print(f"错误: 在文件中未找到足够的列来表示3D位移")
                continue
        
        # 保存列信息
        segment_data_info.append((x_col, y_col, z_col))
        
        # 创建数据点副本，以便在接力模式中进行调整
        x_values = df[x_col].values.copy()
        y_values = df[y_col].values.copy()
        z_values = df[z_col].values.copy()
          # 根据连接模式选择绘图方式
        if (connection_mode == "relay" or connection_mode == "relay_scatter") and i > 0 and last_end_point is not None:
            # 接力模式 - 将当前segment的起点移动到上一个segment的终点
            if len(x_values) > 0:
                # 计算需要的偏移量
                start_x, start_y, start_z = x_values[0], y_values[0], z_values[0]
                dx = last_end_point[0] - start_x
                dy = last_end_point[1] - start_y
                dz = last_end_point[2] - start_z
                
                # 应用偏移到所有点
                x_values += dx
                y_values += dy
                z_values += dz
                
                # 更新本segment的终点位置，供下一个segment使用
                if len(x_values) > 1:
                    last_end_point = (x_values[-1], y_values[-1], z_values[-1])
        
        elif connection_mode not in ["relay", "relay_scatter", "line"]:
            # 散点图模式 - 同一原点发散
            connection_mode = "scatter"  # 确保模式正确
        
        # 检查是否是第一个segment并且是接力模式
        if (connection_mode == "relay" or connection_mode == "relay_scatter") and i == 0 and len(x_values) > 0:
            last_end_point = (x_values[-1], y_values[-1], z_values[-1])
          # 绘制散点或线图
        if connection_mode == "scatter":
            # 散点图模式 - 同一原点发散
            ax.scatter(
                df[x_col], df[y_col], df[z_col],
                color=colors[i], 
                s=10,  # 点的大小
                alpha=0.7,  # 透明度
                label=f"Segment {segment_num}"
            )
        elif connection_mode == "relay_scatter":
            # 散点接力模式 - 下一个segment起点是前一个segment终点
            # 使用调整后的数据绘制散点图
            ax.scatter(
                x_values, y_values, z_values,
                color=colors[i], 
                s=10,  # 点的大小
                alpha=0.7,  # 透明度
                label=f"Segment {segment_num}"
            )
              # 添加起点和终点标记
            if len(x_values) > 0:
                # 起点 - 用绿色三角形标记
                ax.scatter(
                    x_values[0], y_values[0], z_values[0],
                    color='green', marker='^', s=50, alpha=1
                )
                # 终点 - 用红色星形标记
                ax.scatter(
                    x_values[-1], y_values[-1], z_values[-1],
                    color='red', marker='*', s=50, alpha=1
                )
        elif connection_mode == "vector":
            # 箭头图模式 - 从原点指向每个segment的终点
            if len(df[x_col]) > 0:
                # 获取终点坐标
                end_x = df[x_col].iloc[-1]
                end_y = df[y_col].iloc[-1]
                end_z = df[z_col].iloc[-1]
                
                # 绘制从原点(0,0,0)到终点的箭头
                ax.quiver(
                    0, 0, 0,  # 起点(0,0,0)
                    end_x, end_y, end_z,  # 方向向量（终点坐标即为从原点出发的向量）
                    color=colors[i],
                    arrow_length_ratio=0.1,  # 箭头大小比例
                    linewidth=2,
                    label=f"Segment {segment_num}"
                )
                
                # 在终点添加标记
                ax.scatter(
                    end_x, end_y, end_z,
                    color='red', marker='*', s=80, alpha=1
                )
                
                # 在原点处添加小标记
                if i == 0:  # 只需要添加一次原点标记
                    ax.scatter(
                        0, 0, 0,
                        color='black', marker='o', s=100, alpha=1
                    )
        else:
            # 线图模式或线图接力模式
            if connection_mode == "line":
                # 普通线图模式，直接使用原始数据
                plot_x, plot_y, plot_z = df[x_col], df[y_col], df[z_col]
            else:
                # 线图接力模式，使用调整后的数据
                plot_x, plot_y, plot_z = x_values, y_values, z_values
            
            # 绘制线图
            ax.plot(
                plot_x, plot_y, plot_z,
                color=colors[i], 
                linewidth=2,
                alpha=0.7,
                label=f"Segment {segment_num}"
            )
            
            # 添加起点和终点标记
            if len(plot_x) > 0:
                # 起点 - 用绿色三角形标记
                ax.scatter(
                    plot_x[0], plot_y[0], plot_z[0],
                    color='green', marker='^', s=50, alpha=1
                )
                # 终点 - 用红色星形标记
                ax.scatter(
                    plot_x[-1], plot_y[-1], plot_z[-1],
                    color='red', marker='*', s=50, alpha=1
                )
    
    # 确定绘图范围
    all_x = np.concatenate([df[info[0]].values for df, info in zip(displacement_data, segment_data_info)])
    all_y = np.concatenate([df[info[1]].values for df, info in zip(displacement_data, segment_data_info)])
    all_z = np.concatenate([df[info[2]].values for df, info in zip(displacement_data, segment_data_info)])
    
    max_range = max(
        np.max(np.abs(all_x)),
        np.max(np.abs(all_y)),
        np.max(np.abs(all_z))
    ) * 1.1  # 添加10%的边缘
    
    # 设置坐标轴范围
    ax.set_xlim([-max_range, max_range])
    ax.set_ylim([-max_range, max_range])
    ax.set_zlim([-max_range, max_range])
    
    # 添加坐标轴标签和标题
    ax.set_xlabel('X位移 (cm)')
    ax.set_ylabel('Y位移 (cm)')
    ax.set_zlabel('Z位移 (cm)')    # 根据连接模式设置标题
    if connection_mode == "scatter":
        ax.set_title('位移数据点图 (同一原点发散)')
    elif connection_mode == "line":
        ax.set_title('位移数据线图 (首尾连接)')
    elif connection_mode == "relay_scatter":
        ax.set_title('位移数据接力散点图 (点接力连接)')
    elif connection_mode == "relay":
        ax.set_title('位移数据接力线图 (线接力连接)')
    elif connection_mode == "vector":
        ax.set_title('位移数据箭头图 (原点指向终点)')
      # 添加图例
    if len(segment_numbers) > 10:
        # 如果段数太多，只显示部分图例
        handles, labels = ax.get_legend_handles_labels()
        ax.legend(handles[:10], labels[:10], loc='upper right', title='Segments (top 10)')
    else:
        ax.legend(loc='upper right', title='Segments')
          # 添加颜色渐变说明
    ax.text2D(0.05, 0.95, "颜色渐变: 蓝→青→绿→黄→红 表示 Segment顺序从早到晚", 
              transform=ax.transAxes, fontsize=12, bbox=dict(facecolor='white', alpha=0.7))
    
    # 添加坐标轴网格
    ax.grid(True)
    
    # 设置视角
    ax.view_init(elev=30, azim=45)
    
    plt.tight_layout()
    
    # 保存图像
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"已保存位移图: {output_file}")
    
    plt.show()
    print("位移图绘制完成!")

if __name__ == "__main__":
    # 指定包含位移数据CSV文件的文件夹路径
    default_folder = r"E:\Document\user001\7.25\Session_20250725_225125 - 副本\output_world_coordinates\test"
    
    print("=" * 50)
    print("位移数据可视化工具")
    print("=" * 50)
    
    # 输入文件夹路径
    folder_path = input(f"请输入包含位移数据的文件夹路径 [默认: {default_folder}]: ")
    folder_path = folder_path.strip() if folder_path.strip() else default_folder
    
    # 确认文件夹存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹不存在: {folder_path}")
        exit(1)    # 选择绘图模式
    print("\n请选择绘图模式:")
    print("1. 散点图 (同一原点发散)")
    print("2. 线图 (首尾连接)")
    print("3. 散点接力图 (下一个segment起点是前一个segment终点)")
    print("4. 箭头图 (从原点指向每个segment的终点位置)")
    mode_choice = input("请输入选择 [默认: 1]: ").strip()
    
    if mode_choice == "2":
        connection_mode = "line"
        output_file = os.path.join(folder_path, "位移数据线图.png")
    elif mode_choice == "3":
        connection_mode = "relay_scatter"
        output_file = os.path.join(folder_path, "位移数据接力散点图.png")
    elif mode_choice == "4":
        connection_mode = "vector"
        output_file = os.path.join(folder_path, "位移数据箭头图.png")
    else:
        connection_mode = "scatter"
        output_file = os.path.join(folder_path, "位移数据点图.png")
    
    # 执行可视化
    visualize_displacement_points(folder_path, output_file, connection_mode)
