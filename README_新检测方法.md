# 新的积分检测方法说明

## 概述

本次更新对运动事件检测器进行了重大改进，不再依赖加速度零点检测，而是通过积分计算速度和位移，然后使用两种新方法检测加速部分。

## 主要改进

### 1. 检测逻辑变更
- **原方法**: 基于加速度零点检测加速-减速分界点
- **新方法**: 通过积分计算速度和位移，基于极值点检测

### 2. 两种检测方法

#### 方法1: 速度最大值方法 (`detection_method='velocity'`)
- **原理**: 速度最大值点之前为加速部分
- **优势**: 能够避免减速阶段的反向速度最大值
- **适用场景**: 短时间、高强度的运动

#### 方法2: 位移最大值方法 (`detection_method='displacement'`)
- **原理**: 位移最大值点之前为加速部分
- **优势**: 基于位移极值，更稳定
- **适用场景**: 长时间、渐进式的运动

### 3. 智能运动方向识别
- 自动识别正向/负向运动
- 避免误检测减速阶段的反向峰值
- 基于初始运动趋势确定主要方向

## 使用方法

### 基本用法

```python
from motion_event_detector import MotionEventDetector, process_file

# 方法1: 使用速度最大值检测
result_velocity = process_file(
    "your_data.csv", 
    visualize=True,
    only_acceleration=True,
    detection_method='velocity'
)

# 方法2: 使用位移最大值检测
result_displacement = process_file(
    "your_data.csv", 
    visualize=True,
    only_acceleration=True,
    detection_method='displacement'
)
```

### 直接API使用

```python
# 创建检测器
detector = MotionEventDetector(
    window_size=10,
    overlap=0.85,
    sample_rate=100
)

# 速度方法检测
start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    use_integration=True,
    detection_method='velocity'
)

# 位移方法检测
start_idx2, end_idx2, confidence2, pattern2 = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    use_integration=True,
    detection_method='displacement'
)
```

## 核心算法

### 速度最大值检测算法

1. **积分计算速度**: `velocity = ∫ acceleration dt`
2. **识别运动方向**: 基于初始速度趋势
3. **寻找峰值**: 
   - 正向运动: 寻找正向速度峰值
   - 负向运动: 寻找负向速度峰值
4. **验证显著性**: 确保峰值足够显著（≥30%最大速度）
5. **边界确定**: 第一个显著峰值作为加速阶段结束点

### 位移最大值检测算法

1. **二次积分计算位移**: `displacement = ∫∫ acceleration dt²`
2. **确定运动方向**: 基于最终位移方向
3. **寻找极值**:
   - 正向运动: 寻找位移最大值
   - 负向运动: 寻找位移最小值（绝对值最大）
4. **边界确定**: 位移极值点作为加速阶段结束点

## 关键特性

### 1. 避免反向速度峰值
- 通过初始运动方向识别避免误检测
- 只考虑与主要运动方向一致的峰值
- 智能过滤减速阶段的反向加速

### 2. 自适应阈值
- 基于信号强度动态调整阈值
- 考虑峰值显著性验证
- 提供置信度评估

### 3. 鲁棒性增强
- 多重验证机制
- 边界优化算法
- 异常情况处理

## 可视化功能

新的可视化功能包含5个子图：
1. **原始加速度数据**: 显示三轴原始数据
2. **过滤后数据**: 显示预处理后的数据
3. **主运动轴**: 突出显示主要运动轴
4. **速度积分**: 显示速度曲线和峰值点
5. **位移积分**: 显示位移曲线和极值点

## 参数说明

### 新增参数
- `detection_method`: 检测方法选择
  - `'velocity'`: 速度最大值方法
  - `'displacement'`: 位移最大值方法

### 现有参数保持不变
- `only_acceleration_phase`: 是否只检测加速阶段
- `use_integration`: 是否使用积分方法
- `visualize`: 是否生成可视化

## 使用建议

### 选择检测方法的建议

1. **速度方法适用于**:
   - 短时间爆发性运动
   - 明显的加速-减速模式
   - 需要精确捕捉加速峰值的场景

2. **位移方法适用于**:
   - 长时间渐进式运动
   - 位移变化明显的运动
   - 需要稳定检测结果的场景

3. **对比验证**:
   - 建议同时使用两种方法进行对比
   - 根据具体应用选择最适合的方法
   - 可以基于置信度选择最佳结果

### 参数调优建议

1. **采样率设置**: 确保采样率足够高以捕捉运动细节
2. **窗口大小**: 根据运动持续时间调整窗口大小
3. **重叠比例**: 高重叠比例提供更好的时间分辨率

## 测试和验证

运行测试脚本验证新方法：

```bash
python test_new_detection_methods.py
```

查看使用示例：

```bash
python example_usage.py
```

## 注意事项

1. **数据质量**: 确保输入数据质量良好，噪声较小
2. **采样率**: 建议采样率≥50Hz以获得准确结果
3. **运动类型**: 方法适用于单一方向的运动事件
4. **边界条件**: 极短或极长的运动可能需要参数调整

## 兼容性

- 保持与原有API的向后兼容
- 新参数为可选参数，默认使用速度方法
- 原有的可视化功能仍然可用
