# -*- coding: utf-8 -*-
"""
Created on Wed Apr 17 18:54:45 2024

@author: blueperiod
"""

import numpy as np

class Auto_divideSegment():

    PTn_start = [] #PTn与角速度曲线的开始交点下标
    PTn_end = [] #PTn与角速度曲线的结束交点下标

    saccadePeak = [] #处理前的扫视峰值
    saccadePeak_index = [] #处理前的扫视峰值下标

    saccade_start = [] #扫视开始下标
    saccade_end = [] #扫视结束下标

    saccade_start_new = [] #删除重复扫视后的开始下标
    saccade_end_new = [] #删除重复扫视后的结束下标

    merged_start = [] #合并临近扫视开始下标
    merged_end = [] #合并临近扫视结束下标

    highPeak_start = [] #删除低峰扫视开始下标
    highPeak_end = [] #删除低峰扫视结束下标

    longSegment_start = [] #删除短扫视开始下标
    longSegment_end = [] #删除短扫视结束下标

    '''定义初始化函数'''
    def __init__(self, var_data, PTh, PTl, minLen, mergeThresh, peakThresh):
        self.var_data = var_data
        self.PTh = PTh
        self.PTl = PTl
        self.minLen = minLen
        self.mergeThresh = mergeThresh
        self.peakThresh = peakThresh

    '''计算PTn与曲线的交点下标'''
    def get_PTn_region(self):

        get_PTn_start = 0
        for i in range(len(self.var_data)):  # 寻找PTn与曲线的交点index(这里加入合法数据限制条件)

            if (self.var_data[i] >= self.PTh and self.var_data[i - 1] <= self.PTh):
                self.PTn_start.append(i)
                get_PTn_start = 1
            if (self.var_data[i - 1] >= self.PTh and self.var_data[i] <= self.PTh and get_PTn_start == 1):  # 最后一个判定条件保证了两个列表相同下标对应
                self.PTn_end.append(i)
                get_PTn_start = 0


    '''计算处理前的扫视峰值'''
    def get_SaccadePeak(self):
        for i in range(min(len(self.PTn_start), len(self.PTn_end))):
            peak = max(self.var_data[self.PTn_start[i]:self.PTn_end[i]])  # 找出区间内的峰值
            for j in range(self.PTn_start[i], self.PTn_end[i]):
                if self.var_data[j] >= peak: #找到高峰对应的下标及其数值
                    self.saccadePeak_index.append(j)
                    self.saccadePeak.append(self.var_data[j])


    '''扫视开始及结束检测'''
    def get_saccade(self):
        Is_start_detected = 0  # 用于避免连续检测到开始值/结束值
        for i in range(len(self.saccadePeak_index)):
            if Is_start_detected == 0:
                for j in range(self.saccadePeak_index[i], self.saccadePeak_index[i] - 300, -1):
                    if (j - 1 == 0 or j == 0):  # 遇到数据左边界
                        self.saccade_start.append(0)
                        Is_start_detected = 1
                        break
                    '''两个判定扫视左边界的条件：1.角速度小于一定值(可以和查找峰值的阈值不同) 2.遇到U形拐点 '''
                    if (self.var_data[j] < (self.PTl) and self.var_data[j - 1] > self.var_data[j]):
                        self.saccade_start.append(j)
                        Is_start_detected = 1
                        break
            if Is_start_detected == 1:
                for j in range(self.saccadePeak_index[i], self.saccadePeak_index[i] + 300):
                    if (j + 1 == len(self.var_data) - 1 or j == len(self.var_data) - 1):
                        self.saccade_end.append(len(self.var_data) - 1)  # 遇到数据右边界
                        Is_start_detected = 0
                        break
                    '''同理 '''
                    if (self.var_data[j] < (self.PTl) and self.var_data[j] < self.var_data[j + 1]):
                        self.saccade_end.append(j)
                        Is_start_detected = 0
                        break


    '''删除长度<=minLen的扫视(可能是噪声)，合并相同的扫视'''
    def get_saccade_new(self):
        short_saccade = []
        same_start_saccade = []
        same_end_saccade = []

        for i in range(len(self.saccade_start)):
            if (self.saccade_end[i] - self.saccade_start[i] <= 3):
                short_saccade.append(i)

        '''重复扫视由多峰检测引起，扫视开始阈值与结束阈值相同'''
        '''如果存在重复扫视，则两个会完全重叠(这是由扫视开始和结束在U形拐点处的判定值相同导致的)'''
        # 记录相同的相邻扫视起点和扫视终点(只标记前面一个)
        for i in range(len(self.saccade_start) - 1):
            if (self.saccade_start[i] == self.saccade_start[i + 1] and self.saccade_end[i] == self.saccade_end[i + 1]):
                same_start_saccade.append(i)
                same_end_saccade.append(i)

        # 将所有标记加在扫视起点、扫视终点列表的对应位置(两个列表相同位置同时标记)
        for i in range(len(short_saccade)):
            self.saccade_start[short_saccade[i]] = -1  # 短扫视标记为-1
            self.saccade_end[short_saccade[i]] = -1
        for i in range(len(same_start_saccade)):
            self.saccade_start[same_start_saccade[i]] = -2  # 相同扫视标记为-2
            self.saccade_end[same_end_saccade[i]] = -2

        # 更新扫视起点、扫视终点列表
        for i in range(len(self.saccade_start)):
            if (self.saccade_start[i] >= 0):
                self.saccade_start_new.append(self.saccade_start[i])
            if (self.saccade_end[i] >= 0):
                self.saccade_end_new.append(self.saccade_end[i])


    def merge_segments(self):
        i = 0
        while i < len(self.saccade_start_new) - 1: #不是最后一个值
            if abs(self.saccade_start_new[i+1] - self.saccade_end_new[i]) <= self.mergeThresh: #有合并的可能
                self.merged_start.append(self.saccade_start_new[i])
                while i < len(self.saccade_start_new) - 1 and abs(self.saccade_start_new[i+1] - self.saccade_end_new[i]) <= self.mergeThresh:
                    i += 1
                if i < len(self.saccade_end_new):  # 添加这个检查
                    self.merged_end.append(self.saccade_end_new[i])
            else:
                self.merged_start.append(self.saccade_start_new[i]) #不需要合并
                if i < len(self.saccade_end_new):  # 添加这个检查
                    self.merged_end.append(self.saccade_end_new[i])
            i += 1
        if i == len(self.saccade_start_new) - 1: #最后一个值,不需要合并
            self.merged_start.append(self.saccade_start_new[i])
            if i < len(self.saccade_end_new):  # 添加这个检查
                self.merged_end.append(self.saccade_end_new[i])

    '''计算新扫视集峰值及下标'''
    def delete_lowPeakSegment(self):
        for i in range(len(self.merged_start)):
            max = np.max(self.var_data[self.merged_start[i]: self.merged_end[i]+1])
            if max > self.peakThresh:
                self.highPeak_start.append(self.merged_start[i])
                self.highPeak_end.append(self.merged_end[i])

    '''计算新扫视集峰值及下标'''
    def delete_shortSegment(self):
        for i in range(len(self.highPeak_start)):
            if (self.highPeak_end[i] - self.highPeak_start[i]) >= self.minLen:
                self.longSegment_start.append(self.highPeak_start[i])
                self.longSegment_end.append(self.highPeak_end[i])


    def value_clear(self):
        self.PTn_start = []  # PTn与角速度曲线的开始交点下标
        self.PTn_end = []  # PTn与角速度曲线的结束交点下标
        self.saccadePeak = []  # 处理前的扫视峰值
        self.saccadePeak_index = []  # 处理前的扫视峰值下标
        self.saccade_start = []  # 扫视开始下标
        self.saccade_end = []  # 扫视结束下标
        self.saccade_start_new = []  # 删除短扫视、重复扫视后的开始下标
        self.saccade_end_new = []  # 删除短扫视、重复扫视后的结束下标
        self.merged_start = []  # 合并临近扫视开始下标
        self.merged_end = []  # 合并临近扫视结束下标
        self.highPeak_start = []  # 删除低峰扫视开始下标
        self.highPeak_end = []  # 删除低峰扫视结束下标
        self.longSegment_start = [] #删除短扫视开始下标
        self.longSegment_end = [] #删除短扫视结束下标


    def run_Auto_divideSegment(self):
        self.value_clear()
        self.get_PTn_region()
        self.get_SaccadePeak()
        self.get_saccade()
        self.get_saccade_new()
        self.merge_segments()
        self.delete_lowPeakSegment()
        self.delete_shortSegment()
