# 时间阈值优化

## 问题描述

在之前的实现中，当检测到的速度最大点时间小于0.1秒时，系统会继续寻找后续的最大速度点。这种做法可能导致：

- **检测不一致**: 有时会找到后续更大的峰值，有时找不到
- **时间延长**: 可能检测到远离真实峰值的后续点
- **逻辑复杂**: 需要额外的备用策略处理

### 原有逻辑问题
```
如果速度最大点 < 0.1s:
    继续在0.1s之后寻找最大速度点
    如果找不到显著峰值:
        使用备用策略
```

## 解决方案

### 核心优化
**当检测到的速度最大点时间小于0.1秒时，直接将结束时间设置为0.1秒**

### 新的逻辑
```python
# 首先在整个搜索范围内找到最大速度点
max_idx_in_search = np.argmax(velocity_magnitude[:len(search_range)])

# 检查最大速度点是否在时间阈值之前
if max_idx_in_search < min_samples:
    # 最大速度点在时间阈值之前，直接使用时间阈值
    end_idx = start_idx + min_samples
    print(f"最大速度点在时间阈值前，直接使用时间阈值: 0.1s")
else:
    # 最大速度点在时间阈值之后，正常检测
    end_idx = start_idx + max_idx_in_search
```

## 技术实现

### 1. 三轴合速度矢量方法优化

```python
def _find_acceleration_end_by_velocity_magnitude(self, velocity_magnitude, start_idx, min_duration_sec=0.1):
    # 首先在整个搜索范围内找到最大速度点
    max_vel_idx_in_search = np.argmax(velocity_magnitude[:len(search_range)])
    max_vel_value = velocity_magnitude[max_vel_idx_in_search]
    
    # 检查最大速度点是否在时间阈值之前
    if max_vel_idx_in_search < min_samples:
        # 直接使用时间阈值
        end_idx = start_idx + min_samples
        print(f"最大速度点在时间阈值前，直接使用时间阈值: 0.1s")
    else:
        # 正常检测流程
        if max_vel_value >= 0.2 * max_velocity_magnitude:
            end_idx = start_idx + max_vel_idx_in_search
        else:
            end_idx = start_idx + min_samples
```

### 2. 三轴独立检测方法优化

```python
def _find_acceleration_end_by_individual_axes(self, velocity_x, velocity_y, velocity_z, start_idx, min_duration_sec=0.1):
    for axis_name, velocity_axis in zip(['X', 'Y', 'Z'], [velocity_x, velocity_y, velocity_z]):
        # 找到该轴的最大速度点
        max_idx_in_search = np.argmax(np.abs(search_range))
        
        # 检查是否在时间阈值之前
        if max_idx_in_search < min_samples:
            # 直接使用时间阈值
            axis_end_times.append((min_samples, abs(max_value), axis_name))
            print(f"{axis_name}轴最大速度点在时间阈值前，使用时间阈值: 0.1s")
        else:
            # 正常检测
            axis_end_times.append((max_idx_in_search, abs(max_value), axis_name))
```

### 3. 主轴+副轴合成方法优化

```python
def _find_acceleration_end_by_main_secondary_axes(self, velocity_x, velocity_y, velocity_z, main_axis, start_idx, min_duration_sec=0.1):
    # 主轴检测
    max_idx_in_search = np.argmax(np.abs(main_search_range))
    if max_idx_in_search < min_samples:
        main_end_idx = min_samples  # 直接使用时间阈值
    else:
        main_end_idx = max_idx_in_search
    
    # 副轴合成检测
    max_idx_in_search = np.argmax(secondary_search_range)
    if max_idx_in_search < min_samples:
        secondary_end_idx = min_samples  # 直接使用时间阈值
    else:
        secondary_end_idx = max_idx_in_search
```

## 主要改进

### 1. 简化逻辑
- **直接决策**: 不再需要复杂的后续搜索逻辑
- **一致性**: 所有早期峰值都统一处理为0.1秒
- **可预测**: 结果更加稳定和可预测

### 2. 提高稳定性
- **最小保证**: 确保所有检测结果至少为0.1秒
- **避免异常**: 不会出现过短的检测结果
- **统一标准**: 所有方法使用相同的处理逻辑

### 3. 性能优化
- **减少计算**: 不需要额外的备用策略搜索
- **快速决策**: 直接基于最大值位置做决策
- **内存友好**: 减少了额外的数组操作

## 使用方法

### 1. 自动应用

优化已经集成到所有velocity检测方法中，无需额外配置：

```python
from motion_event_detector import MotionEventDetector

detector = MotionEventDetector(sample_rate=100)

# 所有方法都自动应用优化
result = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='velocity_individual'  # 或其他方法
)
```

### 2. 测试验证

```bash
python test_threshold_optimization.py
```

## 效果对比

### 优化前的问题场景
```
速度最大点在0.05s:
  原逻辑: 在0.1s后寻找新的峰值 → 可能找到0.3s的峰值
  问题: 检测结果偏离真实峰值时间
```

### 优化后的处理
```
速度最大点在0.05s:
  新逻辑: 直接设置结束时间为0.1s
  优势: 保持与真实峰值的接近性，同时满足最小时间要求
```

## 调试信息

### 1. 早期峰值检测

```
最大速度点在时间阈值前(索引5, 0.050s)，直接使用时间阈值: 索引10, 持续时间0.100s
```

### 2. 正常峰值检测

```
找到第一次减小前的最大速度点: 索引15, 持续时间0.150s, 合速度1.234
```

### 3. 三轴独立检测

```
X轴最大速度点在时间阈值前，使用时间阈值: 索引10, 时间0.100s, 速度1.234
Y轴最大速度点: 索引12, 时间0.120s, 速度0.987
Z轴最大速度点: 索引8, 时间0.080s, 速度1.456

选择最早的轴: X轴 (使用时间阈值)
最早时间: 索引10, 持续时间0.100s
```

## 适用场景

### 1. 推荐使用
- **快速运动**: 加速时间很短的运动
- **冲击性运动**: 瞬间达到峰值的运动
- **高频采样**: 采样率较高，容易出现早期峰值

### 2. 效果显著的情况
- **峰值时间 < 0.1s**: 直接受益于优化
- **多轴早期峰值**: 各轴都有早期峰值的情况
- **噪声环境**: 避免噪声导致的异常短时间检测

## 参数配置

### 1. 时间阈值设置

```python
min_duration_sec = 0.1  # 最小时间阈值
min_samples = int(min_duration_sec * sample_rate)  # 对应的样本数
```

### 2. 显著性阈值

```python
significance_threshold = 0.1  # 10%的最大速度
magnitude_threshold = 0.2     # 20%的最大速度（合速度方法）
```

## 故障排除

### 1. 检测时间过短

**问题**: 检测结果仍然小于0.1秒
**解决**: 
- 检查采样率设置
- 验证min_samples计算
- 确认优化逻辑是否正确执行

### 2. 检测时间过长

**问题**: 所有结果都是0.1秒
**解决**:
- 检查数据是否都是早期峰值
- 调整显著性阈值
- 验证峰值检测逻辑

### 3. 优化未生效

**问题**: 早期峰值没有被设置为0.1秒
**解决**:
- 检查调试输出信息
- 验证峰值检测是否正确
- 确认条件判断逻辑

## 更新日志

- **v1.7**: 添加时间阈值优化，早期峰值直接设置为0.1秒
- **v1.6**: 三轴独立检测和主轴+副轴合成检测方法
- **v1.5**: 第一次减小前最大速度检测方法

现在的检测方法能够智能处理早期峰值情况，确保检测结果的稳定性和一致性！
