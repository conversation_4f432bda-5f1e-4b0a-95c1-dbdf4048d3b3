#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试individual_motion_visualization.py的标准方向功能
验证每个运动事件是否正确对应其专属的标准方向
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from individual_motion_visualization import visualize_individual_motions

def create_test_data(output_dir):
    """
    创建测试用的CSV文件，模拟不同的运动事件
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 模拟8个不同方向的运动事件
    test_movements = [
        (1.5, 0.2),     # 主要向东
        (0.3, 1.8),     # 主要向北  
        (-1.2, 0.4),    # 主要向西
        (0.1, -1.6),    # 主要向南
        (1.0, 1.1),     # 东北方向
        (1.3, -0.9),    # 东南方向
        (-0.8, 1.4),    # 西北方向
        (-1.1, -1.2),   # 西南方向
    ]
    
    for i, (dx, dy) in enumerate(test_movements):
        # 创建CSV文件，模拟从(0,0,0)到(dx, dy, 0.1)的位移
        filename = f"segment{i+1}_acce_test.csv"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w') as f:
            f.write("X(m),Y(m),Z(m)\n")
            f.write(f"0.000,0.000,0.000\n")
            f.write(f"{dx/100:.3f},{dy/100:.3f},0.001\n")  # 转换为米
    
    print(f"已创建 {len(test_movements)} 个测试文件在目录: {output_dir}")
    return test_movements

def test_standard_directions():
    """
    测试标准方向功能
    """
    print("=" * 60)
    print("测试individual_motion_visualization的标准方向功能")
    print("=" * 60)
    
    # 创建测试数据目录
    test_dir = r"d:\research\code\整合全流程代码\test_data"
    expected_movements = create_test_data(test_dir)
    
    # 定义测试用的标准方向（每个运动事件一个专属方向）
    test_standard_directions = [
        (2.0, 0),      # 运动1的标准方向：纯东向
        (0, 2.0),      # 运动2的标准方向：纯北向
        (-2.0, 0),     # 运动3的标准方向：纯西向
        (0, -2.0),     # 运动4的标准方向：纯南向
        (1.4, 1.4),    # 运动5的标准方向：东北向
        (1.4, -1.4),   # 运动6的标准方向：东南向
        (-1.4, 1.4),   # 运动7的标准方向：西北向
        (-1.4, -1.4),  # 运动8的标准方向：西南向
    ]
    
    print(f"\n测试配置:")
    print(f"- 运动事件数量: {len(expected_movements)}")
    print(f"- 标准方向数量: {len(test_standard_directions)}")
    print(f"- 每个运动事件将与其对应序号的标准方向配对\n")
    
    print("运动事件与标准方向的配对:")
    for i, ((actual_x, actual_y), (std_x, std_y)) in enumerate(zip(expected_movements, test_standard_directions)):
        actual_angle = np.degrees(np.arctan2(actual_y, actual_x))
        std_angle = np.degrees(np.arctan2(std_y, std_x))
        if actual_angle < 0:
            actual_angle += 360
        if std_angle < 0:
            std_angle += 360
            
        angle_diff = abs(actual_angle - std_angle)
        if angle_diff > 180:
            angle_diff = 360 - angle_diff
            
        print(f"  运动{i+1}: 实际({actual_x:+.1f}, {actual_y:+.1f})cm, 角度{actual_angle:.0f}° "
              f"vs 标准({std_x:+.1f}, {std_y:+.1f})cm, 角度{std_angle:.0f}° "
              f"[偏差: {angle_diff:.0f}°]")
    
    # 输出目录
    output_dir = os.path.join(test_dir, "visualization_test")
    
    # 运行可视化
    print(f"\n开始运行可视化测试...")
    visualize_individual_motions(
        test_dir,
        acc_data=None,
        output_folder=output_dir,
        standard_directions=test_standard_directions
    )
    
    print(f"\n测试完成！请检查输出目录: {output_dir}")
    print("验证要点:")
    print("1. 每个子图应该只显示一个灰色虚线箭头（该事件的专属标准方向）")
    print("2. 第1个子图的标准方向应该指向正东（右侧）")
    print("3. 第2个子图的标准方向应该指向正北（上方）")
    print("4. 以此类推...")
    print("5. 角度偏差应该显示在每个子图的左上角")

if __name__ == "__main__":
    test_standard_directions()
