import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

def parse_timestamp(time_str):
    """解析时间戳为相对秒数"""
    try:
        dt = datetime.strptime(time_str, '%H-%M-%S.%f')
        return dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond/1000000
    except ValueError as e:
        print(f"时间解析错误: {time_str}, {e}")
        return 0

def high_pass_filter(data, alpha):
    """高通滤波器函数"""
    filtered_data = np.zeros_like(data)
    filtered_data[0] = data[0]
    for i in range(1, len(data)):
        filtered_data[i] = alpha * (filtered_data[i - 1] + data[i] - data[i - 1])
    return filtered_data

def process_acceleration_data(input_file, output_file, alpha=0.9):
    """处理加速度数据"""
    # 读取数据
    data = pd.read_csv(input_file)
    
    # 计算相对时间
    data['time_diff'] = data['time'].apply(parse_timestamp)
    base_time = data['time_diff'].min()
    data['time_diff'] = data['time_diff'] - base_time
    
    # 提取加速度数据并应用滤波
    axes = ['x', 'y', 'z']
    filtered_data = {'time': data['time'], 'sensor_timestamp': data['sensor_timestamp']}
    
    for axis in axes:
        acc_data = data[axis].to_numpy()
        filtered_data[f'{axis}_filtered'] = high_pass_filter(acc_data, alpha)
    
    # 保存处理后的数据
    pd.DataFrame(filtered_data).to_csv(output_file, index=False)
    
    # 绘制对比图
    plt.figure(figsize=(12, 8))
    for i, axis in enumerate(axes, 1):
        plt.subplot(3, 1, i)
        plt.plot(data['time_diff'], data[axis], 
                label=f'Original {axis}', linestyle='--', alpha=0.5)
        plt.plot(data['time_diff'], filtered_data[f'{axis}_filtered'], 
                label=f'Filtered {axis}')
        plt.legend()
        plt.title(f'{axis.upper()}-axis Acceleration')
    
    plt.tight_layout()
    plt.savefig(output_file.replace('.csv', '_plot.png'))
    plt.close()

if __name__ == "__main__":
    input_file = r"E:\Study\科研\data\实验3\acce_data_22-14-02.181_500hz_filtered.csv"
    output_file = input_file.replace('.csv', '_filtered.csv')
    process_acceleration_data(input_file, output_file)