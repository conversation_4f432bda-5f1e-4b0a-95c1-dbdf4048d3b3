# -*- coding:utf-8 -*-
"""
作者：${何志想}
日期：2025年05月10日
功能：手动输入空档期时间偏移，处理加速度和陀螺仪数据
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from interpolation import sort_and_clean_data

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def time_to_seconds(time_str):
    """将HH-MM-SS.mmm格式转换为秒数"""
    dt = datetime.strptime(time_str, '%H-%M-%S.%f')
    return dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1e6

def calc_absolute_time(base_time_str, offset_seconds):
    """
    计算基准时间加上偏移后的时间
    Args:
        base_time_str: 基准时间 (HH-MM-SS.fff)
        offset_seconds: 偏移秒数
    Returns:
        绝对时间字符串 (HH-MM-SS.fff)
    """
    # 解析基准时间
    base_time = datetime.strptime(base_time_str, '%H-%M-%S.%f')
    # 添加偏移
    absolute_time = base_time + timedelta(seconds=offset_seconds)
    return absolute_time.strftime('%H-%M-%S.%f')[:-3]

def extract_and_save_gap_data(data_file, gaps, output_dir=None):
    """提取并保存空档期数据"""
    # 使用输入文件所在目录作为输出目录
    output_dir = os.path.dirname(data_file) if output_dir is None else output_dir
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取数据
    df = pd.read_csv(data_file)
    
    # 获取基准时间（第一条记录的时间）
    base_timestamp = df['time'].iloc[0]
    print(f"基准时间: {base_timestamp}")
    
    # 处理每个空档期
    for i, (gap_start, gap_end) in enumerate(gaps, 1):
        # 计算实际的世界时间
        abs_start = calc_absolute_time(base_timestamp, gap_start)
        abs_end = calc_absolute_time(base_timestamp, gap_end)
        
        print(f"\n空档期 {i}:")
        print(f"基准时间: {base_timestamp}")
        print(f"相对时间区间: {gap_start:.3f}s - {gap_end:.3f}s")
        print(f"绝对时间区间: {abs_start} - {abs_end}")
        
        # 提取数据
        mask = (df['time'] >= abs_start) & (df['time'] <= abs_end)
        gap_data = df[mask]
        
        if not gap_data.empty:
            # 确定数据类型（acce或gyro）
            data_type = "acce" if "acce" in data_file.lower() else "gyro" if "gyro" in data_file.lower() else "data"
            
            # 使用数据类型和序号构建输出文件名
            output_file = os.path.join(output_dir, 
                f'segment{i}_{data_type}_{abs_start.replace(":", "-")}.csv')
            gap_data.to_csv(output_file, index=False)
            
            print(f"持续时间: {gap_end - gap_start:.3f} 秒")
            print(f"数据点数: {len(gap_data)}")
            print(f"已保存至: {output_file}")
            print(f"数据类型: {data_type}")
        else:
            print(f"警告: 在此时间范围内未找到任何数据点!")

def visualize_data_with_gaps(data_file, gaps):
    """可视化数据和标记的空档期"""
    df = pd.read_csv(data_file)
    base_timestamp = df['time'].iloc[0]
    
    # 将时间转换为相对秒数
    df['time_seconds'] = df['time'].apply(time_to_seconds) - time_to_seconds(base_timestamp)
    
    # 创建图表
    plt.figure(figsize=(12, 6))
    
    # 绘制数据
    data_type = "Accelerometer" if "acce" in data_file.lower() else "Gyroscope" if "gyro" in data_file.lower() else "grav" if "grav" in data_file.lower() else "mag" if "mag" in data_file.lower() else "data"
    
    if "acce" in data_file.lower() or "gyro" in data_file.lower():
        plt.plot(df['time_seconds'], df['x'], 'r-', label='X轴')
        plt.plot(df['time_seconds'], df['y'], 'g-', label='Y轴')
        plt.plot(df['time_seconds'], df['z'], 'b-', label='Z轴')
    else:
        # 如果不是标准的加速度或陀螺仪数据，尝试绘制第一列数值数据
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 1:  # 确保有时间列之外的数值列
            plt.plot(df['time_seconds'], df[numeric_cols[1]], label=numeric_cols[1])
    
    # 标记空档期
    for i, (gap_start, gap_end) in enumerate(gaps, 1):
        plt.axvspan(gap_start, gap_end, color='yellow', alpha=0.3, label=f'运动片段 {i}' if i==1 else "")
        plt.axvline(x=gap_start, color='orange', linestyle='--', linewidth=1)
        plt.axvline(x=gap_end, color='orange', linestyle='--', linewidth=1)
        # 添加空档期标签
        plt.text((gap_start + gap_end)/2, plt.ylim()[1]*0.9, f'运动事件{i}', 
                 horizontalalignment='center', backgroundcolor='yellow', alpha=0.6)
    
    plt.title(f'{data_type}数据与标记的空档期')
    plt.xlabel('相对时间 (秒)')
    plt.ylabel('幅度')
    plt.legend()
    plt.grid(True)
    
    # 保存图表
    output_dir = os.path.dirname(data_file)
    base_filename = os.path.basename(data_file).split('.')[0]
    output_file = os.path.join(output_dir, f'{base_filename}_gaps_visualization.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"\n已保存可视化图表: {output_file}")
    
    plt.show()

def input_manual_gaps():
    """交互式输入空档期"""
    print("\n===== 手动输入空档期 =====")
    print("请输入空档期的相对时间（相对于文件开始时间的秒数）")
    print("格式: 开始时间,结束时间")
    print("例如: 5.2,8.7")
    print("输入空行完成输入\n")
    
    gaps = []
    i = 1
    while True:
        user_input = input(f"空档期 {i} (留空结束): ")
        if not user_input.strip():
            break
        
        try:
            start, end = map(float, user_input.split(','))
            if start >= end:
                print("错误: 结束时间必须大于开始时间!")
                continue
            gaps.append((start, end))
            print(f"已添加空档期: {start:.3f}s - {end:.3f}s (持续 {end-start:.3f}s)")
            i += 1
        except ValueError:
            print("格式错误! 请使用'开始时间,结束时间'格式，例如: 5.2,8.7")
    
    return gaps

if __name__ == "__main__":
    print("="*50)
    print("手动空档期输入与数据处理工具")
    print("="*50)
    
    # 输入文件路径
    default_path = r"E:\Document\user001\6.18\straight\Session_20250618_212411 - 副本\acce_data_21-24-11.290.csv"
    file_path = input(f"请输入加速度数据文件路径 [默认: {default_path}]: ")
    file_path = file_path.strip() if file_path.strip() else default_path
    
    # 确认文件存在
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在: {file_path}")
        exit(1)
    
    # 手动输入空档期
    gaps = input_manual_gaps()
    
    if not gaps:
        print("未输入任何空档期，程序退出。")
        exit(0)
    
    print("\n开始处理数据...")
    
    # 数据排序和清理
    print("\n执行数据清理和排序...")
    original_df = pd.read_csv(file_path)
    cleaned_df = sort_and_clean_data(original_df)
    
    # 保存处理后的数据
    sorted_file = file_path.replace('.csv', '_sorted.csv')
    cleaned_df.to_csv(sorted_file, index=False)
    print(f"\n已保存排序后的数据: {sorted_file}")
    
    # 提取并保存空档期数据
    print("\n开始提取空档期数据...")
    extract_and_save_gap_data(sorted_file, gaps)
    visualize_data_with_gaps(sorted_file, gaps)
    
    # 检查是否存在对应的陀螺仪数据
    gyro_file = file_path.replace('acce_', 'gyro_')
    
    if os.path.exists(gyro_file):
        print(f"\n找到对应的陀螺仪数据: {gyro_file}")
        # 处理陀螺仪数据
        original_gyro_df = pd.read_csv(gyro_file)
        cleaned_gyro_df = sort_and_clean_data(original_gyro_df)
        sorted_gyro_file = gyro_file.replace('.csv', '_sorted.csv')
        cleaned_gyro_df.to_csv(sorted_gyro_file, index=False)
        print(f"已保存排序后的陀螺仪数据: {sorted_gyro_file}")
        extract_and_save_gap_data(sorted_gyro_file, gaps)
        visualize_data_with_gaps(sorted_gyro_file, gaps)
    
    # 检查是否存在对应的陀螺仪数据
    grav_file = file_path.replace('acce_', 'grav_')
    
    if os.path.exists(grav_file):
        print(f"\n找到对应的陀螺仪数据: {grav_file}")
        # 处理陀螺仪数据
        original_grav_df = pd.read_csv(grav_file)
        cleaned_grav_df = sort_and_clean_data(original_grav_df)
        sorted_grav_file = grav_file.replace('.csv', '_sorted.csv')
        cleaned_grav_df.to_csv(sorted_grav_file, index=False)
        print(f"已保存排序后的陀螺仪数据: {sorted_grav_file}")
        extract_and_save_gap_data(sorted_grav_file, gaps)

    # 检查是否存在对应的陀螺仪数据
    mag_file = file_path.replace('acce_', 'mag_')
    
    if os.path.exists(mag_file):
        print(f"\n找到对应的陀螺仪数据: {mag_file}")
        # 处理陀螺仪数据
        original_mag_df = pd.read_csv(mag_file)
        cleaned_mag_df = sort_and_clean_data(original_mag_df)
        sorted_mag_file = mag_file.replace('.csv', '_sorted.csv')
        cleaned_mag_df.to_csv(sorted_mag_file, index=False)
        print(f"已保存排序后的陀螺仪数据: {sorted_mag_file}")
        extract_and_save_gap_data(sorted_mag_file, gaps)
    
    # 可视化结果
    

    
    print("\n处理完成!")
