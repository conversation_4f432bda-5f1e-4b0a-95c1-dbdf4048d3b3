# 零速度点检测优化

## 问题描述

在原来的最大速度检测方法中，使用滑动窗口检测速度减小5%的时间点来确定搜索范围。这种方法存在以下问题：

- **检测不准确**: 5%的减小阈值可能过于敏感或不够敏感
- **逻辑复杂**: 需要滑动窗口和稳定性验证的复杂逻辑
- **不够直观**: 减小5%不能很好地反映运动的自然结束点

### 原有方法问题
```python
# 原方法：滑动窗口检测减小5%
for i in range(min_samples, len(velocity_magnitude) - window_size):
    current_velocity = velocity_magnitude[i]
    future_velocity = np.mean(velocity_magnitude[i+1:i+1+window_size])
    
    if future_velocity < current_velocity * 0.95:  # 减小超过5%
        # 复杂的稳定性验证...
```

## 解决方案

### 核心优化
**先检测除了初始时间之外又一次达到0速度的时间点，然后在0时刻到这个时间点之间找速度（绝对值）最大的时间**

### 新的检测逻辑
```python
# 1. 找到零速度点
zero_point = find_first_zero_velocity_point(velocity_magnitude, min_samples)

# 2. 在0时刻到零速度点之间找最大速度
if zero_point is not None:
    search_range = velocity_magnitude[:zero_point + 1]
    max_velocity_idx = np.argmax(search_range)
else:
    # 没有找到零速度点，使用全部数据
    max_velocity_idx = np.argmax(velocity_magnitude)
```

## 技术实现

### 1. 零速度点检测

```python
def _find_first_zero_velocity_point(self, velocity_magnitude, min_samples):
    # 计算零速度阈值（5%的最大速度）
    max_velocity = np.max(velocity_magnitude)
    zero_threshold = 0.05 * max_velocity
    
    # 从最小时间阈值之后开始寻找零点
    for i in range(min_samples, len(velocity_magnitude)):
        current_velocity = velocity_magnitude[i]
        
        # 检查是否达到零速度阈值
        if current_velocity <= zero_threshold:
            # 验证这是一个稳定的零点
            if self._verify_zero_velocity_point(velocity_magnitude, i, zero_threshold):
                return i
    
    return None
```

### 2. 零速度点稳定性验证

```python
def _verify_zero_velocity_point(self, velocity_magnitude, zero_point, zero_threshold):
    # 检查零点之后的一段时间内速度是否持续保持较低水平
    verify_window = min(10, len(velocity_magnitude) - zero_point)
    post_velocities = velocity_magnitude[zero_point:zero_point+verify_window]
    
    # 统计后续速度低于阈值的比例
    low_velocity_count = np.sum(post_velocities <= zero_threshold * 2)
    consistency_ratio = low_velocity_count / len(post_velocities)
    
    # 60%以上的时间保持较低速度才认为是稳定的零点
    return consistency_ratio > 0.6
```

### 3. 单轴零速度点检测

```python
def _find_first_velocity_decrease_single_axis(self, velocity_axis, min_samples, axis_name):
    # 计算零速度阈值（5%的最大速度）
    max_abs_velocity = np.max(np.abs(velocity_axis))
    zero_threshold = 0.05 * max_abs_velocity
    
    # 寻找零速度点
    for i in range(min_samples, len(velocity_axis)):
        current_velocity = abs(velocity_axis[i])
        
        if current_velocity <= zero_threshold:
            if self._verify_zero_velocity_point_single_axis(velocity_axis, i, zero_threshold):
                return i
    
    return None
```

## 主要改进

### 1. 更直观的检测逻辑
- **物理意义明确**: 零速度点代表运动的自然结束
- **阈值合理**: 5%最大速度作为零速度阈值更加合理
- **逻辑简单**: 直接寻找零点，避免复杂的减小检测

### 2. 提高检测准确性
- **完整运动周期**: 考虑从0到零速度的完整运动过程
- **真实最大值**: 在完整运动周期内找到真正的最大速度
- **避免误判**: 不会被中间的小幅波动误导

### 3. 增强鲁棒性
- **稳定性验证**: 确保零速度点是稳定的，不是暂时波动
- **备用策略**: 没有找到零速度点时使用全部数据
- **多轴适应**: 每个轴独立检测零速度点

## 使用方法

### 1. 自动应用

优化已经集成到所有velocity检测方法中：

```python
from motion_event_detector import MotionEventDetector

detector = MotionEventDetector(sample_rate=100)

# 所有方法都自动使用零速度点检测
result = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='velocity'  # 或其他velocity方法
)
```

### 2. 测试验证

```bash
python test_zero_velocity_detection.py
```

## 效果对比

### 原方法（减小5%检测）
```
运动过程: 加速 -> 最大速度 -> 减速 -> 零速度
检测逻辑: 寻找速度减小5%的点
问题: 可能在减速初期就检测到减小点，错过真正的最大速度
```

### 新方法（零速度点检测）
```
运动过程: 加速 -> 最大速度 -> 减速 -> 零速度
检测逻辑: 寻找回到零速度的点
优势: 在完整运动周期内找到真正的最大速度点
```

## 调试信息

### 1. 零速度点检测

```
寻找除初始时间外又一次达到0速度的时间点
  最大速度: 2.345
  零速度阈值: 0.117 (5%最大速度)
检测到零速度点: 索引150, 时间1.500s
  当前速度: 0.089, 阈值: 0.117
  零点验证: 后续低速比例 0.80 (稳定)
```

### 2. 单轴零速度点检测

```
  寻找X轴零速度点
    最大速度: 1.234
    零速度阈值: 0.062
    检测到X轴零速度点: 索引120, 时间1.200s
```

### 3. 最大速度检测

```
找到零速度点: 索引150, 时间1.500s
零速度点前最大速度: 索引80, 时间0.800s, 速度2.345
```

## 适用场景

### 1. 推荐使用
- **完整运动周期**: 包含加速-减速-回零的完整运动
- **自然运动**: 符合物理规律的运动模式
- **精确检测**: 需要准确找到真正最大速度的应用

### 2. 效果显著的情况
- **回零运动**: 运动最终回到静止状态
- **单次运动**: 一次性的运动事件
- **清晰边界**: 运动开始和结束边界清晰

## 参数配置

### 1. 零速度阈值

```python
zero_threshold = 0.05 * max_velocity  # 5%的最大速度
```

### 2. 稳定性验证

```python
verify_window = min(10, len(velocity_magnitude) - zero_point)  # 验证窗口
consistency_threshold = 0.6  # 60%一致性要求
tolerance_factor = 2  # 允许2倍阈值的波动
```

### 3. 时间约束

```python
min_samples = int(0.1 * sample_rate)  # 最小时间阈值
```

## 故障排除

### 1. 未找到零速度点

**问题**: 运动没有回到零速度
**解决**: 
- 检查运动是否完整
- 调整零速度阈值
- 使用备用的全数据搜索

### 2. 零速度点检测过早

**问题**: 在运动中期就检测到零速度点
**解决**:
- 增加稳定性验证要求
- 调整零速度阈值
- 检查数据噪声水平

### 3. 最大速度检测不准确

**问题**: 检测到的最大速度点不合理
**解决**:
- 验证零速度点检测是否正确
- 检查搜索范围设置
- 确认数据预处理质量

## 技术特性

### 1. 检测精度
- **零速度阈值**: 5%最大速度的精确阈值
- **稳定性要求**: 60%一致性的严格验证
- **时间分辨率**: 基于采样率的精确时间定位

### 2. 适应性
- **自动阈值**: 基于数据自动计算零速度阈值
- **多轴支持**: 每个轴独立进行零速度点检测
- **备用策略**: 提供完整的备用检测机制

### 3. 鲁棒性
- **噪声抑制**: 通过稳定性验证抑制噪声影响
- **边界处理**: 妥善处理数据边界情况
- **异常恢复**: 提供多层次的异常处理机制

## 更新日志

- **v1.8**: 添加零速度点检测优化，替代滑动窗口减小5%检测
- **v1.7**: 时间阈值优化，早期峰值直接设置为0.1秒
- **v1.6**: 三轴独立检测和主轴+副轴合成检测方法

现在的检测方法能够更准确地找到运动的真正最大速度点，通过检测零速度点来确定完整的运动周期！
