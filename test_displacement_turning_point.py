# -*- coding:utf-8 -*-
"""
测试基于速度转折点的位移检测方法
验证新的位移检测逻辑是否能正确找到正向运动的位移最大点
"""

import numpy as np
import matplotlib.pyplot as plt
from motion_event_detector import MotionEventDetector

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def create_complex_motion_data(sample_rate=100):
    """
    创建复杂的运动数据，包含速度反向的情况
    模拟：加速 -> 减速 -> 反向加速的运动模式
    """
    duration = 4.0  # 4秒数据
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 创建复杂的加速度模式：
    # 0-0.2秒: 静止
    # 0.2-1.2秒: 正向加速（真正的加速阶段）
    # 1.2-2.5秒: 减速（速度从正向减到零）
    # 2.5-3.5秒: 反向加速（速度变为负向）
    # 3.5-4.0秒: 反向减速
    
    acc_x = np.zeros_like(t)
    
    # 静止阶段（0-0.2秒）
    mask_still1 = (t >= 0) & (t < 0.2)
    acc_x[mask_still1] = 0.05 * np.random.normal(0, 0.02, np.sum(mask_still1))
    
    # 正向加速阶段（0.2-1.2秒）
    mask_accel = (t >= 0.2) & (t < 1.2)
    t_accel = t[mask_accel] - 0.2
    acc_x[mask_accel] = 2.0 * np.sin(np.pi * t_accel / 1.0)  # 平滑加速
    
    # 减速阶段（1.2-2.5秒）
    mask_decel = (t >= 1.2) & (t < 2.5)
    t_decel = t[mask_decel] - 1.2
    acc_x[mask_decel] = -1.5 * np.sin(np.pi * t_decel / 1.3)  # 平滑减速
    
    # 反向加速阶段（2.5-3.5秒）
    mask_rev_accel = (t >= 2.5) & (t < 3.5)
    t_rev_accel = t[mask_rev_accel] - 2.5
    acc_x[mask_rev_accel] = -1.8 * np.sin(np.pi * t_rev_accel / 1.0)  # 反向加速
    
    # 反向减速阶段（3.5-4.0秒）
    mask_rev_decel = (t >= 3.5) & (t <= 4.0)
    t_rev_decel = t[mask_rev_decel] - 3.5
    acc_x[mask_rev_decel] = 1.2 * np.sin(np.pi * t_rev_decel / 0.5)  # 反向减速
    
    # Y轴和Z轴添加噪声
    acc_y = 0.1 * np.random.normal(0, 0.1, len(t))
    acc_z = 0.3 * acc_x + 0.1 * np.random.normal(0, 0.1, len(t))
    
    # 组合三轴数据
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    return acc_data, t

def test_old_displacement_method():
    """
    测试原来的位移检测方法（整个过程的位移最大点）
    """
    print("=== 测试原来的位移检测方法 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_complex_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    # 模拟原来的简单位移检测方法
    def old_displacement_detection(displacement, start_idx):
        """模拟原来的位移检测方法"""
        # 简单地找到整个过程的位移最大点
        if np.max(displacement) > abs(np.min(displacement)):
            max_idx = np.argmax(displacement)
            return start_idx + max_idx
        else:
            min_idx = np.argmin(displacement)
            return start_idx + min_idx
    
    # 预处理和计算
    processed_data = detector.preprocess_data(acc_data)
    main_axis_data = processed_data[:, 0]  # X轴
    smoothed_data = np.convolve(main_axis_data, np.ones(5)/5, mode='same')
    
    # 找到起始点
    threshold = 0.1 * np.max(np.abs(smoothed_data))
    start_candidates = np.where(np.abs(smoothed_data) > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    # 计算位移
    dt = 1.0 / sample_rate
    velocity = np.cumsum(smoothed_data[start_idx:]) * dt
    displacement = np.cumsum(velocity) * dt
    
    # 使用原方法检测
    old_end_idx = old_displacement_detection(displacement, start_idx)
    old_duration = (old_end_idx - start_idx) / sample_rate
    
    print(f"原方法检测结果:")
    print(f"  起始时间: {start_idx/sample_rate:.3f}s")
    print(f"  结束时间: {old_end_idx/sample_rate:.3f}s")
    print(f"  持续时间: {old_duration:.3f}s")
    
    return start_idx, old_end_idx, old_duration, displacement, velocity

def test_new_displacement_method():
    """
    测试新的基于速度转折点的位移检测方法
    """
    print("\n=== 测试新的位移检测方法（基于速度转折点） ===")
    
    sample_rate = 100
    acc_data, time_axis = create_complex_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    # 使用新的位移检测方法
    start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
        acc_data, only_acceleration_phase=True, use_integration=True, detection_method='displacement'
    )
    
    duration = (end_idx - start_idx) / sample_rate
    print(f"新方法检测结果:")
    print(f"  起始时间: {start_idx/sample_rate:.3f}s")
    print(f"  结束时间: {end_idx/sample_rate:.3f}s")
    print(f"  持续时间: {duration:.3f}s")
    print(f"  置信度: {confidence:.3f}")
    
    return start_idx, end_idx, duration

def analyze_velocity_turning_point():
    """
    分析速度转折点的检测过程
    """
    print("\n=== 分析速度转折点检测过程 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_complex_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    main_axis_data = processed_data[:, 0]
    smoothed_data = np.convolve(main_axis_data, np.ones(5)/5, mode='same')
    
    # 找到起始点
    threshold = 0.1 * np.max(np.abs(smoothed_data))
    start_candidates = np.where(np.abs(smoothed_data) > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    # 计算速度和位移
    dt = 1.0 / sample_rate
    velocity = np.cumsum(smoothed_data[start_idx:]) * dt
    displacement = np.cumsum(velocity) * dt
    
    # 计算位移的速度（位移的导数）
    displacement_velocity = np.gradient(displacement) / dt
    
    # 找到速度转折点
    min_samples = int(0.2 * sample_rate)
    turning_point = detector._find_velocity_turning_point(displacement_velocity, min_samples)
    
    print(f"速度转折点分析:")
    if turning_point is not None:
        print(f"  转折点索引: {turning_point}")
        print(f"  转折点时间: {(start_idx + turning_point)/sample_rate:.3f}s")
        print(f"  转折点前的位移: {displacement[turning_point]:.3f}")
        print(f"  转折点的速度: {displacement_velocity[turning_point]:.3f}")
    else:
        print("  未找到明显的速度转折点")
    
    return start_idx, displacement, displacement_velocity, turning_point

def visualize_comparison():
    """
    可视化对比新旧方法的检测结果
    """
    print("\n=== 生成对比可视化图 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_complex_motion_data(sample_rate)
    
    # 测试两种方法
    old_start, old_end, old_duration, displacement, velocity = test_old_displacement_method()
    new_start, new_end, new_duration = test_new_displacement_method()
    
    # 分析速度转折点
    start_idx, disp_full, disp_vel, turning_point = analyze_velocity_turning_point()
    
    # 创建可视化
    fig, axes = plt.subplots(4, 1, figsize=(14, 12))
    
    # 子图1: 原始加速度数据
    axes[0].plot(time_axis, acc_data[:, 0], 'b-', label='X轴加速度')
    axes[0].axvspan(old_start/sample_rate, old_end/sample_rate, color='red', alpha=0.3, label=f'原方法 ({old_duration:.3f}s)')
    axes[0].axvspan(new_start/sample_rate, new_end/sample_rate, color='green', alpha=0.3, label=f'新方法 ({new_duration:.3f}s)')
    axes[0].set_title('加速度数据与检测结果对比')
    axes[0].set_ylabel('加速度 (m/s²)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 子图2: 速度积分
    time_vel = np.arange(start_idx, start_idx + len(velocity)) / sample_rate
    axes[1].plot(time_vel, velocity, 'g-', label='积分速度')
    axes[1].axvspan(old_start/sample_rate, old_end/sample_rate, color='red', alpha=0.3, label='原方法检测')
    axes[1].axvspan(new_start/sample_rate, new_end/sample_rate, color='green', alpha=0.3, label='新方法检测')
    axes[1].set_title('速度积分结果')
    axes[1].set_ylabel('速度 (m/s)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 子图3: 位移积分和转折点
    time_disp = np.arange(start_idx, start_idx + len(displacement)) / sample_rate
    axes[2].plot(time_disp, displacement, 'purple', label='位移积分')
    axes[2].axvspan(old_start/sample_rate, old_end/sample_rate, color='red', alpha=0.3, label='原方法检测')
    axes[2].axvspan(new_start/sample_rate, new_end/sample_rate, color='green', alpha=0.3, label='新方法检测')
    
    # 标记转折点
    if turning_point is not None:
        turning_time = (start_idx + turning_point) / sample_rate
        axes[2].axvline(x=turning_time, color='orange', linestyle='--', linewidth=2, label='速度转折点')
        axes[2].plot(turning_time, displacement[turning_point], 'o', color='orange', markersize=8)
    
    axes[2].set_title('位移积分结果与速度转折点')
    axes[2].set_ylabel('位移 (m)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    # 子图4: 位移速度（位移的导数）
    time_disp_vel = np.arange(start_idx, start_idx + len(disp_vel)) / sample_rate
    axes[3].plot(time_disp_vel, disp_vel, 'orange', label='位移速度（位移导数）')
    axes[3].axhline(y=0, color='k', linestyle='-', alpha=0.3)
    
    if turning_point is not None:
        turning_time = (start_idx + turning_point) / sample_rate
        axes[3].axvline(x=turning_time, color='orange', linestyle='--', linewidth=2, label='速度转折点')
        axes[3].plot(turning_time, disp_vel[turning_point], 'o', color='orange', markersize=8)
    
    axes[3].set_title('位移速度（用于检测转折点）')
    axes[3].set_xlabel('时间 (秒)')
    axes[3].set_ylabel('位移速度 (m/s)')
    axes[3].legend()
    axes[3].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('displacement_turning_point_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("对比图已保存为: displacement_turning_point_comparison.png")

def test_different_scenarios():
    """
    测试不同运动场景下的检测效果
    """
    print("\n=== 测试不同运动场景 ===")
    
    scenarios = [
        ("简单加速", lambda t: 2.0 * t),
        ("加速-减速", lambda t: 2.0 * np.sin(np.pi * t / 2)),
        ("加速-减速-反向", lambda t: 2.0 * np.sin(np.pi * t / 1.5) if t < 3 else -1.0 * (t - 3)),
        ("多次振荡", lambda t: 1.5 * np.sin(2 * np.pi * t / 1.5))
    ]
    
    sample_rate = 100
    
    print("场景名称          原方法持续时间  新方法持续时间  改进效果")
    print("-" * 60)
    
    for scenario_name, acc_func in scenarios:
        # 创建测试数据
        duration = 3.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        acc_x = np.array([acc_func(time) for time in t])
        acc_y = 0.1 * np.random.normal(0, 0.1, len(t))
        acc_z = 0.2 * acc_x + 0.1 * np.random.normal(0, 0.1, len(t))
        acc_data = np.column_stack([acc_x, acc_y, acc_z])
        
        # 测试两种方法
        detector = MotionEventDetector(sample_rate=sample_rate)
        
        try:
            # 新方法
            start_new, end_new, conf_new, pattern_new = detector.detect_single_motion_event(
                acc_data, only_acceleration_phase=True, use_integration=True, detection_method='displacement'
            )
            new_duration = (end_new - start_new) / sample_rate
            
            # 简单的原方法模拟
            processed_data = detector.preprocess_data(acc_data)
            smoothed_data = np.convolve(processed_data[:, 0], np.ones(5)/5, mode='same')
            threshold = 0.1 * np.max(np.abs(smoothed_data))
            start_candidates = np.where(np.abs(smoothed_data) > threshold)[0]
            start_old = start_candidates[0] if len(start_candidates) > 0 else 0
            
            dt = 1.0 / sample_rate
            velocity = np.cumsum(smoothed_data[start_old:]) * dt
            displacement = np.cumsum(velocity) * dt
            
            if np.max(displacement) > abs(np.min(displacement)):
                max_idx = np.argmax(displacement)
                end_old = start_old + max_idx
            else:
                min_idx = np.argmin(displacement)
                end_old = start_old + min_idx
            
            old_duration = (end_old - start_old) / sample_rate
            
            # 评估改进效果
            if abs(new_duration - old_duration) < 0.1:
                improvement = "相似"
            elif new_duration < old_duration:
                improvement = "更精确"
            else:
                improvement = "更保守"
            
            print(f"{scenario_name:15} {old_duration:12.3f}s {new_duration:12.3f}s   {improvement}")
            
        except Exception as e:
            print(f"{scenario_name:15} {'错误':>12} {'错误':>12}   检测失败")

if __name__ == "__main__":
    print("基于速度转折点的位移检测方法测试")
    print("=" * 60)
    
    print("改进说明:")
    print("- 原方法: 找整个过程的位移最大点")
    print("- 新方法: 先找速度转折点，再在转折点前找位移最大点")
    print("- 目标: 避免减速过程中速度反向导致的位移反向问题")
    print()
    
    # 测试新旧方法对比
    test_old_displacement_method()
    test_new_displacement_method()
    
    # 分析速度转折点
    analyze_velocity_turning_point()
    
    # 生成可视化对比
    visualize_comparison()
    
    # 测试不同场景
    test_different_scenarios()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n改进总结:")
    print("1. ✅ 添加了速度转折点检测功能")
    print("2. ✅ 在转折点前寻找位移最大点")
    print("3. ✅ 避免了减速阶段的速度反向问题")
    print("4. ✅ 提供了稳定性验证机制")
    print("5. ✅ 保持了最小时间阈值功能")
    print("\n现在位移方法能更准确地检测正向运动的加速阶段！")
