import socket
import csv
import base64
import wave
import os
import numpy as np
from datetime import datetime
import logging


"""
接受来自手机端的传感器数据和音频数据，并保存到CSV文件和WAV文件中
"""

def timestamp_to_time_format(timestamp):
    """将时间戳转换为 HH-MM-SS.mmm 格式"""
    dt = datetime.fromtimestamp(int(timestamp)/1000)  # 转换为秒
    ms = int(timestamp) % 1000  # 获取毫秒部分
    return f"{dt.strftime('%H-%M-%S')}.{ms:03d}"

class DataReceiver:
    # 音频包时间阈值(毫秒)，如果两个包的时间差超过这个值，可能存在丢包
    AUDIO_PACKET_THRESHOLD_MS = 150  # 假设正常情况下音频包间隔不会超过100ms
    
    def __init__(self, host='0.0.0.0', port=5005, save_dir='D:/collected_data'): 
        self.host = host
        self.port = port
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.sock.bind((self.host, self.port))
        
        self.start_timestamp = None
        self.data_dir = save_dir
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 添加用于记录上一个音频包时间戳的变量
        self.last_audio_timestamp = None
        # 添加丢包计数器
        self.packet_loss_count = 0
        
        # 配置日志
        logging.basicConfig(
            filename=os.path.join(self.data_dir, 'packet_loss.log'),
            level=logging.INFO,
            format='%(asctime)s - %(message)s'
        )
        
        print(f"UDP服务器端口 {host}:{port}")

    def init_files(self, first_timestamp):
        """使用第一个数据包的时间戳初始化文件"""
        if self.start_timestamp is None:
            self.start_timestamp = first_timestamp
            self.formatted_time = timestamp_to_time_format(self.start_timestamp)
            
            # 初始化CSV文件，添加重力传感器
            self.sensor_files = {}
            # sensors = ['acce', 'gyro', 'grav', 'linacc', 'rotvec']
            sensors = ['acce', 'gyro', 'grav', 'rotvec']
            for sensor in sensors:
                filename = f'{self.data_dir}/{sensor}_data_{self.formatted_time}.csv'
                with open(filename, 'w', newline='') as f:
                    writer = csv.writer(f)
                    if sensor == 'rotvec':
                        # 旋转向量传感器有四个值
                        writer.writerow(['time', 'sensor_timestamp', 'x', 'y', 'z', 'w'])
                    else:
                        writer.writerow(['time', 'sensor_timestamp', 'x', 'y', 'z'])

                self.sensor_files[sensor] = filename
            
            # 修改WAV文件的采样率为48kHz
            self.wav_file = wave.open(f'{self.data_dir}/audio_{self.formatted_time}.wav', 'wb')
            self.wav_file.setnchannels(1)  # 单声道
            self.wav_file.setsampwidth(2)  # 16位采样
            self.wav_file.setframerate(48000)  # 采样率为48000Hz

    def process_sensor_data(self, data):
        parts = data.split(',')
        if len(parts) >= 6:
            timestamp = parts[0]
            if self.start_timestamp is None:
                self.init_files(timestamp)
            
            sensor_type = parts[1]
            if sensor_type in self.sensor_files:
                formatted_time = timestamp_to_time_format(timestamp)
                with open(self.sensor_files[sensor_type], 'a', newline='') as f:
                    writer = csv.writer(f)
                    if sensor_type == 'rotvec':
                        # 旋转向量传感器有四个值
                        writer.writerow([formatted_time, parts[2], parts[4], parts[5], parts[6], parts[7]])
                    else:
                        writer.writerow([formatted_time, parts[2], parts[4], parts[5], parts[6]])

    def process_audio_data(self, data):
        try:
            timestamp, data_type, audio_base64 = data.split(',', 2)
            if self.start_timestamp is None:
                self.init_files(timestamp)
            
            # 添加音频包时间阈值检测
            current_timestamp = int(timestamp)
            
            # 记录当前音频包的到达时间(系统时间)，用于分析传输延迟
            arrival_time = datetime.now().timestamp() * 1000  # 转换为毫秒
            system_device_time_diff = arrival_time - current_timestamp
            
            if self.last_audio_timestamp is not None:
                time_diff = current_timestamp - self.last_audio_timestamp
                
                # 计算预期的音频数据长度（基于时间差）
                expected_audio_samples = int(time_diff * 48)  # 48kHz采样率，每毫秒48个样本
                
                if time_diff > self.AUDIO_PACKET_THRESHOLD_MS:
                    self.packet_loss_count += 1
                    
                    # 计算丢包的具体时间区间
                    lost_start_time = self.last_audio_timestamp + 20  # 估计上一个包后20ms开始丢失
                    lost_end_time = current_timestamp - 20  # 估计当前包前20ms结束丢失
                    
                    # 计算绝对时间格式
                    lost_start_formatted = timestamp_to_time_format(str(lost_start_time))
                    lost_end_formatted = timestamp_to_time_format(str(lost_end_time))
                    
                    loss_msg = f"可能的音频丢包: 当前包时间戳 {current_timestamp}，上一包时间戳 {self.last_audio_timestamp}，差值 {time_diff}ms"
                    loss_msg += f"，丢包时间区间: {lost_start_formatted} 到 {lost_end_formatted}"
                    loss_msg += f"，丢失约 {(time_diff-40)/1000:.3f} 秒音频数据"
                    loss_msg += f"，系统接收时间比设备时间晚: {system_device_time_diff:.1f}ms"
                    
                    print(loss_msg)
                    logging.info(loss_msg)
                    
                    # # 将丢包信息保存到专门的文件中
                    # if not hasattr(self, 'packet_loss_file'):
                    #     self.packet_loss_file = open(f'{self.data_dir}/packet_loss_details.csv', 'w', newline='')
                    #     self.packet_loss_writer = csv.writer(self.packet_loss_file)
                    #     self.packet_loss_writer.writerow(['packet_index', 'lost_start_time', 'lost_end_time', 
                    #                                     'lost_duration_ms', 'absolute_start_time', 'absolute_end_time'])
                    
                    # self.packet_loss_writer.writerow([self.packet_loss_count, lost_start_time, lost_end_time, 
                    #                                 time_diff-40, lost_start_formatted, lost_end_formatted])
                    # self.packet_loss_file.flush()  # 确保立即写入文件
                    

                
                # 在AUDIO_PACKET_THRESHOLD_MS的80%以上但未超过阈值时，记录警告
                elif time_diff > self.AUDIO_PACKET_THRESHOLD_MS * 0.8:
                    warning_msg = f"音频包间隔偏大: {time_diff}ms，接近丢包阈值 {self.AUDIO_PACKET_THRESHOLD_MS}ms"
                    logging.warning(warning_msg)
            
            # 更新上一个音频包的时间戳
            self.last_audio_timestamp = current_timestamp
            
            # 记录音频数据长度和对应的时间戳
            audio_data = base64.b64decode(audio_base64)
            audio_samples = len(audio_data) / 2  # 16位采样 = 2字节/样本
            audio_duration_ms = (audio_samples / 48)  # 48kHz = 48样本/毫秒
            
            if not hasattr(self, 'audio_metadata_file'):
                self.audio_metadata_file = open(f'{self.data_dir}/audio_metadata.csv', 'w', newline='')
                self.audio_metadata_writer = csv.writer(self.audio_metadata_file)
                self.audio_metadata_writer.writerow(['packet_index', 'timestamp', 'formatted_time', 
                                                'samples', 'duration_ms', 'system_time_diff_ms'])
            
            packet_index = getattr(self, 'total_audio_packets', 0) + 1
            self.audio_metadata_writer.writerow([packet_index, current_timestamp, 
                                            timestamp_to_time_format(str(current_timestamp)), 
                                            audio_samples, audio_duration_ms, system_device_time_diff])
            
            # 每50个包保存一次元数据文件
            if packet_index % 50 == 0:
                self.audio_metadata_file.flush()
            
            # 记录总数据量统计
            if not hasattr(self, 'total_audio_packets'):
                self.total_audio_packets = 0
                self.total_audio_bytes = 0
            
            self.total_audio_packets += 1
            self.total_audio_bytes += len(audio_data)
            
            # 每100个包记录一次统计信息
            if self.total_audio_packets % 100 == 0:
                logging.info(f"已接收音频包: {self.total_audio_packets}, 总字节数: {self.total_audio_bytes}, " 
                        f"丢包率: {(self.packet_loss_count/self.total_audio_packets)*100:.2f}%")
            
            self.wav_file.writeframes(audio_data)
        except Exception as e:
            print(f"处理音频数据时出错: {e}")
            import traceback
            traceback.print_exc()  # 打印详细错误堆栈


    def start_receiving(self):
        try:
            print("开始接收数据...")
            while True:
                data, addr = self.sock.recvfrom(65535)
                data_str = data.decode('utf-8')
                
                # 根据数据类型进行处理
                if 'audio' in data_str:
                    self.process_audio_data(data_str)
                else:
                    self.process_sensor_data(data_str)
                
        except KeyboardInterrupt:
            print("\n停止接收数据")
            if self.packet_loss_count > 0:
                print(f"检测到可能的音频丢包次数: {self.packet_loss_count}")
            self.cleanup()
        except Exception as e:
            print(f"发生错误: {e}")
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'wav_file'):
            self.wav_file.close()
        self.sock.close()

if __name__ == "__main__":

    save_path = r"D:\research\data\方向角度测量\10" 
    ""
    receiver = DataReceiver(save_dir=save_path)
    receiver.start_receiving()
