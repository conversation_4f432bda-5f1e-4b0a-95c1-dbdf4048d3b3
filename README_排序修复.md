# Segment编号排序修复

## 问题描述

在原始代码中，位移图片的显示顺序存在问题：

- **原来的顺序**: 10, 11, 12, 13, 14, 15, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9
- **期望的顺序**: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16

这是因为文件系统默认使用字符串排序，而不是数字排序。

## 解决方案

### 1. 问题根源
```python
# 原来的处理方式（隐式字符串排序）
files = glob.glob("displacement_segment*.csv")
# 结果: ['segment001.csv', 'segment010.csv', 'segment011.csv', ..., 'segment002.csv']
```

### 2. 修复方法
在 `visualize_individual_motions` 函数中添加了排序逻辑：

```python
# 提取segment编号
segment_numbers = []
for file_path in source_files:
    segment_num = extract_segment_number(file_path)
    segment_numbers.append(segment_num if segment_num is not None else -1)

# 按segment编号排序
valid_indices = []
invalid_indices = []

for i, segment_num in enumerate(segment_numbers):
    if segment_num >= 0:
        valid_indices.append((segment_num, i))
    else:
        invalid_indices.append((-1, i))

# 按数字大小排序
valid_indices.sort(key=lambda x: x[0])

# 重新排列数据
sorted_indices = [idx for _, idx in valid_indices] + [idx for _, idx in invalid_indices]
displacement_endpoints = np.array([displacement_endpoints[i] for i in sorted_indices])
source_files = [source_files[i] for i in sorted_indices]
segment_numbers = [segment_numbers[i] for i in sorted_indices]
```

## 修复效果

### 修复前
```
子图排列顺序:
Position 1: Segment 10
Position 2: Segment 11  
Position 3: Segment 12
Position 4: Segment 13
Position 5: Segment 14
Position 6: Segment 15
Position 7: Segment 16
Position 8: Segment 1
Position 9: Segment 2
...
```

### 修复后
```
子图排列顺序:
Position 1: Segment 1
Position 2: Segment 2
Position 3: Segment 3
Position 4: Segment 4
Position 5: Segment 5
Position 6: Segment 6
Position 7: Segment 7
Position 8: Segment 8
Position 9: Segment 9
Position 10: Segment 10
Position 11: Segment 11
Position 12: Segment 12
Position 13: Segment 13
Position 14: Segment 14
Position 15: Segment 15
Position 16: Segment 16
```

## 技术细节

### 1. Segment编号提取
```python
def extract_segment_number(filename):
    """从文件名中提取segment编号"""
    match = re.search(r'segment(\d+)', os.path.basename(filename))
    if match:
        return int(match.group(1))  # 返回整数，不是字符串
    return None
```

### 2. 数字排序vs字符串排序
```python
# 字符串排序（错误）
files = ['segment1.csv', 'segment10.csv', 'segment2.csv']
files.sort()  # 结果: ['segment1.csv', 'segment10.csv', 'segment2.csv']

# 数字排序（正确）
files_with_nums = [(extract_segment_number(f), f) for f in files]
files_with_nums.sort(key=lambda x: x[0])  # 按数字排序
sorted_files = [f for _, f in files_with_nums]  # 结果: ['segment1.csv', 'segment2.csv', 'segment10.csv']
```

### 3. 数据同步排序
确保所有相关数组都按相同顺序排序：
- `displacement_endpoints`: 位移终点坐标
- `source_files`: 源文件路径
- `segment_numbers`: segment编号

## 兼容性

### 1. 向后兼容
- ✅ 支持原有的文件命名格式
- ✅ 处理无效的segment编号
- ✅ 保持原有的功能不变

### 2. 错误处理
```python
# 处理无效segment编号
if segment_num is not None:
    valid_indices.append((segment_num, i))
else:
    invalid_indices.append((-1, i))  # 无效的放在最后
```

## 测试验证

### 1. 运行测试脚本
```bash
python test_sorting_fix.py
```

### 2. 测试内容
- ✅ Segment编号提取功能
- ✅ 文件排序逻辑
- ✅ 完整可视化流程
- ✅ 边界情况处理

### 3. 预期输出
```
排序验证:
期望顺序: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
实际顺序: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
✅ 排序正确！segment编号按1-16顺序排列
```

## 使用方法

### 1. 直接使用
修复后的代码会自动按正确顺序排列，无需额外操作：

```python
from individual_motion_visualization import visualize_individual_motions

# 自动按1-16顺序排列
visualize_individual_motions(
    csv_folder="your_data_folder",
    output_folder="output_folder"
)
```

### 2. 验证排序
在控制台输出中查看排序信息：
```
排序后的segment编号: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
```

## 文件命名要求

### 1. 标准格式
```
displacement_segment001.csv
displacement_segment002.csv
...
displacement_segment016.csv
```

### 2. 支持的变体
```
displacement_segment1.csv     # 无前导零
displacement_segment01.csv    # 单个前导零
displacement_segment001.csv   # 双前导零
```

### 3. 不支持的格式
```
displacement_seg1.csv         # 缺少"segment"关键字
displacement_1.csv            # 缺少"segment"关键字
segment1_displacement.csv     # 顺序错误
```

## 故障排除

### 1. 排序仍然错误
- 检查文件命名是否包含"segment"关键字
- 确认segment编号是数字格式
- 查看控制台输出的排序信息

### 2. 部分文件未显示
- 检查文件名格式是否正确
- 确认文件在指定文件夹中
- 查看是否有重复的segment编号

### 3. 调试信息
代码会输出排序后的segment编号，用于验证：
```python
print(f"排序后的segment编号: {segment_numbers}")
```

## 更新日志

- **v1.1**: 修复segment编号排序问题
- **v1.0**: 原始版本（存在排序问题）

现在您的位移图片将按照正确的1-16顺序显示！
