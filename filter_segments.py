import os
import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt
from interpolation import sort_and_clean_data
from filter import high_pass_filter  # 高通滤波器导入
from KalmanFilter import KalmanFilter  # 卡尔曼滤波器导入

"""
对文件夹下的所有加速度计数据片段进行滤波
"""

def process_segment_files(input_dir, use_kalman=True, plot_results=True):
    """批量处理分割的加速度数据片段"""
    
    # 只查找指定文件名全名的文件
    segment_files = [f for f in os.listdir(input_dir) 
                    if re.match(r'^segment\d+_acce_\d{2}-\d{2}-\d{2}\.\d{3}\.csv$', f)]
                    # if f.startswith('linacc_data') and f.endswith('.csv')]
                    # if f.startswith('linear') and f.endswith('.csv')]
    
    print(f"找到 {len(segment_files)} 个segment1数据文件")
    
    for file in segment_files:
        input_path = os.path.join(input_dir, file)

        # 读取并处理加速度数据
        original_df = pd.read_csv(input_path)
        
        # 数据排序和清理
        cleaned_df = sort_and_clean_data(original_df)
        
        
        # 根据选择的滤波方法设置输出文件名
        if use_kalman:
            output_path = input_path.replace('.csv', '_kalman.csv')
        else:
            output_path = input_path.replace('.csv', '_highpass_10Hz.csv')
        
        print(f"\n处理文件: {file}")
        df = cleaned_df
        filtered_data = {'time': df['time']}
        
        if use_kalman:
            # 卡尔曼滤波处理
            process_variance = 1e-7  # 过程噪声方差(重力加速度的变化)
            measurement_variance = 0.002242  # 测量噪声方差（传感器误差）
            
            for axis in ['x', 'y', 'z']:
                kf = KalmanFilter(
                    process_variance=process_variance,
                    measurement_variance=measurement_variance,
                    estimation_error=1.0,
                    initial_estimate=df[axis].iloc[0]
                )
                
                gravity_component = []
                data = df[axis].values
                for measurement in data:
                    gravity_estimate = kf.update(measurement)
                    gravity_component.append(gravity_estimate)
                
                filtered_data[axis] = data - np.array(gravity_component)
        
        else:
            # 高通滤波处理 2Hz=0.996， 5Hz=0.99， 10Hz=0.98
            for axis in ['x', 'y', 'z']:
                filtered_data[axis] = high_pass_filter(df[axis].values, alpha=0.98)
        
        # 保存滤波后的数据
        pd.DataFrame(filtered_data).to_csv(output_path, index=False)
        print(f"已保存滤波后的数据: {output_path}")
        
        # 绘制对比图
        if plot_results:
            plt.figure(figsize=(12, 8))
            for i, axis in enumerate(['x', 'y', 'z'], 1):
                plt.subplot(3, 1, i)
                plt.plot(df['time'], df[axis], 
                        label=f'Original {axis}', linestyle='--', alpha=0.5)
                plt.plot(df['time'], filtered_data[axis], 
                        label=f'{"Kalman" if use_kalman else "Highpass"} {axis}')
                plt.legend()
                plt.title(f'{axis.upper()}-axis Acceleration')
                plt.grid(True)
            
            plt.tight_layout()
            plt.savefig(output_path.replace('.csv', '_plot.png'))
            plt.show()
            plt.close()

if __name__ == "__main__":

    data_dir = r"E:\Document\user001\Session_20250503_181352"
    
    # 通过修改use_kalman参数来选择滤波方法
    process_segment_files(data_dir, use_kalman=True, plot_results=True)
    print("\n完成segment数据处理")
    
    # # 使用高通滤波
    # process_segment_files(data_dir, use_kalman=False, plot_results=True)
    # print("\n完成segment1高通滤波处理")
