# -*- coding:utf-8 -*-
"""
测试修正后的零速度点检测逻辑
验证：从第一个样本点开始寻找零点，如果零点时间<0.1s则最终输出设置为0.1s
"""

import numpy as np
import matplotlib.pyplot as plt
from motion_event_detector import MotionEventDetector

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def create_early_zero_motion_data(sample_rate=100):
    """
    创建早期零速度点的运动数据
    模拟在0.05秒就回到零速度的运动
    """
    duration = 2.0  # 2秒数据
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # X轴：很快回到零速度（0.05秒）
    acc_x = np.zeros_like(t)
    mask_x = (t >= 0.2) & (t < 0.25)  # 0.05秒的运动
    t_x = t[mask_x] - 0.2
    acc_x[mask_x] = 4.0 * np.sin(np.pi * t_x / 0.05)  # 快速加速-减速
    
    # Y轴：稍长一点回到零速度（0.08秒）
    acc_y = np.zeros_like(t)
    mask_y = (t >= 0.3) & (t < 0.38)  # 0.08秒的运动
    t_y = t[mask_y] - 0.3
    acc_y[mask_y] = 3.0 * np.sin(np.pi * t_y / 0.08)
    
    # Z轴：正常长度回到零速度（0.15秒）
    acc_z = np.zeros_like(t)
    mask_z = (t >= 0.4) & (t < 0.55)  # 0.15秒的运动
    t_z = t[mask_z] - 0.4
    acc_z[mask_z] = 2.0 * np.sin(np.pi * t_z / 0.15)
    
    # 添加少量噪声
    noise_level = 0.02
    acc_x += noise_level * np.random.normal(0, 1, len(t))
    acc_y += noise_level * np.random.normal(0, 1, len(t))
    acc_z += noise_level * np.random.normal(0, 1, len(t))
    
    # 组合三轴数据
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    return acc_data, t

def test_corrected_zero_detection():
    """
    测试修正后的零速度点检测逻辑
    """
    print("=== 测试修正后的零速度点检测逻辑 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_early_zero_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    methods = [
        ('velocity', '三轴合速度矢量'),
        ('velocity_individual', '三轴独立检测'),
        ('velocity_main_secondary', '主轴+副轴合成')
    ]
    
    print("测试场景: 早期零速度点（理论上<0.1秒）")
    print("预期行为: 从第一个样本点开始寻找零点，如果零点<0.1s则最终输出0.1s")
    print()
    
    results = {}
    
    for method_name, method_desc in methods:
        print(f"{'-'*50}")
        print(f"测试方法: {method_desc} ({method_name})")
        print(f"{'-'*50}")
        
        try:
            start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
                acc_data, only_acceleration_phase=True, use_integration=True, detection_method=method_name
            )
            
            duration = (end_idx - start_idx) / sample_rate
            results[method_name] = {
                'start_time': start_idx / sample_rate,
                'end_time': end_idx / sample_rate,
                'duration': duration,
                'confidence': confidence,
                'success': True
            }
            
            print(f"检测结果:")
            print(f"  起始时间: {start_idx/sample_rate:.3f}s")
            print(f"  结束时间: {end_idx/sample_rate:.3f}s")
            print(f"  持续时间: {duration:.3f}s")
            print(f"  置信度: {confidence:.3f}")
            
            # 验证最小时间阈值强制执行
            if abs(duration - 0.1) < 0.01:
                print(f"  ✅ 最小时间阈值强制执行成功（0.1秒）")
            elif duration > 0.1:
                print(f"  ℹ️  持续时间大于0.1秒，可能是正常检测")
            else:
                print(f"  ❌ 最小时间阈值强制执行失败（<0.1秒）")
            
        except Exception as e:
            print(f"检测失败: {e}")
            results[method_name] = {
                'success': False,
                'error': str(e)
            }
        
        print()
    
    return acc_data, time_axis, results

def analyze_zero_detection_process():
    """
    分析零速度点检测的详细过程
    """
    print("=== 分析零速度点检测过程 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_early_zero_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    
    # 计算三轴速度
    window = 5
    smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
    smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
    smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
    
    combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
    threshold = 0.1 * np.max(combined_acc)
    start_candidates = np.where(combined_acc > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    dt = 1.0 / sample_rate
    velocity_x = np.cumsum(smoothed_x[start_idx:]) * dt
    velocity_y = np.cumsum(smoothed_y[start_idx:]) * dt
    velocity_z = np.cumsum(smoothed_z[start_idx:]) * dt
    velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)
    
    # 分析零速度点检测
    min_samples = int(0.1 * sample_rate)
    
    print(f"零速度点检测分析:")
    print(f"  时间阈值: 0.1秒 ({min_samples}个样本)")
    print(f"  运动起始索引: {start_idx}")
    
    # 分析各轴的零速度点（从第一个样本点开始）
    axis_names = ['X', 'Y', 'Z']
    axis_velocities = [velocity_x, velocity_y, velocity_z]
    
    for axis_name, velocity_axis in zip(axis_names, axis_velocities):
        print(f"\n  {axis_name}轴分析:")
        zero_point = detector._find_first_velocity_decrease_single_axis(velocity_axis, min_samples, axis_name)
        if zero_point is not None:
            zero_time = (start_idx + zero_point) / sample_rate
            print(f"    零速度点: 索引{zero_point}, 时间{zero_time:.3f}s")
            
            # 检查是否在时间阈值之前
            if zero_point < min_samples:
                print(f"    ⚠️  零速度点在时间阈值前({zero_point/sample_rate:.3f}s < 0.1s)")
                print(f"    应该强制使用0.1秒作为最终结果")
            else:
                print(f"    ✅ 零速度点在时间阈值后，正常使用")
            
            # 在零速度点之前找最大速度（绝对值）
            search_range = velocity_axis[:zero_point + 1]
            if len(search_range) > 0:
                max_idx = np.argmax(np.abs(search_range))
                max_time = (start_idx + max_idx) / sample_rate
                max_value = velocity_axis[max_idx]
                print(f"    零速度点前最大速度: 索引{max_idx}, 时间{max_time:.3f}s, 速度{max_value:.3f}")
        else:
            print(f"    未找到零速度点")
    
    return start_idx, velocity_x, velocity_y, velocity_z, velocity_magnitude

def visualize_corrected_detection():
    """
    可视化修正后的检测逻辑
    """
    print("\n=== 生成修正后检测逻辑可视化 ===")
    
    # 获取测试数据和结果
    acc_data, time_axis, results = test_corrected_zero_detection()
    start_idx, velocity_x, velocity_y, velocity_z, velocity_magnitude = analyze_zero_detection_process()
    
    sample_rate = 100
    min_samples = int(0.1 * sample_rate)
    
    # 创建可视化
    fig, axes = plt.subplots(4, 1, figsize=(14, 12))
    
    # 子图1: 加速度数据
    axes[0].plot(time_axis, acc_data[:, 0], 'r-', label='X轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 1], 'g-', label='Y轴加速度', alpha=0.8)
    axes[0].plot(time_axis, acc_data[:, 2], 'b-', label='Z轴加速度', alpha=0.8)
    
    # 标记时间阈值线
    threshold_time = (start_idx + min_samples) / sample_rate
    axes[0].axvline(x=threshold_time, color='orange', linestyle='--', linewidth=2, label='0.1s时间阈值')
    
    axes[0].set_title('加速度数据（早期零速度点场景）')
    axes[0].set_ylabel('加速度 (m/s²)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 子图2: 三轴速度分量
    time_vel = np.arange(start_idx, start_idx + len(velocity_x)) / sample_rate
    axes[1].plot(time_vel, velocity_x, 'r-', label='X轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_y, 'g-', label='Y轴速度', linewidth=2)
    axes[1].plot(time_vel, velocity_z, 'b-', label='Z轴速度', linewidth=2)
    
    # 标记零速度点和时间阈值
    axes[1].axvline(x=threshold_time, color='orange', linestyle='--', linewidth=2, label='0.1s时间阈值')
    
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    for velocity_axis, color, axis_name in zip([velocity_x, velocity_y, velocity_z], ['red', 'green', 'blue'], ['X', 'Y', 'Z']):
        # 找零速度点（从第一个样本点开始）
        zero_point = detector._find_first_velocity_decrease_single_axis(velocity_axis, min_samples, axis_name)
        if zero_point is not None:
            zero_time = time_vel[zero_point]
            
            # 根据是否在时间阈值前使用不同的标记
            if zero_point < min_samples:
                axes[1].axvline(x=zero_time, color=color, linestyle=':', alpha=0.7, label=f'{axis_name}轴零点(早期)')
                # 标记强制使用的0.1秒点
                axes[1].axvline(x=threshold_time, color=color, linestyle='-', alpha=0.5)
            else:
                axes[1].axvline(x=zero_time, color=color, linestyle=':', alpha=0.7, label=f'{axis_name}轴零点(正常)')
            
            # 在零速度点前找最大速度
            search_range = velocity_axis[:zero_point + 1]
            if len(search_range) > 0:
                max_idx = np.argmax(np.abs(search_range))
                max_time = time_vel[max_idx]
                max_value = velocity_axis[max_idx]
                axes[1].plot(max_time, max_value, 'o', color=color, markersize=8, 
                           label=f'{axis_name}轴最大速度')
    
    axes[1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axes[1].set_title('三轴速度分量与零速度点检测（从第一个样本点开始）')
    axes[1].set_ylabel('速度 (m/s)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 子图3: 检测逻辑说明
    axes[2].text(0.05, 0.9, '修正后的检测逻辑:', fontsize=12, fontweight='bold')
    axes[2].text(0.05, 0.8, '1. 从第一个样本点开始寻找零速度点', fontsize=10)
    axes[2].text(0.05, 0.7, '2. 通过相邻样本点一正一负判断零速度点', fontsize=10)
    axes[2].text(0.05, 0.6, '3. 在0时刻到零速度点之间找绝对值最大速度', fontsize=10)
    axes[2].text(0.05, 0.5, '4. 如果零速度点<0.1s，强制最终输出为0.1s', fontsize=10, color='red', fontweight='bold')
    
    axes[2].text(0.05, 0.3, '与原逻辑的区别:', fontsize=12, fontweight='bold')
    axes[2].text(0.05, 0.2, '• 原逻辑: 从0.1s后开始寻找零速度点', fontsize=10, color='gray')
    axes[2].text(0.05, 0.1, '• 新逻辑: 从第一个样本点开始寻找，后续强制应用阈值', fontsize=10, color='green')
    
    axes[2].set_xlim(0, 1)
    axes[2].set_ylim(0, 1)
    axes[2].axis('off')
    
    # 子图4: 检测结果总结
    axes[3].text(0.05, 0.8, '修正后检测结果:', fontsize=12, fontweight='bold')
    
    y_pos = 0.7
    for method_name in ['velocity', 'velocity_individual', 'velocity_main_secondary']:
        method_labels = {'velocity': '三轴合速度', 'velocity_individual': '三轴独立', 'velocity_main_secondary': '主轴+副轴'}
        if results[method_name]['success']:
            result = results[method_name]
            duration = result['duration']
            if abs(duration - 0.1) < 0.01:
                status = "✅ 强制0.1s"
            elif duration > 0.1:
                status = "ℹ️ 正常检测"
            else:
                status = "❌ 阈值失效"
            axes[3].text(0.05, y_pos, f'{method_labels[method_name]}: {duration:.3f}s {status}', fontsize=10)
        else:
            axes[3].text(0.05, y_pos, f'{method_labels[method_name]}: 检测失败', fontsize=10, color='red')
        y_pos -= 0.1
    
    axes[3].set_xlim(0, 1)
    axes[3].set_ylim(0, 1)
    axes[3].axis('off')
    
    plt.tight_layout()
    plt.savefig('corrected_zero_detection.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("修正后检测逻辑图已保存为: corrected_zero_detection.png")

def test_different_zero_scenarios():
    """
    测试不同零速度点场景
    """
    print("\n=== 测试不同零速度点场景 ===")
    
    scenarios = [
        ("极早零点(0.02s)", 0.02),
        ("早期零点(0.05s)", 0.05),
        ("临界零点(0.08s)", 0.08),
        ("边界零点(0.10s)", 0.10),
        ("正常零点(0.15s)", 0.15)
    ]
    
    sample_rate = 100
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    print("场景名称          理论零点时间  检测持续时间  阈值强制状态")
    print("-" * 60)
    
    for scenario_name, zero_time in scenarios:
        try:
            # 创建特定零点时间的测试数据
            duration = 1.5
            t = np.linspace(0, duration, int(sample_rate * duration))
            
            acc_x = np.zeros_like(t)
            # 在指定时间达到零速度
            mask = (t >= 0.2) & (t < 0.2 + zero_time)
            if np.sum(mask) > 0:
                t_motion = t[mask] - 0.2
                acc_x[mask] = 3.0 * np.sin(np.pi * t_motion / zero_time)
            
            acc_y = 0.1 * np.random.normal(0, 1, len(t))
            acc_z = 0.1 * np.random.normal(0, 1, len(t))
            
            acc_data = np.column_stack([acc_x, acc_y, acc_z])
            
            # 使用三轴独立检测方法
            start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
                acc_data, 
                only_acceleration_phase=True, 
                use_integration=True, 
                detection_method='velocity_individual'
            )
            
            detected_duration = (end_idx - start_idx) / sample_rate
            
            # 判断阈值强制状态
            if zero_time < 0.1 and abs(detected_duration - 0.1) < 0.01:
                status = "✅ 强制执行"
            elif zero_time >= 0.1 and detected_duration >= 0.1:
                status = "ℹ️ 正常检测"
            elif zero_time < 0.1 and detected_duration < 0.1:
                status = "❌ 强制失效"
            else:
                status = "⚠️ 异常情况"
            
            print(f"{scenario_name:16} {zero_time:12.2f}s {detected_duration:12.3f}s   {status}")
            
        except Exception as e:
            print(f"{scenario_name:16} {zero_time:12.2f}s {'失败':>12}   检测错误")

if __name__ == "__main__":
    print("修正后的零速度点检测逻辑测试")
    print("=" * 60)
    
    print("修正说明:")
    print("- 原逻辑: 从0.1s后开始寻找零速度点")
    print("- 新逻辑: 从第一个样本点开始寻找零速度点")
    print("- 阈值强制: 如果零速度点<0.1s，最终输出强制设置为0.1s")
    print()
    
    # 测试修正后的检测逻辑
    test_corrected_zero_detection()
    
    # 分析检测过程
    analyze_zero_detection_process()
    
    # 生成可视化
    visualize_corrected_detection()
    
    # 测试不同场景
    test_different_zero_scenarios()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n修正总结:")
    print("1. ✅ 从第一个样本点开始寻找零速度点")
    print("2. ✅ 通过一正一负判断零速度点")
    print("3. ✅ 在0时刻到零速度点之间找绝对值最大速度")
    print("4. ✅ 如果零速度点<0.1s，强制最终输出为0.1s")
    print("5. ✅ 修正了原来的逻辑错误")
    print("\n现在的检测逻辑更加符合用户需求！")
