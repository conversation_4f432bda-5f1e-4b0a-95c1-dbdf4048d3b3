# -*- coding:utf-8 -*-
"""
作者：${何志想}
日期：2024年08月11日
"""
import matplotlib as mpl
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import os
import scipy.io.wavfile as wav
import pandas as pd
from scipy.stats import zscore
from scipy.fftpack import fft
from Auto_divide import Auto_divideSegment
from fliter_data import read_data, calcu_var
from interpolation import sort_and_clean_data, interpolate_acc_data

# 设置中文字体支持
def set_chinese_font():
    """配置matplotlib使用中文字体"""
    # 检查常见的中文字体
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'STSong', 'KaiTi', 'FangSong']
    font_found = False
    
    for font_name in chinese_fonts:
        font_path = fm.findfont(fm.FontProperties(family=font_name))
        if os.path.exists(font_path) and 'ttf' in font_path:
            plt.rcParams['font.family'] = font_name
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            print(f"使用中文字体: {font_name}")
            font_found = True
            break
    
    if not font_found:
        print("警告: 未找到合适的中文字体，图表中的中文可能无法正确显示")

def FFT (Fs,data):
    L = len (data)                        # 信号长度
    N =int(np.power(2,np.ceil(np.log2(L))))    # 下一个最近二次幂
    FFT_y1 = np.abs(fft(data,N))/L*2      # N点FFT 变化,但除以信号长度
    Fre = np.arange(int(N/2))/N*Fs       # 频率坐标
    FFT_y1 = FFT_y1[range(int(N/2))]      # 取一半
    return Fre, FFT_y1

def log_transform(signal):
    return np.log1p(signal)

def calcu_total_var(data, zeroThreshold):
    cumulative_variance = np.zeros(int(data.shape[0]/window_size))
    # 对每一列数据进行归一化
    for dim in range(data.shape[1]):
        var_data = calcu_var(data[:, dim], window_size)
        cumulative_variance += var_data
    # 这里将最大值的X以上的值设置为0
    threshold = max(cumulative_variance) * zeroThreshold
    cumulative_variance = [x if x <= threshold else 0 for x in cumulative_variance]
    return cumulative_variance

def calculate_time_info(sample_rate, window_size):
    """计算采样和时间的对应关系"""
    window_time_ms = (window_size / sample_rate) * 1000
    samples_per_ms = sample_rate / 1000
    return {
        "samples_per_second": sample_rate,
        "samples_per_ms": samples_per_ms,
        "window_time_ms": window_time_ms,
        "window_size": window_size
    }

def convert_samples_to_time(sample_index, sample_rate):
    """将样本点索引转换为时间（秒）"""
    return sample_index / sample_rate

def format_segments_info(segments_starts, segments_ends, sample_rate, window_size, start_seg, end_seg):
    """将检测到的片段信息格式化为文本，只包含指定范围内的片段"""
    segments_text = ""
    for i, (start, end) in enumerate(zip(segments_starts, segments_ends)):
        # 只处理在指定范围内的片段
        if start > start_seg and end < end_seg:
            # 计算时间，开始时间减少0.1秒
            start_time = (start * window_size) / sample_rate - 0.1  # 减少0.1秒
            end_time = (end * window_size) / sample_rate + 0.20
            
            # 确保开始时间不小于0
            start_time = max(0, start_time)
            
            # 转换为分:秒.毫秒格式
            start_min = int(start_time // 60)
            start_sec = start_time % 60
            end_min = int(end_time // 60)
            end_sec = end_time % 60
            
            segment_info = f"""
片段 {i+1}:
开始时间: {start_min:02d}:{start_sec:06.3f}
结束时间: {end_min:02d}:{end_sec:06.3f}
持续时间: {end_time - start_time:.3f} 秒
窗口范围: {start} - {end}
"""
            segments_text += segment_info
    
    return segments_text

def visualize_audio_and_acc_variance(audio_variance, acc_variance, original_events, extended_events, gaps, window_size):
    """可视化音频方差、加速度方差和事件边界（改进版）
    
    参数:
        audio_variance: 音频方差数据
        acc_variance: 加速度方差数据
        original_events: 原始事件(开始,结束)窗口索引元组列表
        extended_events: 拓展后事件(开始,结束)窗口索引元组列表
        gaps: 空档期(开始,结束)窗口索引元组列表
        window_size: 窗口大小
    """
    # 设置中文字体
    set_chinese_font()
    
    # 创建子图，使用更大的图形尺寸
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12), sharex=True)
    
    # 绘制音频方差
    ax1.plot(audio_variance, 'b-', linewidth=1.5, label='音频方差')
    ax1.set_title('音频方差与事件检测', fontsize=16)
    ax1.set_ylabel('标准化方差', fontsize=14)
    ax1.grid(True, linestyle='--', alpha=0.7)
    
    # 标记原始事件区域
    for i, (start, end) in enumerate(original_events):
        ax1.axvspan(start, end, color='r', alpha=0.3, label='原始事件' if i == 0 else "")
    
    # 绘制加速度方差
    ax2.plot(acc_variance, 'g-', linewidth=1.5, label='加速度方差')
    ax2.set_title('加速度方差与边界拓展', fontsize=16)
    ax2.set_xlabel('窗口索引', fontsize=14)
    ax2.set_ylabel('归一化方差', fontsize=14)
    ax2.grid(True, linestyle='--', alpha=0.7)
    
    # 标记原始事件和拓展边界
    for i, ((orig_start, orig_end), (ext_start, ext_end)) in enumerate(zip(original_events, extended_events)):
        # 显示原始边界
        ax2.axvspan(orig_start, orig_end, color='r', alpha=0.3, label='原始事件' if i == 0 else "")
        # 显示拓展边界
        ax2.axvspan(ext_start, orig_start, color='y', alpha=0.5, label='前向拓展' if i == 0 else "")
        ax2.axvspan(orig_end, ext_end, color='y', alpha=0.5, label='后向拓展' if i == 0 else "")
    
    # 在两个图上都标记空档期
    for i, (start, end) in enumerate(gaps):
        ax1.axvspan(start, end, color='g', alpha=0.2, label='空档期' if i == 0 else "")
        ax2.axvspan(start, end, color='c', alpha=0.2, label='空档期' if i == 0 else "")
    
    # 添加图例，调整位置和大小
    ax1.legend(loc='upper right', fontsize=12)
    ax2.legend(loc='upper right', fontsize=12)
    
    # 设置网格和背景
    ax1.set_facecolor('#f8f8f8')
    ax2.set_facecolor('#f8f8f8')
    
    plt.tight_layout()
    return fig

def visualize_acc_data_with_events(acc_data, events, gaps, window_size, sample_rate):
    """可视化三轴加速度数据和事件区间
    
    参数:
        acc_data: 加速度数据，形状为 [N, 3]
        events: 事件(开始,结束)窗口索引元组列表
        gaps: 空档期(开始,结束)窗口索引元组列表
        window_size: 窗口大小
        sample_rate: 采样率
    """
    # 设置中文字体
    set_chinese_font()
    
    # 确保acc_data是正确的numpy数组
    if not isinstance(acc_data, np.ndarray):
        print(f"警告：acc_data不是numpy数组，类型为: {type(acc_data)}")
        try:
            # 尝试转换为numpy数组
            acc_data = np.array(acc_data)
            print(f"已将acc_data转换为numpy数组，形状: {acc_data.shape}")
        except Exception as e:
            print(f"无法将acc_data转换为numpy数组: {e}")
            # 创建一个空图形并返回
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, "无法处理加速度数据", ha='center', va='center', fontsize=14)
            plt.tight_layout()
            return fig
    
    # 检查acc_data的维度
    if acc_data.ndim == 1:
        print(f"警告：acc_data是一维数组，长度: {len(acc_data)}，将尝试转换为二维数组")
        # 对于一维数组，我们假设它只包含一个轴的数据
        acc_data = np.array([acc_data, np.zeros_like(acc_data), np.zeros_like(acc_data)]).T
        print(f"转换后acc_data形状: {acc_data.shape}")
    elif acc_data.ndim > 2:
        print(f"警告：acc_data维度过高: {acc_data.ndim}，将尝试重塑为二维数组")
        # 尝试重塑为二维数组
        try:
            acc_data = acc_data.reshape(acc_data.shape[0], -1)[:, :3]
            print(f"重塑后acc_data形状: {acc_data.shape}")
        except Exception as e:
            print(f"无法重塑acc_data: {e}")
            # 创建一个空图形并返回
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, "无法处理加速度数据", ha='center', va='center', fontsize=14)
            plt.tight_layout()
            return fig
    
    # 确保acc_data至少有3列（对应x,y,z三个轴）
    if acc_data.shape[1] < 3:
        print(f"警告：acc_data列数不足3列: {acc_data.shape[1]}，将添加零列")
        # 补充缺失的列
        missing_cols = 3 - acc_data.shape[1]
        zeros = np.zeros((acc_data.shape[0], missing_cols))
        acc_data = np.hstack((acc_data, zeros))
        print(f"添加零列后acc_data形状: {acc_data.shape}")
    
    # 创建一个大图形
    fig, axes = plt.subplots(3, 1, figsize=(16, 14), sharex=True)
    fig.suptitle('三轴加速度数据与事件区间可视化', fontsize=18)
    
    # 实际图表的有效数据范围
    data_start_time = 0  # 从0秒开始
    data_end_time = len(acc_data) / sample_rate  # 实际数据的结束时间
    
    # 计算时间轴（秒）- 使用正确的采样率
    time_axis = np.arange(len(acc_data)) / sample_rate
    
    # 打印时间轴信息，帮助调试
    print(f"时间轴范围: {time_axis.min():.2f}秒 - {time_axis.max():.2f}秒")
    print(f"加速度数据点数: {len(acc_data)}")
    
    # 轴标签和颜色
    axes_labels = ['X轴加速度', 'Y轴加速度', 'Z轴加速度']
    axes_colors = ['#FF5733', '#33A5FF', '#33FF57']
    
    # 计算事件和空档期的时间范围
    event_times = []
    for start, end in events:
        start_time = (start * window_size) / sample_rate
        end_time = (end * window_size) / sample_rate
        event_times.append((start_time, end_time))
    
    gap_times = []
    for start, end in gaps:
        start_time = (start * window_size) / sample_rate
        end_time = (end * window_size) / sample_rate
        gap_times.append((start_time, end_time))
    
    # 确定全局时间范围
    all_times = []
    if event_times:
        all_times.extend([t for pair in event_times for t in pair])
    if gap_times:
        all_times.extend([t for pair in gap_times for t in pair])
    
    # 确保数据范围至少覆盖所有事件和空档期
    if all_times:
        plot_start_time = max(0, min(all_times) - 0.5)  # 添加0.5秒缓冲
        plot_end_time = max(all_times) + 0.5  # 添加0.5秒缓冲
    else:
        plot_start_time = 0
        plot_end_time = data_end_time
        
    # 确保绘图范围不超过数据范围
    plot_start_time = max(0, plot_start_time)
    plot_end_time = min(data_end_time, plot_end_time)
    
    # 如果事件和空档占整个数据的比例很小，则适当扩展绘图范围
    data_range = data_end_time - data_start_time
    if (plot_end_time - plot_start_time) < 0.25 * data_range:  # 如果小于25%的范围
        margin = 0.5 * data_range  # 添加较大的边距
        plot_start_time = max(0, plot_start_time - margin)
        plot_end_time = min(data_end_time, plot_end_time + margin)
        
    # 额外检查：确保至少有75%的数据被显示
    if (plot_end_time - plot_start_time) < 0.75 * data_range:
        plot_start_time = data_start_time
        plot_end_time = data_end_time
    
    # 为每个轴绘制加速度数据
    for i, (ax, label, color) in enumerate(zip(axes, axes_labels, axes_colors)):
        ax.plot(time_axis, acc_data[:, i], color=color, linewidth=1.2, label=label)
        ax.set_ylabel(label, fontsize=14)
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.set_facecolor('#f8f8f8')
        ax.legend(loc='upper right', fontsize=12)
        
        # 标记事件区域
        for start, end in events:
            # 转换窗口索引到时间
            start_time = (start * window_size) / sample_rate
            end_time = (end * window_size) / sample_rate
            ax.axvspan(start_time, end_time, color='r', alpha=0.2)
            
        # 标记空档期
        for start, end in gaps:
            # 转换窗口索引到时间
            start_time = (start * window_size) / sample_rate
            end_time = (end * window_size) / sample_rate
            ax.axvspan(start_time, end_time, color='g', alpha=0.1)
    
    # 添加事件区间图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='r', alpha=0.2, label='按键事件'),
        Patch(facecolor='g', alpha=0.1, label='空档期')
    ]
    
    # 在最后一个轴添加X轴标签
    axes[-1].set_xlabel('时间（秒）', fontsize=14)
    
    # 添加图例到图形
    fig.legend(handles=legend_elements, loc='lower right', fontsize=12)
    
    # 设置X轴范围 - 确保显示所有需要的数据
    for ax in axes:
        ax.set_xlim(plot_start_time, plot_end_time)
    
    # 动态设置合适的刻度间隔
    time_range = plot_end_time - plot_start_time
    if time_range > 120:
        # 超过120秒时，每60秒一个刻度
        tick_interval = 60
    elif time_range > 60:
        # 60-120秒时，每30秒一个刻度
        tick_interval = 30
    elif time_range > 30:
        # 30-60秒时，每10秒一个刻度
        tick_interval = 10
    elif time_range > 10:
        # 10-30秒时，每5秒一个刻度
        tick_interval = 5
    elif time_range > 5:
        # 5-10秒时，每2秒一个刻度
        tick_interval = 2
    else:
        # 少于5秒时，每1秒一个刻度
        tick_interval = 1
    
    # 设置X轴刻度
    start_tick = int(plot_start_time // tick_interval) * tick_interval
    tick_positions = np.arange(start_tick, plot_end_time + tick_interval, tick_interval)
    for ax in axes:
        ax.set_xticks(tick_positions)
        ax.set_xticklabels([f"{t:.1f}" for t in tick_positions])
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.92, hspace=0.05)  # 为总标题腾出空间，减小子图间距
    plt.show()
    
    # 打印显示范围信息
    print(f"绘制范围: {plot_start_time:.2f}秒 - {plot_end_time:.2f}秒 (总时长 {plot_end_time - plot_start_time:.2f}秒，数据总长度 {data_range:.2f}秒)")
    print(f"显示比例: {(plot_end_time - plot_start_time) / data_range * 100:.1f}% 的数据")
    
    return fig

def extend_segments_with_acc_data(acc_data, events_starts, events_ends, window_size, 
                                extend_threshold=0.6, max_extend_windows=15):
    """使用加速度数据方差来拓展事件边界
    
    参数:
        acc_data: 加速度数据，形状为 [N, 3]
        events_starts: 原始事件开始窗口索引列表
        events_ends: 原始事件结束窗口索引列表
        window_size: 窗口大小（样本点数）
        extend_threshold: 方差阈值，小于此值的窗口被视为可拓展区域
        max_extend_windows: 最大拓展窗口数
        
    返回:
        extended_starts: 拓展后的开始窗口索引列表
        extended_ends: 拓展后的结束窗口索引列表
        acc_variance_norm: 归一化的加速度方差
    """
    # 计算三轴加速度数据的窗口数量
    acc_windows = len(acc_data) // window_size
    print(f"加速度数据窗口数量: {acc_windows}")
    
    # 确保有足够的数据点计算方差
    if acc_windows < 1:
        print("警告: 加速度数据点数小于窗口大小，无法计算方差")
        return events_starts, events_ends, []
    
    # 计算三轴加速度数据的方差和
    total_variance = np.zeros(acc_windows)
    for dim in range(acc_data.shape[1]):
        # 只处理完整的窗口数据
        usable_data = acc_data[:acc_windows * window_size, dim]
        var_data = calcu_var(usable_data, window_size)
        if len(var_data) > 0:
            # 确保方差数组大小匹配
            total_variance[:len(var_data)] += var_data
    
    # 正规化处理
    if np.max(total_variance) > 0:
        acc_variance_norm = total_variance / np.max(total_variance)
    else:
        acc_variance_norm = total_variance
        
    # 打印原始事件窗口范围和可用加速度数据窗口范围
    print(f"原始事件窗口范围: {min(events_starts) if events_starts else 'N/A'} - {max(events_ends) if events_ends else 'N/A'}")
    print(f"可用加速度窗口范围: 0 - {len(acc_variance_norm)-1}")
    
    # 拓展每个事件边界
    extended_starts = []
    extended_ends = []
    
    for start, end in zip(events_starts, events_ends):
        # 检查边界是否在可用加速度窗口范围内
        if start >= len(acc_variance_norm) or end >= len(acc_variance_norm):
            print(f"警告: 事件窗口范围 [{start}-{end}] 超出加速度数据范围 [0-{len(acc_variance_norm)-1}]")
            # 使用可用范围的边界
            new_start = min(start, len(acc_variance_norm)-1) if len(acc_variance_norm) > 0 else 0
            new_end = min(end, len(acc_variance_norm)-1) if len(acc_variance_norm) > 0 else 0
            extended_starts.append(new_start)
            extended_ends.append(new_end)
            continue
        
        # 向前拓展
        new_start = start
        for i in range(min(max_extend_windows, start)):
            if start-i-1 >= 0 and start-i-1 < len(acc_variance_norm):
                if acc_variance_norm[start-i-1] < extend_threshold:
                    new_start = start - i - 1
                else:
                    break
            else:
                break
        
        # 向后拓展
        new_end = end
        for i in range(min(max_extend_windows, len(acc_variance_norm) - end - 1)):
            if end+i+1 < len(acc_variance_norm):
                if acc_variance_norm[end+i+1] < extend_threshold:
                    new_end = end + i + 1
                else:
                    break
            else:
                break
        
        extended_starts.append(new_start)
        extended_ends.append(new_end)
        
    # 打印边界拓展效果
    for i, (orig_start, orig_end, ext_start, ext_end) in enumerate(
            zip(events_starts, events_ends, extended_starts, extended_ends)):
        print(f"事件 {i+1}: 原始窗口 [{orig_start}-{orig_end}] → 拓展后 [{ext_start}-{ext_end}]")
        print(f"        前向拓展: {orig_start - ext_start} 窗口，后向拓展: {ext_end - orig_end} 窗口")
        
    return extended_starts, extended_ends, acc_variance_norm

def extract_gap_segments(segments_starts, segments_ends, total_windows):
    """提取空档期片段
    
    参数:
        segments_starts: 事件开始窗口索引列表
        segments_ends: 事件结束窗口索引列表
        total_windows: 总窗口数
        
    返回:
        gaps: 空档期的(开始,结束)窗口索引元组列表
    """
    gaps = []
    
    # 处理第一个事件前的空档
    if len(segments_starts) > 0 and segments_starts[0] > 0:
        gaps.append((0, segments_starts[0]))
    
    # 处理事件之间的空档
    for i in range(len(segments_starts) - 1):
        gaps.append((segments_ends[i], segments_starts[i + 1]))
    
    # 处理最后一个事件后的空档
    if len(segments_ends) > 0 and segments_ends[-1] < total_windows:
        gaps.append((segments_ends[-1], total_windows))
    
    print(f"\n检测到 {len(gaps)} 个空档期:")
    for i, (start, end) in enumerate(gaps):
        print(f"空档 {i+1}: 窗口范围 [{start}-{end}], 长度: {end-start} 窗口")
        
    return gaps

if __name__ == '__main__':

    file_path = r"E:\Document\user001\4.21\south\Session_20250421_140648\audio_data_14-06-48.777.wav"

    original_sampling_rate, data = wav.read(file_path)

    window_size = 200 
    
    # 设置中文字体
    set_chinese_font()

    # 计算并显示时间信息
    time_info = calculate_time_info(original_sampling_rate, window_size)


    # zeroThreshold = 0.75 #0.75
    # 计算方差和(不同列方差相加)
    # cumulative_variance = calcu_total_var(data, zeroThreshold)
    variance = calcu_var(data, window_size)
    # print("len(variance):", len(variance))

    # # 将其中某一部分置为零
    # for i in range(1200, 1400):
    #     variance[i] = 0
    # for i in range(1810, 1850):
    #     variance[i] = 0

    #标记开始分割的起点和终点
    #构建横轴label(以1000为间隔)
    tick_interval = 1000
    ticks = range(0, len(variance), tick_interval)
    tick_labels = [str(tick) for tick in ticks]
    index = []
    for i in range(len(variance)):
        index.append(i)

    # 数据标准化(均值为0，方差为1)
    zscore_variance = zscore(variance)
    PTh = np.min(zscore_variance) + 0.02 # 0.05 事件检测阈值
    PTl = np.min(zscore_variance) + 0.01  # 0.02 事件检测阈值
    
    minLen = 2   # 5 事件最小长度
    mergeThresh = 50   # 20 合并阈值
    
    peakThresh = np.min(zscore_variance) + 1.0   # 0.03 峰值检测阈值

    # 开始执行分割(针对方差和),检测信号开始和结束点(移除静默部分)
    autoSeg = Auto_divideSegment(zscore_variance, PTh, PTl, minLen, mergeThresh, peakThresh)
    autoSeg.run_Auto_divideSegment()

    # 标记需要分割的信息的起点/结束点
    start_seg = 150
    end_seg = 2000

    plt.xticks(ticks, tick_labels, rotation=-35)
    for xc in [start_seg, end_seg]:
        plt.axvline(x=xc, color='k', linestyle='-', linewidth=1)
    for i in range(len(autoSeg.longSegment_start)):
        start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
        plt.axvspan(start, end, color='r', alpha=0.5, linewidth=2.5)
    plt.plot(index, zscore_variance)


    # 绘制第二幅，关注低幅度区域
    plt.figure()
    plt.xticks(ticks, tick_labels, rotation=-35)
    for xc in [start_seg, end_seg]:
        plt.axvline(x=xc, color='k', linestyle='-', linewidth=1)
    for i in range(len(autoSeg.longSegment_start)):
        start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
        plt.axvspan(start, end, color='r', alpha=0.5, linewidth=0.5)
    plt.plot(index, zscore_variance)
    plt.ylim(0, 1)

    # # 绘制第三幅，对其中部分内容放大[观察具体某一部分分割是否正确]
    # plt.figure()
    # plt.xticks(ticks, tick_labels, rotation=-35)
    # for xc in [start_seg, end_seg]:
    #     plt.axvline(x=xc, color='k', linestyle='-', linewidth=1)
    # for i in range(len(autoSeg.longSegment_start)):
    #     start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
    #     plt.axvspan(start, end, color='r', alpha=0.5, linewidth=0.5)
    # plt.plot(index, zscore_variance)
    # plt.xlim(10000, 10500)


    plt.show()

    # 开始读取并保存数据
    j = 0
    for i in range(len(autoSeg.longSegment_start)):
        start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
        if start > start_seg and end < end_seg:
            # 正确的时间换算：
            # 1. 每个window包含200个采样点
            # 2. 采样率为48000Hz，即每秒48000个采样点
            start_time = (start * window_size) / original_sampling_rate - 0.1  # 起始时间(秒)
            end_time = (end * window_size) / original_sampling_rate + 0.20   # 结束时间(秒)

            start_time = max(0, start_time)
            duration = end_time - start_time
            
            # 转换为分:秒.毫秒格式
            start_min = int(start_time // 60)
            start_sec = start_time % 60
            end_min = int(end_time // 60)
            end_sec = end_time % 60
            
            print(f"\n片段 {i+1}:")
            print(f"开始时间: {start_min:02d}:{start_sec:06.3f}")
            print(f"结束时间: {end_min:02d}:{end_sec:06.3f}")
            print(f"持续时间: {duration:.3f} 秒")
            print(f"窗口范围: {start} - {end}")

    # 生成片段信息文本
    segments_info = format_segments_info(
        autoSeg.longSegment_start, 
        autoSeg.longSegment_end,
        original_sampling_rate,
        window_size,
        start_seg,  # 添加起始点
        end_seg     # 添加结束点
    )
    
    # 保存片段信息到文件
    output_dir = os.path.dirname(file_path)
    segments_file = os.path.join(output_dir, 'segments_info.txt')
    with open(segments_file, 'w') as f:
        f.write(segments_info)
    
    print(f"\n片段信息已保存至: {segments_file}")

    # 在保存片段信息之后，添加加速度数据处理部分
    print("\n开始处理对应的加速度数据...")
    
    # 构建对应的加速度数据文件路径
    acc_file = file_path.replace('audio_', 'acce_').replace('.wav', '.csv')
    gyro_file = file_path.replace('audio_', 'gyro_').replace('.wav', '.csv')

    if os.path.exists(acc_file):
        print(f"找到对应的加速度数据文件: {acc_file}")
        
        # 读取并处理加速度数据
        original_df = pd.read_csv(acc_file)
        orginal_gyro_df = pd.read_csv(gyro_file)
        
        # 数据排序和清理
        print("\n执行数据清理和排序...")
        cleaned_df = sort_and_clean_data(original_df)
        cleaned_gyro_df = sort_and_clean_data(orginal_gyro_df)
        
        # 进行插值
        print("\n执行500Hz插值...")
        # interpolated_df = interpolate_acc_data(cleaned_df)
        
        # 保存处理后的数据
        sorted_file = acc_file.replace('.csv', '_sorted.csv')
        sorted_gyro_file = gyro_file.replace('.csv', '_sorted.csv')
        output_file = acc_file.replace('.csv', '_500hz.csv')
        
        cleaned_df.to_csv(sorted_file, index=False)
        cleaned_gyro_df.to_csv(sorted_gyro_file, index=False)
        # interpolated_df.to_csv(output_file, index=False)
        
        print(f"\n已保存排序后的数据: {sorted_file}")
        print(f"已保存插值后的数据: {output_file}")
        
        # 自动执行空档期提取
        print("\n开始提取空档期数据...")
        from extract_gap_segments import extract_gap_segments_from_audio_log, extract_and_save_gap_data
        
        segments, gaps = extract_gap_segments_from_audio_log(segments_info)
        extract_and_save_gap_data(sorted_file, gaps)
        extract_and_save_gap_data(sorted_gyro_file, gaps)
        
        # 使用加速度数据拓展事件边界
        print("\n使用加速度数据拓展事件边界...")
        original_starts = autoSeg.longSegment_start.copy()
        original_ends = autoSeg.longSegment_end.copy()
        
        # 转换加速度数据为numpy数组
        acc_data = np.column_stack([cleaned_df['x'].values, cleaned_df['y'].values, cleaned_df['z'].values])
        
        extended_starts, extended_ends, acc_variance = extend_segments_with_acc_data(
            acc_data, autoSeg.longSegment_start, autoSeg.longSegment_end, window_size, 
            extend_threshold=0.6, max_extend_windows=15
        )
        
        # 更新分段信息
        original_segments = list(zip(original_starts, original_ends))
        extended_segments = list(zip(extended_starts, extended_ends))
        
        print(f"\n原始音频检测事件数量: {len(original_segments)}")
        print(f"加速度拓展后事件数量: {len(extended_segments)}")
        
        # 提取空档期
        print("\n计算空档期...")
        total_windows = len(data) // window_size  # 使用音频数据的总窗口数
        gaps = extract_gap_segments(extended_starts, extended_ends, total_windows)
        
        # 使用拓展后的边界
        autoSeg.longSegment_start = extended_starts
        autoSeg.longSegment_end = extended_ends
        
        # 可视化音频方差、加速度方差和事件边界
        print("\n生成可视化图表...")
        variance_fig = visualize_audio_and_acc_variance(
            zscore_variance, acc_variance, 
            original_segments, extended_segments, 
            gaps, window_size
        )
        
        # 添加三轴加速度数据可视化
        print("\n生成三轴加速度数据可视化...")
        acc_fig = visualize_acc_data_with_events(
            acc_data, extended_segments, gaps, window_size, original_sampling_rate
        )
        
        # 保存可视化结果
        output_dir = os.path.dirname(file_path)
        variance_viz_file = os.path.join(output_dir, 'events_and_acc_variance.png')
        acc_viz_file = os.path.join(output_dir, 'three_axis_acc_visualization.png')
        
        variance_fig.savefig(variance_viz_file, dpi=300, bbox_inches='tight')
        acc_fig.savefig(acc_viz_file, dpi=300, bbox_inches='tight')
        
        print(f"方差分析可视化结果已保存至: {variance_viz_file}")
        print(f"三轴加速度可视化结果已保存至: {acc_viz_file}")
    
    else:
        print(f"未找到对应的加速度数据文件: {acc_file}")

    # 绘制音频检测结果图，添加标题和网格
    plt.figure(figsize=(14, 8))
    plt.xticks(ticks, tick_labels, rotation=-35)
    for xc in [start_seg, end_seg]:
        plt.axvline(x=xc, color='k', linestyle='-', linewidth=1)
    for i in range(len(autoSeg.longSegment_start)):
        start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
        plt.axvspan(start, end, color='r', alpha=0.5, linewidth=2.5)
    plt.plot(index, zscore_variance)
    plt.title('音频事件检测 - 全局视图', fontsize=16)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.xlabel('窗口索引', fontsize=14)
    plt.ylabel('标准化方差', fontsize=14)

    # 绘制第二幅，关注低幅度区域
    plt.figure(figsize=(14, 8))
    plt.xticks(ticks, tick_labels, rotation=-35)
    for xc in [start_seg, end_seg]:
        plt.axvline(x=xc, color='k', linestyle='-', linewidth=1)
    for i in range(len(autoSeg.longSegment_start)):
        start, end = autoSeg.longSegment_start[i], autoSeg.longSegment_end[i]
        plt.axvspan(start, end, color='r', alpha=0.5, linewidth=0.5)
    plt.plot(index, zscore_variance)
    plt.ylim(0, 1)
    plt.title('音频事件检测 - 低幅度区域', fontsize=16)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.xlabel('窗口索引', fontsize=14)
    plt.ylabel('标准化方差', fontsize=14)




