# -*- coding:utf-8 -*-
"""
测试16个位移图片的可视化功能
创建模拟数据来验证新的16个位移图片支持
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from individual_motion_visualization import visualize_individual_motions

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def create_test_displacement_data(num_motions=16, output_folder="test_16_motions"):
    """
    创建测试用的位移数据文件
    
    参数:
    num_motions: 要创建的运动事件数量
    output_folder: 输出文件夹
    """
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    print(f"创建 {num_motions} 个测试位移数据文件...")
    
    # 定义16种不同的运动模式
    motion_patterns = [
        # 基本方向（8个）
        (2.0, 0.0, 0.0),      # 正X方向
        (0.0, 2.0, 0.0),      # 正Y方向  
        (0.0, 0.0, 2.0),      # 正Z方向
        (-2.0, 0.0, 0.0),     # 负X方向
        (0.0, -2.0, 0.0),     # 负Y方向
        (0.0, 0.0, -2.0),     # 负Z方向
        (1.5, 1.5, 0.0),      # XY平面对角线
        (-1.5, -1.5, 0.0),    # XY平面对角线（反向）
        
        # 复合方向（8个）
        (1.0, 1.0, 1.0),      # 三维对角线
        (-1.0, -1.0, -1.0),   # 三维对角线（反向）
        (2.2, 0.5, 0.0),      # X主导，Y辅助
        (0.5, 2.2, 0.0),      # Y主导，X辅助
        (0.0, 1.8, 1.8),      # YZ平面对角线
        (1.8, 0.0, 1.8),      # XZ平面对角线
        (1.2, -1.2, 1.0),     # 复合运动1
        (-1.0, 1.2, -1.2),    # 复合运动2
    ]
    
    for i in range(num_motions):
        # 选择运动模式（如果超过16个，循环使用）
        pattern_idx = i % len(motion_patterns)
        end_x, end_y, end_z = motion_patterns[pattern_idx]
        
        # 添加一些随机变化
        noise_factor = 0.1
        end_x += np.random.normal(0, noise_factor)
        end_y += np.random.normal(0, noise_factor)
        end_z += np.random.normal(0, noise_factor)
        
        # 创建位移数据（从原点到终点的轨迹）
        num_points = 50  # 轨迹点数
        t = np.linspace(0, 1, num_points)
        
        # 使用平滑的轨迹（三次函数）
        x_traj = end_x * (3*t**2 - 2*t**3)
        y_traj = end_y * (3*t**2 - 2*t**3)
        z_traj = end_z * (3*t**2 - 2*t**3)
        
        # 创建时间戳
        timestamps = [f"00-00-{i*0.02:.3f}" for i in range(num_points)]
        
        # 创建DataFrame
        df = pd.DataFrame({
            'time': timestamps,
            'x': x_traj,
            'y': y_traj,
            'z': z_traj
        })
        
        # 保存为CSV文件
        filename = f"displacement_segment{i+1:03d}.csv"
        filepath = os.path.join(output_folder, filename)
        df.to_csv(filepath, index=False)
        
        print(f"  创建文件: {filename} (终点: {end_x:.2f}, {end_y:.2f}, {end_z:.2f})")
    
    print(f"测试数据创建完成，保存在: {output_folder}")
    return output_folder

def test_16_motions_visualization():
    """
    测试16个位移图片的可视化功能
    """
    print("=== 测试16个位移图片可视化功能 ===\n")
    
    # 创建测试数据
    test_folder = create_test_displacement_data(16)
    
    # 定义16个标准方向（与individual_motion_visualization.py中的一致）
    standard_directions_16 = [
        (2.0, 0),       # 运动事件1的标准方向：正东方向，2cm
        (0, -2.0),      # 运动事件2的标准方向：正北方向，2cm  
        (-2.0, 0),      # 运动事件3的标准方向：正西方向，2cm
        (0, 2.0),       # 运动事件4的标准方向：正南方向，2cm
        (1.5, -1.5),    # 运动事件5的标准方向：东北方向，约2.1cm
        (1.5, 1.5),     # 运动事件6的标准方向：东南方向，约2.1cm
        (-1.5, -1.5),   # 运动事件7的标准方向：西北方向，约2.1cm
        (-1.5, 1.5),    # 运动事件8的标准方向：西南方向，约2.1cm
        (2.2, 0),       # 运动事件9的标准方向：正东方向，2.2cm
        (0, -2.2),      # 运动事件10的标准方向：正北方向，2.2cm
        (-2.2, 0),      # 运动事件11的标准方向：正西方向，2.2cm
        (0, 2.2),       # 运动事件12的标准方向：正南方向，2.2cm
        (1.8, -1.8),    # 运动事件13的标准方向：东北方向，约2.5cm
        (1.8, 1.8),     # 运动事件14的标准方向：东南方向，约2.5cm
        (-1.8, -1.8),   # 运动事件15的标准方向：西北方向，约2.5cm
        (-1.8, 1.8),    # 运动事件16的标准方向：西南方向，约2.5cm
    ]
    
    # 输出文件夹
    output_folder = os.path.join(test_folder, "visualization_results")
    
    print(f"\n开始可视化分析...")
    print(f"数据文件夹: {test_folder}")
    print(f"输出文件夹: {output_folder}")
    print(f"标准方向数量: {len(standard_directions_16)}")
    
    try:
        # 执行可视化
        visualize_individual_motions(
            csv_folder=test_folder,
            acc_data=None,  # 测试数据不需要加速度计数据
            output_folder=output_folder,
            standard_directions=standard_directions_16
        )
        
        print(f"\n✅ 16个位移图片可视化测试成功！")
        print(f"生成的文件:")
        print(f"  - 总览图: {os.path.join(output_folder, 'motions_overview.png')}")
        print(f"  - 统计图: {os.path.join(output_folder, 'motion_statistics.png')}")
        print(f"  - 16个独立运动图: motion_001.png ~ motion_016.png")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_different_motion_counts():
    """
    测试不同数量的运动事件（4, 9, 16个）
    """
    print("\n=== 测试不同数量的运动事件 ===\n")
    
    test_counts = [4, 9, 16]
    
    for count in test_counts:
        print(f"测试 {count} 个运动事件...")
        
        # 创建测试数据
        test_folder = create_test_displacement_data(count, f"test_{count}_motions")
        output_folder = os.path.join(test_folder, "visualization_results")
        
        # 使用前count个标准方向
        standard_directions = [
            (2.0, 0), (0, -2.0), (-2.0, 0), (0, 2.0),
            (1.5, -1.5), (1.5, 1.5), (-1.5, -1.5), (-1.5, 1.5),
            (2.2, 0), (0, -2.2), (-2.2, 0), (0, 2.2),
            (1.8, -1.8), (1.8, 1.8), (-1.8, -1.8), (-1.8, 1.8)
        ][:count]
        
        try:
            visualize_individual_motions(
                csv_folder=test_folder,
                acc_data=None,
                output_folder=output_folder,
                standard_directions=standard_directions
            )
            print(f"  ✅ {count} 个运动事件测试成功")
            
        except Exception as e:
            print(f"  ❌ {count} 个运动事件测试失败: {e}")

def show_layout_comparison():
    """
    显示不同数量运动事件的布局对比
    """
    print("\n=== 布局对比说明 ===")
    print("不同数量运动事件的网格布局:")
    print("  1-4个运动:   2x2 网格")
    print("  5-9个运动:   3x3 网格")
    print("  10-16个运动: 4x4 网格")
    print("  17+个运动:   动态计算网格大小")
    print("\n16个运动事件的优化:")
    print("  - 使用4x4网格布局")
    print("  - 总览图尺寸调整为3x3英寸每个子图")
    print("  - 箭头和标记大小优化以保持可见性")
    print("  - 支持16种不同颜色")
    print("  - 每个运动都有专属的标准方向")

if __name__ == "__main__":
    print("16个位移图片可视化功能测试")
    print("=" * 50)
    
    # 显示布局说明
    show_layout_comparison()
    
    # 测试16个运动事件
    test_16_motions_visualization()
    
    # 测试不同数量的运动事件
    test_different_motion_counts()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n主要改进总结:")
    print("1. ✅ 支持16个位移图片的展示")
    print("2. ✅ 采用4x4网格布局")
    print("3. ✅ 优化了显示尺寸和元素大小")
    print("4. ✅ 扩展了16种不同颜色")
    print("5. ✅ 提供了16个标准方向配置")
    print("6. ✅ 保持了向后兼容性")
    print("\n可以查看生成的图片文件验证效果！")
