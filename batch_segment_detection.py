# -*- coding:utf-8 -*-
"""
批量处理IMU数据，检测事件并保存时间段
"""
from Auto_divide import Auto_divideSegment
import numpy as np
from scipy.signal import butter, filtfilt, find_peaks
from scipy.stats import zscore
import os
import re
import pandas as pd
import glob
from datetime import datetime
import sys

# 设置标准输出为UTF-8编码
sys.stdout.reconfigure(encoding='utf-8')

# 导入优化的单一事件检测器
class MotionEventDetector:
    """单一运动事件检测器"""
    
    def __init__(self, window_size=10, overlap=0.5, sample_rate=100):
        """
        初始化运动事件检测器
        
        参数:
            window_size: 窗口大小(采样点数)
            overlap: 窗口重叠比例(0-1)
            sample_rate: 采样率(Hz)
        """
        self.window_size = window_size
        self.overlap = overlap
        self.sample_rate = sample_rate
        self.step_size = int(window_size * (1 - overlap))
        
        # 检测参数 - 减小缓冲区，调整阈值
        self.energy_threshold = 0.45  # 能量阈值(相对于最大值的比例)
        self.dynamic_threshold_factor = 0.35  # 动态阈值因子 - 增加以提高灵敏度
        self.min_event_duration = 0.1  # 最小事件持续时间(秒) - 减少为更小的事件窗口
        self.max_event_duration = 8.0  # 最大事件持续时间(秒)
        self.pre_event_buffer = 0.12  # 事件前缓冲时间(秒) - 大幅减少
        self.post_event_buffer = 0.08  # 事件后缓冲时间(秒) - 大幅减少
        
        # 鞍状检测参数
        self.saddle_detection = True  # 启用鞍状检测
        self.trend_window_size = 5  # 趋势分析窗口大小
        self.saddle_sensitivity = 0.25  # 鞍状灵敏度 (0-1) - 稍微降低敏感度
        
        # 边界优化参数 - 新增
        self.edge_threshold = 0.2  # 边缘阈值，低于此值的区域被视为静态
        self.edge_optimization = True  # 启用边界优化
        self.edge_window = 3  # 边缘平滑窗口
    
    def preprocess_data(self, data):
        """
        预处理加速度数据
        
        参数:
            data: 三轴加速度数据 [N, 3]
            
        返回:
            处理后的数据
        """
        # 1. 去除重力影响 (高通滤波)
        filtered_data = self._apply_highpass_filter(data, cutoff=0.5)
        
        # 2. 去除高频噪声 (低通滤波)
        filtered_data = self._apply_lowpass_filter(filtered_data, cutoff=10.0)
        
        return filtered_data
    
    def _apply_highpass_filter(self, data, cutoff=0.5):
        """应用高通滤波器去除重力影响"""
        nyq = 0.5 * self.sample_rate
        normal_cutoff = cutoff / nyq
        b, a = butter(3, normal_cutoff, btype='high')
        
        filtered_data = np.zeros_like(data)
        for i in range(data.shape[1]):
            filtered_data[:, i] = filtfilt(b, a, data[:, i])
            
        return filtered_data
        
    def _apply_lowpass_filter(self, data, cutoff=10.0):
        """应用低通滤波器去除高频噪声"""
        nyq = 0.5 * self.sample_rate
        normal_cutoff = cutoff / nyq
        b, a = butter(3, normal_cutoff, btype='low')
        
        filtered_data = np.zeros_like(data)
        for i in range(data.shape[1]):
            filtered_data[:, i] = filtfilt(b, a, data[:, i])
            
        return filtered_data
    
    def extract_features(self, data):
        """
        提取特征 - 增强版，添加信号趋势分析
        """
        n_samples = len(data)
        n_windows = (n_samples - self.window_size) // self.step_size + 1
        
        # 创建特征数组
        features = np.zeros(n_windows)
        
        # 创建额外的趋势和变化率特征
        feature_trend = np.zeros(n_windows)
        
        # 滑动窗口计算特征
        for i in range(n_windows):
            start_idx = i * self.step_size
            end_idx = start_idx + self.window_size
            window_data = data[start_idx:end_idx]
            
            # 计算合成加速度模值
            acc_magnitude = np.sqrt(np.sum(window_data**2, axis=1))
            
            # 提取特征:
            features[i] = np.mean(acc_magnitude)
            
            # 计算变化趋势 (用于鞍状检测)
            if i >= 2:
                feature_trend[i] = features[i] - features[i-2]
        
        # 归一化特征
        features = (features - np.min(features)) / (np.max(features) - np.min(features) + 1e-10)
        
        return features, feature_trend
    
    def _detect_saddle_pattern(self, features, feature_trend):
        """检测鞍状分布模式"""
        n = len(features)
        if n < 10:  # 数据点太少，无法检测复杂模式
            return False, None, None
        
        # 查找主峰位置
        main_peak_idx = np.argmax(features)
        
        # 向前搜索鞍部起始
        saddle_start = main_peak_idx
        min_before_peak = np.inf
        min_before_peak_idx = main_peak_idx
        
        # 从峰值向左扫描至少1/4的数据长度，寻找趋势变化
        search_range = max(int(n/4), 10)
        search_start = max(0, main_peak_idx - search_range)
        
        for i in range(main_peak_idx - 1, search_start, -1):
            if features[i] < min_before_peak:
                min_before_peak = features[i]
                min_before_peak_idx = i
            
            # 检测趋势突然变化点
            if i > 1 and feature_trend[i] < 0 and feature_trend[i-1] > 0:
                if (features[main_peak_idx] - features[i]) > self.saddle_sensitivity * (features[main_peak_idx] - np.min(features)):
                    saddle_start = i
                    break
        
        # 如果没找到明显的趋势变化点，则使用最小值前的点
        if saddle_start == main_peak_idx and min_before_peak_idx < main_peak_idx:
            # 查找最小值之前的平稳区域
            for i in range(min_before_peak_idx, 0, -1):
                if features[i] > features[min_before_peak_idx] * (1 + self.saddle_sensitivity):
                    saddle_start = i
                    break
        
        # 找不到明显的鞍部起点，则尝试使用能量阈值的一半作为辅助判断
        if saddle_start == main_peak_idx:
            half_energy_threshold = np.min(features) + (np.max(features) - np.min(features)) * 0.2
            for i in range(main_peak_idx - 1, 0, -1):
                if features[i] < half_energy_threshold:
                    saddle_start = i + 1
                    break
        
        # 如果还是没找到合适的起点，就返回峰值的较早时刻
        if saddle_start == main_peak_idx:
            saddle_start = max(0, main_peak_idx - int(n/10))
        
        return True, saddle_start, main_peak_idx
    
    def _optimize_event_edges(self, features, start_idx, end_idx):
        """
        优化事件边缘，避免包含平滑的非运动部分
        
        参数:
            features: 特征序列
            start_idx: 初始起始索引
            end_idx: 初始结束索引
            
        返回:
            优化后的起始和结束索引
        """
        if not self.edge_optimization:
            return start_idx, end_idx
        
        if end_idx <= start_idx:
            return start_idx, end_idx
            
        # 计算事件内部的平均特征值和标准差
        event_features = features[start_idx:end_idx]
        event_mean = np.mean(event_features)
        peak_value = np.max(event_features)
        
        # 边缘判断阈值 - 动态计算
        edge_threshold = peak_value * self.edge_threshold
        
        # 1. 优化起始边界 - 向前寻找第一个超过阈值的点
        optimized_start = start_idx
        for i in range(start_idx, min(end_idx - 1, len(features) - 1)):
            # 使用小窗口平滑判断
            if i + self.edge_window < len(features):
                window_avg = np.mean(features[i:i+self.edge_window])
                if window_avg > edge_threshold:
                    optimized_start = i
                    break
        
        # 2. 优化结束边界 - 向后寻找最后一个超过阈值的点
        optimized_end = end_idx
        for i in range(end_idx - 1, optimized_start + 1, -1):
            if i - self.edge_window >= 0:
                window_avg = np.mean(features[i-self.edge_window:i])
                if window_avg > edge_threshold:
                    optimized_end = i
                    break
        
        # 确保优化后的事件持续时间符合最小要求
        if (optimized_end - optimized_start) * self.step_size / self.sample_rate < self.min_event_duration:
            # 如果太短，则保持原始边界
            return start_idx, end_idx
            
        return optimized_start, optimized_end
    
    def detect_event(self, features):
        """
        检测单一运动事件 - 增强型，支持鞍状分布检测
        
        参数:
            features: 特征向量序列
            
        返回:
            start_idx: 事件开始索引
            end_idx: 事件结束索引
        """
        # 计算特征趋势
        feature_trend = np.zeros_like(features)
        feature_trend[1:] = features[1:] - features[:-1]
        
        # 1. 计算自适应阈值
        dynamic_threshold = np.mean(features) + self.dynamic_threshold_factor * np.std(features)
        absolute_threshold = self.energy_threshold * np.max(features)
        threshold = min(dynamic_threshold, absolute_threshold)  # 使用更宽松的阈值
        
        # 2. 基于阈值找到潜在事件段
        above_threshold = features > threshold
        
        # 如果没有超过阈值的点，使用最高能量点作为事件
        if not np.any(above_threshold):
            peak_idx = np.argmax(features)
            min_idx = max(0, int(peak_idx - self.min_event_duration * self.sample_rate / self.step_size))
            max_idx = min(len(features)-1, int(peak_idx + self.min_event_duration * self.sample_rate / self.step_size))
            return self._optimize_event_edges(features, min_idx, max_idx)
        
        # 3. 找到连续段
        segments = self._find_continuous_segments(above_threshold)
        
        # 4. 如果有多个事件段，选择能量最高的一段
        if len(segments) > 0:
            segment_energies = [np.sum(features[start:end]) for start, end in segments]
            best_segment_idx = np.argmax(segment_energies)
            start_idx, end_idx = segments[best_segment_idx]
            
            # 5. 检测鞍状分布
            if self.saddle_detection:
                is_saddle, saddle_start, peak_idx = self._detect_saddle_pattern(features, feature_trend)
                
                # 如果检测到鞍状分布且鞍部起始点早于当前起始点，则更新起始点
                if is_saddle and saddle_start < start_idx:
                    start_idx = saddle_start
            
            # 6. 扩展事件边界以包含前后的缓冲区 - 但使用更小的缓冲区
            buffer_samples = int(self.pre_event_buffer * self.sample_rate / self.step_size)
            post_buffer_samples = int(self.post_event_buffer * self.sample_rate / self.step_size)
            
            start_idx = max(0, start_idx - buffer_samples)
            end_idx = min(len(features), end_idx + post_buffer_samples)
            
            # 7. 优化边缘以去除平滑部分
            start_idx, end_idx = self._optimize_event_edges(features, start_idx, end_idx)
            
            # 8. 确保事件在允许的持续时间范围内
            event_duration = (end_idx - start_idx) * self.step_size / self.sample_rate
            
            if event_duration < self.min_event_duration:
                # 扩展太短的事件
                additional_samples = int((self.min_event_duration - event_duration) 
                                         * self.sample_rate / self.step_size / 2)
                start_idx = max(0, start_idx - additional_samples)
                end_idx = min(len(features), end_idx + additional_samples)
                
            elif event_duration > self.max_event_duration:
                # 裁剪太长的事件，保留能量最高的部分，但确保包含鞍状结构
                target_samples = int(self.max_event_duration * self.sample_rate / self.step_size)
                
                # 找出能量最集中的区域，但确保包含主峰值
                peak_idx = np.argmax(features[start_idx:end_idx]) + start_idx
                
                # 确保峰值被包含在截取区域内
                half_target = target_samples // 2
                
                # 从峰值向两边扩展
                new_start = max(0, peak_idx - half_target)
                new_end = min(len(features), new_start + target_samples)
                
                # 如果右边界超出范围，调整左边界
                if new_end > len(features):
                    new_start = max(0, len(features) - target_samples)
                    new_end = len(features)
                
                start_idx = new_start
                end_idx = new_end
                
                # 再次优化边缘
                start_idx, end_idx = self._optimize_event_edges(features, start_idx, end_idx)
            
            return start_idx, end_idx
        
        # 如果没有找到合适的段，返回整个序列中能量最高的区域
        peak_idx = np.argmax(features)
        half_window = int(self.min_event_duration * self.sample_rate / self.step_size / 2)
        start_idx = max(0, peak_idx - half_window)
        end_idx = min(len(features) - 1, peak_idx + half_window)
        
        # 优化边缘
        return self._optimize_event_edges(features, start_idx, end_idx)
    
    def _find_continuous_segments(self, binary_vector):
        """找到二值向量中的连续段"""
        segments = []
        in_segment = False
        start_idx = 0
        
        for i, value in enumerate(binary_vector):
            if value and not in_segment:
                # 找到段的开始
                in_segment = True
                start_idx = i
            elif not value and in_segment:
                # 找到段的结束
                in_segment = False
                segments.append((start_idx, i))
        
        # 处理最后一个段
        if in_segment:
            segments.append((start_idx, len(binary_vector)))
            
        return segments
    
    def convert_window_idx_to_sample_idx(self, window_start_idx, window_end_idx):
        """将窗口索引转换为样本索引"""
        sample_start_idx = window_start_idx * self.step_size
        sample_end_idx = (window_end_idx - 1) * self.step_size + self.window_size
        
        return sample_start_idx, sample_end_idx
    
    def detect_single_motion_event(self, acc_data):
        """
        从加速度数据中检测单一运动事件 - 增强版检测整个鞍状运动
        
        参数:
            acc_data: 三轴加速度数据 [N, 3]
            
        返回:
            start_idx: 事件开始样本索引
            end_idx: 事件结束样本索引
            event_confidence: 事件检测置信度(0-1)
        """

        start_idx = 0
        end_idx = len(acc_data)
        confidence = 1.0  # 最高置信度
        
        return start_idx, end_idx, confidence


        # # 1. 预处理数据
        # processed_data = self.preprocess_data(acc_data)
        
        # # 2. 提取特征 (包括趋势分析)
        # features, feature_trend = self.extract_features(processed_data)
        
        # # 3. 检测事件 (包括鞍状检测)
        # window_start_idx, window_end_idx = self.detect_event(features)
        
        # # 4. 转换为样本索引
        # sample_start_idx, sample_end_idx = self.convert_window_idx_to_sample_idx(
        #     window_start_idx, window_end_idx)
        
        # # 确保索引在有效范围内
        # sample_start_idx = max(0, sample_start_idx)
        # sample_end_idx = min(len(acc_data), sample_end_idx)
        
        # # 5. 计算置信度
        # event_section = features[window_start_idx:window_end_idx]
        # non_event_section = np.concatenate([
        #     features[:window_start_idx], 
        #     features[window_end_idx:]
        # ]) if window_start_idx > 0 or window_end_idx < len(features) else np.array([0])
        
        # # 检查事件区域和非事件区域的特征差异
        # if len(event_section) > 0 and len(non_event_section) > 0:
        #     event_mean = np.mean(event_section)
        #     non_event_mean = np.mean(non_event_section)
            
        #     # 计算信噪比作为置信度
        #     if non_event_mean > 0:
        #         snr = event_mean / non_event_mean
        #         confidence = min(1.0, (snr - 1) / 5)  # 归一化到0-1
        #     else:
        #         confidence = 1.0
        # else:
        #     confidence = 0.5
        
        # return sample_start_idx, sample_end_idx, confidence

# 保留原有功能，同时添加新方法
def read_acc_data(file_path, encoding='utf-8'):
    """读取加速度CSV文件数据"""
    try:
        # 尝试使用指定编码读取
        df = pd.read_csv(file_path, encoding=encoding)
    except UnicodeDecodeError:
        # 如果失败，尝试其他编码
        encodings = ['gbk', 'latin1', 'iso-8859-1', 'cp1252', 'gb18030', 'cp936']
        for enc in encodings:
            try:
                df = pd.read_csv(file_path, encoding=enc)
                print(f"成功使用 {enc} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        else:  # 如果所有编码都失败
            raise ValueError(f"无法使用任何已知编码读取文件 {file_path}")
    
    # 提取xyz三轴加速度数据
    data = df[['x', 'y', 'z']].values
    return data, df

def calcu_var(data, window_size):
    """计算信号方差"""
    n_windows = int(len(data) / window_size)
    variance = np.zeros(n_windows)
    
    for i in range(n_windows):
        start_idx = i * window_size
        end_idx = (i + 1) * window_size
        window_data = data[start_idx:end_idx]
        variance[i] = np.var(window_data)
    
    return variance

def calcu_total_var(data, window_size):
    """计算三轴加速度的总方差"""
    cumulative_variance = np.zeros(int(data.shape[0]/window_size))
    for dim in range(data.shape[1]):
        var_data = calcu_var(data[:, dim], window_size)
        cumulative_variance += var_data
    return cumulative_variance

def time_to_seconds(time_str):
    """将HH-MM-SS.mmm格式转换为秒数"""
    parts = time_str.split('.')
    time_parts = parts[0].split('-')
    hours = int(time_parts[0])
    minutes = int(time_parts[1])
    seconds = int(time_parts[2])
    microseconds = int(float('0.' + parts[1]) * 1000000) if len(parts) > 1 else 0
    return hours * 3600 + minutes * 60 + seconds + microseconds / 1000000

def convert_to_timestamp(base_time, window_idx, window_size, sample_rate):
    """将窗口索引转换为实际时间戳"""
    seconds = (window_idx * window_size) / sample_rate
    base_seconds = time_to_seconds(base_time)
    total_seconds = base_seconds + seconds
    
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = total_seconds % 60
    return f"{hours:02d}-{minutes:02d}-{seconds:06.3f}"

def calculate_actual_sample_rate(df):
    """计算实际的采样率"""
    # 计算相邻时间戳之间的时间差
    time_diffs = []
    times = df['time'].values
    for i in range(1, len(times)):
        time_diff = time_to_seconds(times[i]) - time_to_seconds(times[i-1])
        if time_diff > 0:  # 排除可能的异常值
            time_diffs.append(time_diff)
    
    # 使用平均时间差计算采样率
    if len(time_diffs) > 0:
        avg_time_diff = np.mean(time_diffs)
        actual_sample_rate = 1 / avg_time_diff
        return actual_sample_rate
    else:
        return 100  # 默认采样率

# 增加预处理函数从motion_event_detector中移植过来
def apply_highpass_filter(data, cutoff=0.5, sample_rate=100):
    """应用高通滤波器去除重力影响"""
    nyq = 0.5 * sample_rate
    normal_cutoff = cutoff / nyq
    b, a = butter(3, normal_cutoff, btype='high')
    
    filtered_data = np.zeros_like(data)
    for i in range(data.shape[1]):
        filtered_data[:, i] = filtfilt(b, a, data[:, i])
        
    return filtered_data

def apply_lowpass_filter(data, cutoff=10.0, sample_rate=100):
    """应用低通滤波器去除高频噪声"""
    nyq = 0.5 * sample_rate
    normal_cutoff = cutoff / nyq
    b, a = butter(3, normal_cutoff, btype='low')
    
    filtered_data = np.zeros_like(data)
    for i in range(data.shape[1]):
        filtered_data[:, i] = filtfilt(b, a, data[:, i])
        
    return filtered_data

def preprocess_data(data, sample_rate=100):
    """预处理加速度数据"""
    # 1. 去除重力影响 (高通滤波)
    filtered_data = apply_highpass_filter(data, cutoff=0.5, sample_rate=sample_rate)
    
    # 2. 去除高频噪声 (低通滤波)
    filtered_data = apply_lowpass_filter(filtered_data, cutoff=10.0, sample_rate=sample_rate)
    
    return filtered_data

# 从motion_event_detector引入优化的边界检测算法
def optimize_event_edges(features, start_idx, end_idx, edge_threshold=0.2, edge_window=3):
    """优化事件边缘，避免包含平滑的非运动部分"""
    if end_idx <= start_idx:
        return start_idx, end_idx
        
    # 计算事件内部的平均特征值和标准差
    event_features = features[start_idx:end_idx]
    peak_value = np.max(features)
    
    # 边缘判断阈值 - 动态计算
    threshold = peak_value * edge_threshold
    
    # 1. 优化起始边界 - 向前寻找第一个超过阈值的点
    optimized_start = start_idx
    for i in range(start_idx, min(end_idx - 1, len(features) - 1)):
        # 使用小窗口平滑判断
        if i + edge_window < len(features):
            window_avg = np.mean(features[i:i+edge_window])
            if window_avg > threshold:
                optimized_start = i
                break
    
    # 2. 优化结束边界 - 向后寻找最后一个超过阈值的点
    optimized_end = end_idx
    for i in range(end_idx - 1, optimized_start + 1, -1):
        if i - edge_window >= 0:
            window_avg = np.mean(features[i-edge_window:i])
            if window_avg > threshold:
                optimized_end = i
                break
    
    return optimized_start, optimized_end

def detect_saddle_pattern(features, trend_window_size=5, sensitivity=0.25):
    """检测鞍状分布模式"""
    n = len(features)
    if n < 10:  # 数据点太少，无法检测复杂模式
        return False, None, None
    
    # 计算趋势
    trend = np.zeros_like(features)
    for i in range(2, len(features)):
        trend[i] = features[i] - features[i-2]
    
    # 查找主峰位置
    main_peak_idx = np.argmax(features)
    
    # 向前搜索鞍部起始
    saddle_start = main_peak_idx
    min_before_peak = np.inf
    min_before_peak_idx = main_peak_idx
    
    # 从峰值向左扫描至少1/4的数据长度，寻找趋势变化
    search_range = max(int(n/4), 10)
    search_start = max(0, main_peak_idx - search_range)
    
    for i in range(main_peak_idx - 1, search_start, -1):
        if features[i] < min_before_peak:
            min_before_peak = features[i]
            min_before_peak_idx = i
        
        # 检测趋势突然变化点
        if i > 1 and trend[i] < 0 and trend[i-1] > 0:
            if (features[main_peak_idx] - features[i]) > sensitivity * (features[main_peak_idx] - np.min(features)):
                saddle_start = i
                break
    
    # 如果没找到合适的起点，尝试其他方法
    if saddle_start == main_peak_idx:
        half_energy_threshold = np.min(features) + (np.max(features) - np.min(features)) * 0.2
        for i in range(main_peak_idx - 1, 0, -1):
            if features[i] < half_energy_threshold:
                saddle_start = i + 1
                break
    
    return True, saddle_start, main_peak_idx

def optimize_event_boundary(variance_data, start_idx, end_idx, window_size=5, std_ratio=0.2):
    """
    优化事件边界，通过回溯检查方差的变化来调整结束点
    """
    # 计算整体标准差作为参考
    global_std = np.std(variance_data[start_idx:end_idx])
    std_threshold = global_std * std_ratio
    
    # 从结束点向前回溯
    optimized_end = end_idx
    for i in range(end_idx - window_size, start_idx + window_size, -1):
        # 确保索引在有效范围内
        if i < 0 or i+window_size >= len(variance_data):
            continue
        
        # 计算局部窗口的标准差
        local_std = np.std(variance_data[i:i+window_size])
        local_mean = np.mean(variance_data[i:i+window_size])
        global_mean = np.mean(variance_data[start_idx:end_idx])
        
        # 如果局部标准差很小且均值接近静态水平，认为是静态部分
        if local_std < std_threshold and local_mean < global_mean * 0.3:
            optimized_end = i + window_size
            break
    
    return start_idx, optimized_end

def post_process_events(variance_data, events, window_size=5, std_ratio=0.2):
    """
    对检测到的所有事件进行后处理
    """
    optimized_events = []
    for start, end in events:
        opt_start, opt_end = optimize_event_boundary(
            variance_data, start, end, 
            window_size=window_size, 
            std_ratio=std_ratio
        )
        optimized_events.append((opt_start, opt_end))
    return optimized_events

def process_file(file_path, output_file, window_size=10, fail_counter=None):
    """处理单个文件并将结果写入输出文件"""
    try:
        # 读取数据
        data, df = read_acc_data(file_path)
        file_name = os.path.basename(file_path)
        
        # 获取基准时间和计算采样率
        base_time = df['time'].iloc[0]
        actual_sample_rate = calculate_actual_sample_rate(df)
        
        # 数据预处理 - 应用过滤器
        processed_data = preprocess_data(data, sample_rate=actual_sample_rate)
        
        # 计算方差
        variance = calcu_total_var(processed_data, window_size)
        
        # 数据标准化
        zscore_variance = zscore(variance)
        
        # 设置检测参数 - 使用优化后的参数
        PTh = np.min(zscore_variance) + 2.0  # 降低阈值以更好捕获鞍状起始
        PTl = np.min(zscore_variance) + 1.0
        minLen = 1
        mergeThresh = 1
        peakThresh = np.min(zscore_variance) + 1
        
        # 执行分割
        autoSeg = Auto_divideSegment(zscore_variance, PTh, PTl, minLen, mergeThresh, peakThresh)
        autoSeg.run_Auto_divideSegment()
        
        # 获取事件列表
        events = list(zip(autoSeg.longSegment_start, autoSeg.longSegment_end))
        
        # 设置区间范围
        start_seg = 0
        end_seg = len(variance)
        
        # 将检测结果写入输出文件
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"\n文件: {file_name}\n")
            f.write(f"基准时间: {base_time}\n")
            f.write(f"采样率: {actual_sample_rate:.2f} Hz\n")
            f.write("-" * 50 + "\n")
            
            # 鞍状检测并优化事件列表
            optimized_events = []
            
            # 如果没有检测到事件，尝试应用鞍状检测
            if len(events) == 0:
                # 查找信号中的主峰值及其鞍状结构
                is_saddle, saddle_start, peak_idx = detect_saddle_pattern(zscore_variance)
                if is_saddle:
                    # 使用鞍状起点和峰值后的一定范围作为事件
                    event_end = min(len(zscore_variance)-1, peak_idx + int(actual_sample_rate * 0.5 / window_size))
                    events = [(saddle_start, event_end)]
            
            # 对每个事件应用边界优化
            for start, end in events:
                if start > start_seg and end < end_seg:
                    # 先应用鞍状检测
                    is_saddle, saddle_start, _ = detect_saddle_pattern(zscore_variance[max(0, start-20):end+1])
                    if is_saddle and saddle_start + max(0, start-20) < start:
                        start = max(0, saddle_start + max(0, start-20))
                    
                    # 再优化边界
                    opt_start, opt_end = optimize_event_edges(
                        zscore_variance, start, end, 
                        edge_threshold=0.2, edge_window=3
                    )
                    
                    # 确保事件不会太短
                    if (opt_end - opt_start) * window_size / actual_sample_rate >= 0.1:
                        optimized_events.append((opt_start, opt_end))
            
            # 更新事件列表
            if optimized_events:
                events = optimized_events
            else:
                # 后处理优化事件边界（如果没有边界优化成功）
                events = post_process_events(zscore_variance, events, 
                                          window_size=5, std_ratio=0.2)
            
            # 记录检测到的事件时间段
            event_count = 0
            for start, end in events:
                if start > start_seg and end < end_seg:
                    event_count += 1
                    start_time = convert_to_timestamp(base_time, start, window_size, actual_sample_rate)
                    end_time = convert_to_timestamp(base_time, end, window_size, actual_sample_rate)
                    duration = (end - start) * window_size / actual_sample_rate
                    
                    f.write(f"事件 #{event_count}:\n")
                    f.write(f"  起始时间: {start_time}\n")
                    f.write(f"  结束时间: {end_time}\n")
                    f.write(f"  持续时间: {duration:.3f} 秒\n")
                    f.write(f"  窗口索引: {start} - {end}\n\n")
            

            
            if event_count == 0:
                f.write("未检测到任何事件\n\n")
                if fail_counter is not None:
                    fail_counter[0] += 1
            else:
                f.write(f"共检测到 {event_count} 个事件\n\n")
        
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时发生错误: {str(e)}")
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"\n文件: {file_path}\n")
            f.write(f"处理错误: {str(e)}\n\n")
        return False

def process_file_with_new_detector(file_path, output_file, fail_counter=None):
    """使用新的MotionEventDetector处理单个文件并将结果写入输出文件"""
    try:
        # 读取数据
        data, df = read_acc_data(file_path)
        file_name = os.path.basename(file_path)
        
        # 获取基准时间和计算采样率
        base_time = df['time'].iloc[0]
        actual_sample_rate = calculate_actual_sample_rate(df)
        
        # 创建事件检测器
        detector = MotionEventDetector(
            window_size=int(0.1 * actual_sample_rate),  # 100ms窗口
            overlap=0.85,  # 85%重叠 - 提高时间分辨率
            sample_rate=actual_sample_rate
        )
        
        # 执行检测
        start_idx, end_idx, confidence = detector.detect_single_motion_event(data)
        
        # 计算事件时间戳
        start_seconds = start_idx / actual_sample_rate
        end_seconds = end_idx / actual_sample_rate
        
        base_seconds = time_to_seconds(base_time)
        start_time_seconds = base_seconds + start_seconds
        end_time_seconds = base_seconds + end_seconds
        
        # 转换为时间戳格式
        start_time = convert_timestamp_from_seconds(start_time_seconds)
        end_time = convert_timestamp_from_seconds(end_time_seconds)
        duration = end_seconds - start_seconds
        
        # 将检测结果写入输出文件
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"\n文件: {file_name}\n")
            f.write(f"基准时间: {base_time}\n")
            f.write(f"采样率: {actual_sample_rate:.2f} Hz\n")
            f.write("-" * 50 + "\n")
            
            if start_idx < end_idx and duration > 0.1:  # 仅当检测到合理的事件时
                f.write(f"事件 #1:\n")
                f.write(f"  起始时间: {start_time}\n")
                f.write(f"  结束时间: {end_time}\n")
                f.write(f"  持续时间: {duration:.3f} 秒\n")
                f.write(f"  样本索引: {start_idx} - {end_idx}\n")
                f.write(f"  置信度: {confidence:.2f}\n\n")
                f.write(f"共检测到 1 个事件\n\n")
            else:
                f.write("未检测到任何事件\n\n")
                if fail_counter is not None:
                    fail_counter[0] += 1
        
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时发生错误: {str(e)}")
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"\n文件: {file_path}\n")
            f.write(f"处理错误: {str(e)}\n\n")
        return False

def convert_timestamp_from_seconds(total_seconds):
    """将秒数转换为HH-MM-SS.mmm格式的时间戳"""
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = total_seconds % 60
    return f"{hours:02d}-{minutes:02d}-{seconds:06.3f}"

def main():
    # 设置参数
    folder_path = r"E:\Document\user001\7.25\Session_20250725_225125 - 副本\output_world_coordinates"
    use_new_detector = True  # 默认使用新的检测器
    
    if not os.path.exists(folder_path):
        print(f"路径 {folder_path} 不存在!")
        return
    
    # 创建输出文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(folder_path, f"event_detection_results_{timestamp}.txt")
    
    # 写入文件头
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("加速度数据事件检测结果\n")
        f.write(f"检测算法: {'优化的鞍状检测算法' if use_new_detector else '传统分割算法'}\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"处理文件夹: {folder_path}\n")
        f.write("=" * 50 + "\n")
    
    # 查找所有匹配的CSV文件
    all_csv_files = glob.glob(os.path.join(folder_path, "**", "*.csv"), recursive=True)
    # 匹配文件名
    segment_files = [
        f for f in all_csv_files 
        if re.match(r"^segment\d+_world_acce_\d{2}-\d{2}-\d{2}\.\d{3}\.csv$", os.path.basename(f))
        # if re.match(r"^segment\d+_acce_\d{2}-\d{2}-\d{2}\.\d{3}\.csv$", os.path.basename(f))
    ]
    
    # 如果没有找到segment文件，尝试查找其他命名的加速度数据文件
    if len(segment_files) == 0:
        print("未找到segment文件，尝试查找其他加速度数据文件...")
        segment_files = [
            f for f in all_csv_files 
            if (os.path.basename(f).startswith('acce_data') or 
                os.path.basename(f).startswith('linacc_data')) and 
                f.endswith('.csv')
        ]
    
    if len(segment_files) == 0:
        print(f"在 {folder_path} 下未找到任何匹配的CSV文件!")
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write("\n未找到任何匹配的CSV文件!\n")
        return
    
    print(f"找到 {len(segment_files)} 个文件，开始处理...")
    success_count = 0
    fail_counter = [0]  # 使用列表以便在函数内修改
    
    # 处理每个文件
    for i, file_path in enumerate(segment_files):
        print(f"({i+1}/{len(segment_files)}) 处理文件: {os.path.basename(file_path)}")
        
        if use_new_detector:
            # 使用新的检测器处理
            if process_file_with_new_detector(file_path, output_file, fail_counter):
                success_count += 1
        else:
            # 使用传统检测器处理
            window_size = 10  # 窗口大小
            if process_file(file_path, output_file, window_size, fail_counter):
                success_count += 1
    
    # 写入统计信息
    with open(output_file, 'a', encoding='utf-8') as f:
        f.write("=" * 50 + "\n")
        f.write(f"处理统计: 共 {len(segment_files)} 个文件，成功 {success_count} 个，失败 {len(segment_files) - success_count} 个\n")
        f.write(f"未检测到事件的文件数: {fail_counter[0]} 个\n")
    
    print(f"\n处理完成，结果保存至: {output_file}")
    print(f"共 {len(segment_files)} 个文件，成功 {success_count} 个，失败 {len(segment_files) - success_count} 个")
    print(f"未检测到事件的文件数: {fail_counter[0]} 个")

if __name__ == '__main__':
    main()
