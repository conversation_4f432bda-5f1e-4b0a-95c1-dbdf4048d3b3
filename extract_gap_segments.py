import pandas as pd
import os
from datetime import datetime, timed<PERSON><PERSON>

def extract_gap_segments_from_audio_log(log_text):
    """解析音频日志，提取片段信息并计算空档期"""
    segments = []
    lines = log_text.strip().split('\n')
    current_time = {'start': None, 'end': None}
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        if '开始时间:' in line:
            time_str = line.split(':', 1)[1].strip()
            if ':' in time_str:  # 处理 MM:SS.mmm 格式
                minutes, seconds = time_str.split(':')
                current_time['start'] = float(minutes) * 60 + float(seconds)
        elif '结束时间:' in line:
            time_str = line.split(':', 1)[1].strip()
            if ':' in time_str:  # 处理 MM:SS.mmm 格式
                minutes, seconds = time_str.split(':')
                current_time['end'] = float(minutes) * 60 + float(seconds)
                if current_time['start'] is not None:  # 确保有开始时间
                    segments.append((current_time['start'], current_time['end']))
                    current_time = {'start': None, 'end': None}  # 重置当前片段
    
    # 计算空档期
    gaps = []
    if len(segments) > 1:
        segments.sort()  # 确保按时间顺序排序
        for i in range(len(segments) - 1):
            current_end = segments[i][1]      # 当前片段的结束时间
            next_start = segments[i + 1][0]   # 下一片段的开始时间
            if next_start > current_end:      # 确保有空档
                gaps.append((current_end, next_start))
    
    return segments, gaps

def convert_to_world_time(relative_time, base_timestamp):
    """将相对时间（秒）转换为世界时间"""
    base_time = datetime.strptime(base_timestamp, '%H-%M-%S.%f')
    delta = timedelta(seconds=relative_time)
    world_time = base_time + delta
    return world_time.strftime('%H-%M-%S.%f')[:-3]

def calc_absolute_time(base_time_str, offset_seconds):
    """
    计算基准时间加上偏移后的时间
    Args:
        base_time_str: 基准时间 (HH-MM-SS.fff)
        offset_seconds: 偏移秒数
    Returns:
        绝对时间字符串 (HH-MM-SS.fff)
    """
    # 解析基准时间
    base_time = datetime.strptime(base_time_str, '%H-%M-%S.%f')
    # 添加偏移
    absolute_time = base_time + timedelta(seconds=offset_seconds)
    return absolute_time.strftime('%H-%M-%S.%f')[:-3]

def extract_and_save_gap_data(acc_file, gaps, output_dir=None):
    """提取并保存空档期加速度数据"""
    # 使用输入文件所在目录作为输出目录
    output_dir = os.path.dirname(acc_file) if output_dir is None else output_dir
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取加速度数据
    df = pd.read_csv(acc_file)
    
    # 获取基准时间（第一条记录的时间）
    base_timestamp = df['time'].iloc[0]
    print(f"基准时间: {base_timestamp}")
    
    # 处理每个空档期
    for i, (gap_start, gap_end) in enumerate(gaps, 1):
        # 计算实际的世界时间
        abs_start = calc_absolute_time(base_timestamp, gap_start)
        abs_end = calc_absolute_time(base_timestamp, gap_end)
        
        print(f"\n空档期 {i}:")
        print(f"基准时间: {base_timestamp}")
        print(f"相对时间区间: {gap_start:.3f}s - {gap_end:.3f}s")
        print(f"绝对时间区间: {abs_start} - {abs_end}")
        
        # 提取数据
        mask = (df['time'] >= abs_start) & (df['time'] <= abs_end)
        gap_data = df[mask]
        
        if not gap_data.empty:
            # 确定数据类型（acce或gyro）
            data_type = "acce" if "acce" in acc_file.lower() else "gyro" if "gyro" in acc_file.lower() else "grav" if "grav" in acc_file.lower() else "mag" if "mag" in acc_file.lower() else "rotvec" if "rotvec" in acc_file.lower() else "data"
            
            # 使用数据类型和序号构建输出文件名
            output_file = os.path.join(output_dir, 
                f'segment{i}_{data_type}_{abs_start.replace(":", "-")}.csv')
            gap_data.to_csv(output_file, index=False)
            
            print(f"持续时间: {gap_end - gap_start:.3f} 秒")
            print(f"数据点数: {len(gap_data)}")
            print(f"已保存至: {output_file}")
            print(f"数据类型: {data_type}")

if __name__ == "__main__":
    # 设置输入文件
    acc_file = r"E:\Study\科研\data\验证运动的相似性\5cm-1\acce_data_21-00-51.411_500hz.csv"
    segments_file = r"E:\Study\科研\data\验证运动的相似性\5cm-1\segments_info.txt"
    
    # 从文件读取片段信息
    with open(segments_file, 'r') as f:
        log_text = f.read()
    
    # 解析片段和空档期
    segments, gaps = extract_gap_segments_from_audio_log(log_text)
    
    print("\n检测到的片段:")
    for i, (start, end) in enumerate(segments):
        print(f"片段 {i+1}: {start:.3f}s - {end:.3f}s")
    
    print("\n检测到的空档期:")
    for i, (start, end) in enumerate(gaps):
        duration = end - start
        print(f"空档 {i+1}: {start:.3f}s - {end:.3f}s (持续 {duration:.3f}s)")
    
    # 提取并保存空档期数据
    extract_and_save_gap_data(acc_file, gaps)
