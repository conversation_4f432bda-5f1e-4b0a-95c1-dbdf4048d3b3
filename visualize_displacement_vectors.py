from codecs import utf_8_encode
import os
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import glob
import re
import pandas as pd

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

"""
简单的批量可视化位移向量的工具，不做平面拟合
"""

def extract_segment_number(filename):
    """从文件名中提取segment编号"""
    match = re.search(r'segment(\d+)', os.path.basename(filename))
    if match:
        return int(match.group(1))
    return None

def load_displacement_vectors(csv_folder):
    """
    从CSV文件夹中加载所有位移向量数据
    
    参数:
    csv_folder: 包含位移数据的文件夹
    
    返回:
    displacement_endpoints: 位移向量的终点坐标数组
    source_files: 对应的源文件路径
    """
    # 查找所有可能的位移向量文件
    csv_pattern = os.path.join(csv_folder, "*.csv")
    csv_files = glob.glob(csv_pattern)
    
    # 筛选出包含位移数据的文件（通常包含"segment"或"displacement"字样）
    displacement_files = [f for f in csv_files if "segment" in f.lower() and "displacement" in f.lower()]
    
    if not displacement_files:
        print(f"警告: 在 {csv_folder} 中未找到位移向量文件")
        return None, None
    
    print(f"找到 {len(displacement_files)} 个位移向量文件")
    
    displacement_endpoints = []
    source_files = []
    
    for file_path in displacement_files:
        try:
            # 尝试读取CSV文件
            df = pd.read_csv(file_path)
            
            # 检查文件结构，寻找最后一行的位移向量
            if len(df) > 0:
                last_row = df.iloc[-1]
                
                # 尝试从最后一行提取x, y, z坐标
                # 寻找可能的坐标列名
                coord_names = []
                for possible_x in ['x', 'X', 'pos_x', 'position_x']:
                    for possible_y in ['y', 'Y', 'pos_y', 'position_y']:
                        for possible_z in ['z', 'Z', 'pos_z', 'position_z']:
                            if possible_x in last_row.index and possible_y in last_row.index and possible_z in last_row.index:
                                coord_names = [possible_x, possible_y, possible_z]
                                break
                
                if not coord_names and df.shape[1] >= 3:
                    # 如果没有找到明确的坐标列名，尝试使用最后三列
                    last_row = df.iloc[-1, -3:].values
                elif coord_names:
                    # 使用找到的坐标列名
                    last_row = [last_row[col] for col in coord_names]
                else:
                    print(f"警告: 文件 {file_path} 格式不兼容")
                    continue
                
                # 确保坐标是数值型
                endpoint = np.array([float(val) for val in last_row])
                displacement_endpoints.append(endpoint)
                source_files.append(file_path)
                
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {e}")
    
    if not displacement_endpoints:
        return None, None
    
    return np.array(displacement_endpoints), source_files

def visualize_displacement_vectors(csv_folder, output_file=None):
    """
    可视化文件夹中的所有位移向量
    
    参数:
    csv_folder: 包含位移数据的文件夹
    output_file: 输出图像的文件路径
    """
    # 加载位移向量数据
    displacement_endpoints, source_files = load_displacement_vectors(csv_folder)
    
    if displacement_endpoints is None:
        print("无法加载位移向量数据")
        return
    
    # 提取每个向量对应的segment编号
    segment_numbers = []
    for file_path in source_files:
        segment_num = extract_segment_number(file_path)
        segment_numbers.append(segment_num if segment_num is not None else -1)
    
    # 创建图形
    fig = plt.figure(figsize=(12, 10))
    
    # 创建3D视图
    ax = fig.add_subplot(111, projection='3d')
    
    # 分配颜色
    colors = plt.cm.jet(np.linspace(0, 1, len(displacement_endpoints)))
    
    # 确定绘图范围
    max_range = max(
        np.max(np.abs(displacement_endpoints[:, 0])),
        np.max(np.abs(displacement_endpoints[:, 1])),
        np.max(np.abs(displacement_endpoints[:, 2]))
    ) * 1.2
    
    # 绘制原点
    ax.scatter([0], [0], [0], color='black', s=100, marker='o', label='原点')
    
    # 绘制位移向量
    for i, endpoint in enumerate(displacement_endpoints):
        label = f"Segment {segment_numbers[i]}" if segment_numbers[i] >= 0 else f"Vector {i+1}"
        ax.quiver(0, 0, 0, endpoint[0], endpoint[1], endpoint[2], 
                  color=colors[i], arrow_length_ratio=0.1, alpha=0.7, label=label)
    
    # 在原点绘制坐标轴
    axis_length = max_range * 0.2
    ax.quiver(0, 0, 0, axis_length, 0, 0, color='red', arrow_length_ratio=0.1, alpha=1, label='X轴')
    ax.quiver(0, 0, 0, 0, axis_length, 0, color='green', arrow_length_ratio=0.1, alpha=1, label='Y轴')
    ax.quiver(0, 0, 0, 0, 0, axis_length, color='blue', arrow_length_ratio=0.1, alpha=1, label='Z轴')
    
    # 设置坐标轴等比例
    ax.set_box_aspect([1, 1, 1])
    
    # 设置标题和标签
    ax.set_title('位移向量可视化')
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    
    # 设置坐标轴范围
    ax.set_xlim([-max_range, max_range])
    ax.set_ylim([-max_range, max_range])
    ax.set_zlim([-max_range, max_range])
    
    # 添加图例
    if len(segment_numbers) > 15:
        # 如果向量太多，只显示前15个
        handles, labels = ax.get_legend_handles_labels()
        ax.legend(handles[:15], labels[:15], loc='upper right', title='前15个向量')
    else:
        ax.legend(loc='upper right', title='所有向量')
    
    # 添加文本信息
    info_text = f"共有 {len(displacement_endpoints)} 个位移向量"
    ax.text2D(0.05, 0.95, info_text, transform=ax.transAxes)
    
    # 设置视角
    ax.view_init(elev=30, azim=45)
    
    # 添加网格
    ax.grid(True)
    
    plt.tight_layout()
    
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    plt.show()
    
    # 打印向量信息
    print("\n位移向量信息:")
    for i, (endpoint, segment) in enumerate(zip(displacement_endpoints, segment_numbers)):
        label = f"Segment {segment}" if segment >= 0 else f"Vector {i+1}"
        print(f"{label}: [{endpoint[0]:.4f}, {endpoint[1]:.4f}, {endpoint[2]:.4f}], " 
              f"长度: {np.linalg.norm(endpoint):.4f}")
    
    # 计算向量之间的角度
    if len(displacement_endpoints) > 1:
        print("\n向量夹角 (度):")
        for i in range(len(displacement_endpoints)):
            for j in range(i+1, len(displacement_endpoints)):
                v1 = displacement_endpoints[i]
                v2 = displacement_endpoints[j]
                
                # 计算向量之间的夹角
                cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                angle = np.arccos(np.clip(cos_angle, -1.0, 1.0)) * 180 / np.pi
                
                label_i = f"Segment {segment_numbers[i]}" if segment_numbers[i] >= 0 else f"Vector {i+1}"
                label_j = f"Segment {segment_numbers[j]}" if segment_numbers[j] >= 0 else f"Vector {j+1}"
                
                print(f"{label_i} 与 {label_j} 之间的夹角: {angle:.2f}°")

def batch_visualize_folders(base_folder):
    """
    批量处理多个文件夹中的位移向量
    
    参数:
    base_folder: 包含多个数据子文件夹的基础路径
    """
    # 获取所有子文件夹
    subfolders = [f.path for f in os.scandir(base_folder) if f.is_dir()]
    
    if not subfolders:
        print(f"在 {base_folder} 中未找到子文件夹，直接处理该文件夹")
        output_file = os.path.join(base_folder, "位移向量可视化.png")
        visualize_displacement_vectors(base_folder, output_file)
        return
    
    print(f"找到 {len(subfolders)} 个子文件夹")
    
    # 处理每个子文件夹
    for folder in subfolders:
        folder_name = os.path.basename(folder)
        print(f"\n处理文件夹: {folder_name}")
        output_file = os.path.join(folder, "位移向量可视化.png")
        visualize_displacement_vectors(folder, output_file)

if __name__ == "__main__":
    # 指定包含CSV文件的文件夹路径
    csv_folder = r"D:\research\data\方向角度测量\10"
    
    # 输出文件路径
    output_file = os.path.join(csv_folder, "位移向量可视化.png")
    
    # 单文件夹可视化
    visualize_displacement_vectors(csv_folder, output_file)
    
    # 如果需要批量处理多个文件夹，取消下面的注释
    # base_folder = r"D:\research\data\方向角度测量"
    # batch_visualize_folders(base_folder)
