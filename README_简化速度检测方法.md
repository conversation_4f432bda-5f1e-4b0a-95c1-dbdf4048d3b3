# 简化速度检测方法

## 核心思路

根据用户需求，实现了一个更加简单直接的速度检测方法，去除了复杂的阈值和稳健性检测：

### 1. 最小检测时间阈值
- **设置**: 0.1秒
- **规则**: 没有达到0.1秒的就按0.1秒进行输出

### 2. 零速度点检测
- **方法**: 相邻样本点速度一正一负判断
- **逻辑**: `current_velocity * next_velocity < 0`
- **含义**: 速度穿越零点，表示运动方向改变

### 3. 最大速度检测
- **范围**: 从0时刻开始到零速度点的区间
- **方法**: 计算速度绝对值最大值
- **输出**: 绝对值最大值对应的时间点

### 4. 去除复杂验证
- **简化**: 不需要稳健性检测
- **直接**: 不需要多种阈值判断
- **高效**: 减少计算复杂度

## 技术实现

### 1. 零速度点检测（单轴）

```python
def _find_first_velocity_decrease_single_axis(self, velocity_axis, min_samples, axis_name):
    # 从最小时间阈值之后开始寻找零点
    for i in range(min_samples, len(velocity_axis) - 1):
        current_velocity = velocity_axis[i]
        next_velocity = velocity_axis[i + 1]
        
        # 检查相邻样本点是否一正一负（穿越零点）
        if current_velocity * next_velocity < 0:  # 一正一负，乘积为负
            print(f"检测到{axis_name}轴零速度点: 索引{i}, 时间{i/self.sample_rate:.3f}s")
            return i
    
    return None
```

### 2. 零速度点检测（合速度矢量）

```python
def _find_first_zero_velocity_point_magnitude(self, velocity_magnitude, min_samples):
    # 计算接近0的阈值（5%的最大速度）
    max_velocity = np.max(velocity_magnitude)
    zero_threshold = 0.05 * max_velocity
    
    # 从最小时间阈值之后开始寻找接近0的点
    for i in range(min_samples, len(velocity_magnitude)):
        current_velocity = velocity_magnitude[i]
        
        # 检查是否接近0
        if current_velocity <= zero_threshold:
            return i
    
    return None
```

### 3. 最大速度检测

```python
# 在零速度点之前寻找最大值
if zero_point is not None:
    search_range = velocity_axis[:zero_point + 1]
else:
    search_range = velocity_axis

# 找绝对值最大的点
max_idx = np.argmax(np.abs(search_range))
max_time = max_idx / sample_rate
```

### 4. 最小时间阈值强制执行

```python
# 检查最大速度点是否在时间阈值之前
if max_idx < min_samples:
    # 最大速度点在时间阈值之前，直接使用时间阈值
    end_idx = start_idx + min_samples
    print(f"最大速度点在时间阈值前，强制使用0.1秒")
else:
    # 最大速度点在时间阈值之后，正常使用
    end_idx = start_idx + max_idx
```

## 主要优势

### 1. 逻辑简单
- **直观判断**: 一正一负直接表示零速度点
- **无需阈值**: 不需要复杂的百分比阈值计算
- **易于理解**: 物理意义明确

### 2. 计算高效
- **减少计算**: 去除了稳健性验证的复杂计算
- **快速检测**: 简单的乘积判断，计算量小
- **内存友好**: 不需要额外的验证数组

### 3. 结果稳定
- **强制阈值**: 保证最小0.1秒的检测时间
- **明确边界**: 零速度点提供明确的搜索边界
- **避免异常**: 简单逻辑减少异常情况

## 使用方法

### 1. 自动应用

简化方法已经集成到所有velocity检测方法中：

```python
from motion_event_detector import MotionEventDetector

detector = MotionEventDetector(sample_rate=100)

# 所有方法都自动使用简化检测
result = detector.detect_single_motion_event(
    acc_data, 
    only_acceleration_phase=True,
    detection_method='velocity_individual'  # 或其他方法
)
```

### 2. 测试验证

```bash
python test_simplified_velocity_detection.py
```

## 检测流程

### 1. 三轴独立检测
```
1. 对每个轴分别计算速度
2. 寻找相邻样本点一正一负的零速度点
3. 在0时刻到零速度点之间找绝对值最大速度
4. 选择三轴中最早的时间点
5. 如果小于0.1秒，强制设置为0.1秒
```

### 2. 主轴+副轴合成检测
```
1. 确定主轴，计算副轴合成速度
2. 分别检测主轴和副轴合成的零速度点
3. 在各自的0时刻到零速度点之间找最大速度
4. 选择主轴和副轴中较早的时间点
5. 如果小于0.1秒，强制设置为0.1秒
```

### 3. 三轴合速度矢量检测
```
1. 计算三轴合速度矢量
2. 寻找接近0的点（5%最大速度阈值）
3. 在0时刻到接近零点之间找最大速度
4. 如果小于0.1秒，强制设置为0.1秒
```

## 调试信息

### 1. 零速度点检测
```
  寻找X轴零速度点（一正一负判断）
    检测到X轴零速度点: 索引120, 时间1.200s
    当前速度: 0.234, 下一点速度: -0.156
```

### 2. 最大速度检测
```
零速度点前最大速度: 索引80, 时间0.800s, 速度1.234
```

### 3. 时间阈值强制执行
```
最大速度点在时间阈值前，强制使用0.1秒
```

## 适用场景

### 1. 推荐使用
- **简单运动**: 单次加速-减速运动
- **明确边界**: 运动有明确的开始和结束
- **实时处理**: 需要快速检测的应用

### 2. 效果显著的情况
- **清晰零点**: 速度明确穿越零点的运动
- **单向运动**: 主要在一个方向上的运动
- **低噪声**: 数据质量较好的情况

## 参数配置

### 1. 时间阈值
```python
min_duration_sec = 0.1  # 最小检测时间阈值
min_samples = int(min_duration_sec * sample_rate)
```

### 2. 零速度阈值（仅用于合速度矢量）
```python
zero_threshold = 0.05 * max_velocity  # 5%最大速度
```

## 故障排除

### 1. 未找到零速度点

**问题**: 没有检测到一正一负的相邻点
**解决**: 
- 检查运动是否真的回到零速度
- 检查数据采样率是否足够
- 考虑使用全数据范围搜索

### 2. 检测时间过短

**问题**: 所有结果都是0.1秒
**解决**:
- 检查运动是否确实很短
- 验证零速度点检测是否正确
- 确认时间阈值强制执行逻辑

### 3. 检测结果不准确

**问题**: 最大速度点不合理
**解决**:
- 验证零速度点检测是否正确
- 检查速度计算是否准确
- 确认搜索范围设置

## 与原方法对比

| 特性 | 原方法 | 简化方法 |
|------|--------|----------|
| 零速度点检测 | 滑动窗口+5%减小阈值 | 相邻点一正一负 |
| 稳健性验证 | 复杂的一致性检查 | 无验证 |
| 阈值设置 | 多种百分比阈值 | 仅5%零速度阈值 |
| 计算复杂度 | 高 | 低 |
| 检测精度 | 高（但复杂） | 中等（但简单） |
| 适用场景 | 复杂运动 | 简单运动 |

## 更新日志

- **v1.9**: 实现简化速度检测方法
  - 0.1秒最小时间阈值强制执行
  - 一正一负零速度点检测
  - 去除复杂稳健性验证
  - 简化检测流程

现在的检测方法更加简单直接，符合用户的具体需求！
