import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import glob
from least_squares_plane_fit import find_optimal_projection_plane_least_squares, project_vector_to_plane

def create_plane_based_coordinates(plane_normal, plane_d):
    """
    创建以拟合平面为XY平面的坐标系变换矩阵和原点偏移
    
    参数:
    plane_normal: 平面法向量
    plane_d: 平面方程常数项 (ax + by + cz + d = 0)
    
    返回:
    rotation_matrix: 3x3旋转矩阵
    origin_offset: 原点偏移量
    """
    # 确保法向量是单位向量
    plane_normal = plane_normal / np.linalg.norm(plane_normal)
    a, b, c = plane_normal
    
    # 计算平面上的一个点作为新坐标系的原点
    # 求解平面方程 ax + by + cz + d = 0
    # 我们可以取x=0, y=0，求解z，或x=0, z=0，求解y，等
    if abs(c) > 1e-5:  # 如果c不接近0，选择z轴截距
        origin_offset = np.array([0, 0, -plane_d / c])
    elif abs(b) > 1e-5:  # 如果b不接近0，选择y轴截距
        origin_offset = np.array([0, -plane_d / b, 0])
    else:  # 否则选择x轴截距
        origin_offset = np.array([-plane_d / a, 0, 0])
    
    # 平面法向量作为新坐标系的Z轴
    new_z_axis = plane_normal
    
    # 选择一个与法向量不平行的方向作为辅助向量
    if abs(np.dot(new_z_axis, [1, 0, 0])) < 0.9:
        aux_vector = np.array([1, 0, 0])
    else:
        aux_vector = np.array([0, 1, 0])
    
    # 使用叉乘生成新坐标系的X轴（与Z轴垂直）
    new_x_axis = np.cross(aux_vector, new_z_axis)
    new_x_axis = new_x_axis / np.linalg.norm(new_x_axis)
    
    # Y轴完成右手坐标系（X轴和Z轴的叉乘）
    new_y_axis = np.cross(new_z_axis, new_x_axis)
    new_y_axis = new_y_axis / np.linalg.norm(new_y_axis)
    
    # 构建旋转矩阵
    rotation_matrix = np.vstack([new_x_axis, new_y_axis, new_z_axis]).T
    
    return rotation_matrix, origin_offset

def transform_points(points, rotation_matrix, origin_offset):
    """
    将点从原坐标系转换到以拟合平面为XY平面的新坐标系
    
    参数:
    points: 原坐标系中的点坐标，形状为(n, 3)
    rotation_matrix: 旋转矩阵
    origin_offset: 新坐标系原点在原坐标系中的坐标
    
    返回:
    transformed_points: 新坐标系中的点坐标
    """
    # 先平移，使平面上的点成为原点
    translated_points = points - origin_offset
    
    # 再旋转，使平面成为XY平面
    transformed_points = np.dot(translated_points, rotation_matrix)
    
    return transformed_points

def transform_vectors(vectors, rotation_matrix):
    """
    将向量从原坐标系转换到新坐标系
    
    参数:
    vectors: 原坐标系中的向量，形状为(n, 3)
    rotation_matrix: 旋转矩阵
    
    返回:
    transformed_vectors: 新坐标系中的向量
    """
    # 向量只需要旋转，不需要平移
    transformed_vectors = np.dot(vectors, rotation_matrix)
    
    return transformed_vectors

def extract_gravity_vector(acc_data):
    """
    从加速度数据中提取重力向量
    
    参数:
    acc_data: 加速度数据，形状为(n, 3)的numpy数组
    
    返回:
    gravity_vector: 归一化的重力向量
    """
    # 使用低通滤波或简单平均来提取重力分量
    gravity_vector = np.mean(acc_data, axis=0)
    return gravity_vector / np.linalg.norm(gravity_vector)

def create_watch_to_world_matrix(gravity_vector, motion_direction=None):
    """
    创建手表坐标系到世界坐标系的转换矩阵。
    - Z轴定义为重力向量的反方向（指向正上方）
    - X轴、Y轴在水平面内，通过右手法则定义
    
    参数:
    gravity_vector: 手表坐标系中的重力向量
    motion_direction: 可选的运动方向向量，用于确定X轴

    返回:
    rotation_matrix: 从手表坐标系到世界坐标系的旋转矩阵
    """
    # 归一化重力向量
    gravity_vector = gravity_vector / np.linalg.norm(gravity_vector)
    # Z轴为重力向量的反方向（指向正上方）
    z_axis = -gravity_vector

    # 在水平面内确定X轴
    if motion_direction is not None:
        # 保证motion_direction与重力向量垂直（投影到水平面）
        motion_direction = np.array(motion_direction)
        motion_direction = motion_direction - np.dot(motion_direction, z_axis) * z_axis
        if np.linalg.norm(motion_direction) > 1e-6:
            x_axis = motion_direction / np.linalg.norm(motion_direction)
        else:
            # 如果运动方向接近垂直，选择一个默认的X轴方向
            x_axis = np.cross([0, 1, 0], z_axis)
            if np.linalg.norm(x_axis) < 1e-6:
                x_axis = np.cross([1, 0, 0], z_axis)
            x_axis = x_axis / np.linalg.norm(x_axis)
    else:
        # 没有运动方向时，在水平面内选择一个标准方向作为X轴
        if abs(np.dot(z_axis, [1, 0, 0])) < 0.9:
            x_axis = np.cross([0, 1, 0], z_axis)
        else:
            x_axis = np.cross([0, 0, 1], z_axis)
        x_axis = x_axis / np.linalg.norm(x_axis)

    # 使用右手法则确定Y轴（Z叉乘X得到Y）
    y_axis = np.cross(z_axis, x_axis)
    y_axis = y_axis / np.linalg.norm(y_axis)

    # 构建旋转矩阵
    rotation_matrix = np.vstack([x_axis, y_axis, z_axis]).T
    
    return rotation_matrix

def transform_to_world_coordinates(points, watch_to_world_matrix):
    """将手表坐标系中的点转换到世界坐标系"""
    return np.dot(points, watch_to_world_matrix)

def visualize_plane_based_coordinates(csv_folder, acc_data=None, output_file=None):
    """可视化手表坐标系和世界坐标系中的拟合平面"""
    # 使用最小二乘法拟合平面
    plane_normal, d, displacement_endpoints = find_optimal_projection_plane_least_squares(csv_folder)
    
    if plane_normal is None:
        print("无法拟合平面")
        return None, None, None
    
    # 创建图形
    fig = plt.figure(figsize=(18, 9))
    ax1 = fig.add_subplot(121, projection='3d')
    ax2 = fig.add_subplot(122, projection='3d')
    
    # 分配颜色
    colors = plt.cm.jet(np.linspace(0, 1, len(displacement_endpoints)))
    
    # 确定重力向量的比例系数(与位移向量长度相当)
    gravity_scale = np.mean(np.linalg.norm(displacement_endpoints, axis=1))
    
    # # 如果提供了加速度数据，计算世界坐标系的转换
    # if acc_data is not None:
    #     gravity_vector = extract_gravity_vector(acc_data)
    #     print(f"\n原始重力向量: {gravity_vector}")
        
    #     # 确保重力向量是单位向量
    #     gravity_vector = gravity_vector / np.linalg.norm(gravity_vector)
        
    #     # 计算重力向量与平面法向量的夹角
    #     cos_theta = np.dot(gravity_vector, plane_normal) / (np.linalg.norm(gravity_vector) * np.linalg.norm(plane_normal))
    #     angle_degrees = np.degrees(np.arccos(abs(cos_theta)))
    #     print(f"重力向量与平面法向量夹角: {angle_degrees:.2f}°")
        
    #     # 缩放重力向量用于可视化
    #     scaled_gravity = gravity_vector * gravity_scale
        
    #     # 创建手表到世界坐标系的转换矩阵
    #     watch_to_world_matrix = create_watch_to_world_matrix(gravity_vector)
        
    #     # 验证转换后的重力向量方向
    #     world_gravity_unit = transform_to_world_coordinates(gravity_vector.reshape(1, 3), watch_to_world_matrix)[0]
    #     ideal_gravity = np.array([0, 0, -1])
    #     alignment_score = np.dot(world_gravity_unit, ideal_gravity)
    #     alignment_angle = np.degrees(np.arccos(abs(alignment_score)))
        
    #     print(f"世界坐标系中的重力单位向量: {world_gravity_unit}")
    #     print(f"与理想重力方向(-Z)的点积: {alignment_score:.6f} (应接近-1)")
    #     print(f"与理想重力方向的夹角: {alignment_angle:.4f}° (应接近0°)")
        
    #     # 世界坐标系的设置
    #     world_endpoints = transform_to_world_coordinates(displacement_endpoints, watch_to_world_matrix)
    #     world_normal = transform_to_world_coordinates(plane_normal.reshape(1, 3), watch_to_world_matrix)[0]
    #     world_d = -np.dot(world_normal, np.zeros(3))
    #     world_gravity = transform_to_world_coordinates(scaled_gravity.reshape(1, 3), watch_to_world_matrix)[0]
        
    #     # 计算重力向量在拟合平面上的投影
    #     projected_gravity = project_vector_to_plane(world_gravity, world_normal)
        
    #     # 绘制世界坐标系中的向量
    #     for i, endpoint in enumerate(world_endpoints):
    #         ax1.quiver(0, 0, 0, endpoint[0], endpoint[1], endpoint[2], 
    #                   color=colors[i], arrow_length_ratio=0.1, alpha=0.7)
        
    #     # 绘制世界坐标系参考轴
    #     axis_length = gravity_scale * 0.5
    #     # ax1.quiver(0, 0, 0, axis_length, 0, 0, color='gray', alpha=0.5, label='X-axis')
    #     # ax1.quiver(0, 0, 0, 0, axis_length, 0, color='gray', alpha=0.5, label='Y-axis')
    #     # ax1.quiver(0, 0, 0, 0, 0, axis_length, color='gray', alpha=0.5, label='Z-axis')
        
    #     # 绘制重力向量及其投影
    #     ax1.quiver(0, 0, 0, world_gravity[0], world_gravity[1], world_gravity[2],
    #               color='red', arrow_length_ratio=0.1, alpha=1, label='Gravity')
    #     ax1.quiver(0, 0, 0, projected_gravity[0], projected_gravity[1], projected_gravity[2],
    #               color='orange', arrow_length_ratio=0.1, alpha=0.6, label='Gravity Projection')
        
    #     # 使用世界坐标系的平面法向量和偏移
    #     final_normal = world_normal
    #     final_d = world_d
    #     final_endpoints = world_endpoints
    # else:
    #     # 使用原始坐标系的数据
    #     final_normal = plane_normal
    #     final_d = d
    #     final_endpoints = displacement_endpoints
    #     for i, endpoint in enumerate(final_endpoints):
    #         ax1.quiver(0, 0, 0, endpoint[0], endpoint[1], endpoint[2], 
    #                   color=colors[i], arrow_length_ratio=0.1, alpha=0.7)
            
    
    final_normal = plane_normal
    final_d = d
    final_endpoints = displacement_endpoints
    for i, endpoint in enumerate(final_endpoints):
        ax1.quiver(0, 0, 0, endpoint[0], endpoint[1], endpoint[2], 
                    color=colors[i], arrow_length_ratio=0.1, alpha=0.7)
    
    # 确定绘图范围
    max_range = max(
        np.max(np.abs(final_endpoints[:, 0])),
        np.max(np.abs(final_endpoints[:, 1])),
        np.max(np.abs(final_endpoints[:, 2]))
    ) * 1.2
    
    # 创建平面网格
    xx, yy = np.meshgrid(
        np.linspace(-max_range, max_range, 10),
        np.linspace(-max_range, max_range, 10)
    )
    
    # 计算并绘制平面
    a, b, c = final_normal
    zz = (-a * xx - b * yy - final_d) / c if abs(c) > 1e-5 else np.zeros_like(xx)
    ax1.plot_surface(xx, yy, zz, alpha=0.2, color='blue')
    
    # 转换向量到新坐标系
    rotation_matrix, origin_offset = create_plane_based_coordinates(final_normal, final_d)
    transformed_endpoints = transform_vectors(final_endpoints, rotation_matrix)
    
    # # 如果有重力向量，也转换到新坐标系
    # if acc_data is not None:
    #     transformed_gravity = transform_vectors(world_gravity.reshape(1, 3), rotation_matrix)[0]
    #     transformed_proj_gravity = transform_vectors(projected_gravity.reshape(1, 3), rotation_matrix)[0]
        
    #     # 验证平面坐标系中重力向量的Z分量
    #     gravity_z_ratio = transformed_gravity[2] / np.linalg.norm(world_gravity)
    #     xy_ratio = np.sqrt(transformed_gravity[0]**2 + transformed_gravity[1]**2) / np.linalg.norm(world_gravity)
        
    #     print(f"\n平面坐标系中的重力向量: {transformed_gravity}")
    #     print(f"重力向量Z分量比例: {gravity_z_ratio:.4f}")
    #     print(f"重力向量XY平面投影比例: {xy_ratio:.4f}")
        
    #     # 绘制转换后的重力向量及其投影
    #     ax2.quiver(0, 0, 0, transformed_gravity[0], transformed_gravity[1], transformed_gravity[2],
    #               color='red', arrow_length_ratio=0.1, alpha=1, label='Gravity')
    #     ax2.quiver(0, 0, 0, transformed_proj_gravity[0], transformed_proj_gravity[1], transformed_proj_gravity[2],
    #               color='orange', arrow_length_ratio=0.1, alpha=0.6, label='Gravity Projection')
        
        # 在平面坐标系中绘制参考轴
        # ax2.quiver(0, 0, 0, axis_length, 0, 0, color='gray', alpha=0.5, label='New X-axis')
        # ax2.quiver(0, 0, 0, 0, axis_length, 0, color='gray', alpha=0.5, label='New Y-axis')
        # ax2.quiver(0, 0, 0, 0, 0, axis_length, color='gray', alpha=0.5, label='New Z-axis')
    
    # 在新坐标系中绘制向量
    for i, endpoint in enumerate(transformed_endpoints):
        ax2.quiver(0, 0, 0, endpoint[0], endpoint[1], endpoint[2],
                  color=colors[i], arrow_length_ratio=0.1, alpha=0.7)
    
    # 在新坐标系中绘制XY平面
    ax2.plot_surface(xx, yy, np.zeros_like(xx), alpha=0.2, color='green')

    # 设置标题和标签
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    ax1.set_title('Original/World Coordinates' if acc_data is not None else 'Original Coordinates')
    
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    ax2.set_title('Plane-Based Coordinates')
    
    # 设置坐标轴范围
    ax1.set_xlim([-max_range, max_range])
    ax1.set_ylim([-max_range, max_range])
    ax1.set_zlim([-max_range, max_range])
    
    ax2.set_xlim([-max_range, max_range])
    ax2.set_ylim([-max_range, max_range])
    ax2.set_zlim([-max_range, max_range])
    
    # 添加平面方程信息
    watch_eq = f"Plane: {a:.3f}x + {b:.3f}y + {c:.3f}z + {final_d:.3f} = 0"
    ax1.text2D(0.05, 0.95, watch_eq, transform=ax1.transAxes, fontsize=9,
               bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    if acc_data is not None:
        world_info = f"Z-axis aligned with gravity (up direction)"
        ax1.text2D(0.05, 0.90, world_info, transform=ax1.transAxes, fontsize=9,
                   bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    # 设置视角
    ax1.view_init(elev=30, azim=45)
    ax2.view_init(elev=30, azim=45)
    
    # 添加图例
    ax1.legend(loc='upper right')
    ax2.legend(loc='upper right')

    plt.tight_layout()
    
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    plt.show()
    
    # 添加坐标系验证代码
    if acc_data is not None:
        # 验证坐标轴正交性
        x_axis = watch_to_world_matrix[:, 0]
        y_axis = watch_to_world_matrix[:, 1]
        z_axis = watch_to_world_matrix[:, 2]
        print("\n坐标轴正交性验证:")
        print(f"X·Y = {np.dot(x_axis, y_axis):.6f} (应接近0)")
        print(f"Y·Z = {np.dot(y_axis, z_axis):.6f} (应接近0)")
        print(f"Z·X = {np.dot(z_axis, x_axis):.6f} (应接近0)")
        
        # 验证Z轴与重力方向的对齐
        print("\n坐标轴方向验证:")
        print(f"Z轴方向: [{z_axis[0]:.4f}, {z_axis[1]:.4f}, {z_axis[2]:.4f}]")
        print(f"理想重力反方向: [0.0000, 0.0000, 1.0000]")
        print(f"重力反方向与Z轴夹角: {np.degrees(np.arccos(abs(np.dot(-gravity_vector, z_axis)))):.4f}°")
        
        # 验证转换矩阵是否是正交矩阵
        det_check = np.linalg.det(watch_to_world_matrix)
        orthogonal_check = np.allclose(
            np.dot(watch_to_world_matrix.T, watch_to_world_matrix), 
            np.eye(3), 
            rtol=1e-5, 
            atol=1e-5
        )
        print(f"\n转换矩阵行列式: {det_check:.6f} (应接近1)")
        print(f"转换矩阵正交性: {orthogonal_check}")

    return rotation_matrix, origin_offset, transformed_endpoints

def read_accelerometer_data(csv_file):
    """
    读取加速度计数据并返回numpy数组
    
    参数:
    csv_file: CSV文件路径
    
    返回:
    acc_data: shape为(n,3)的numpy数组，包含x,y,z轴的加速度数据
    """
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    
    # 提取x,y,z轴的加速度数据
    acc_data = df[['x', 'y', 'z']].values
    
    return acc_data

if __name__ == "__main__":
    # 指定包含CSV文件的文件夹路径
    csv_folder = r"D:\research\data\基向量确定\2"
    
    # 寻找加速度计数据文件
    acce_files = glob.glob(os.path.join(csv_folder, "acce_data*.csv"))
    if not acce_files:
        acce_files = glob.glob(os.path.join(csv_folder, "grav_data*.csv"))
    
    if not acce_files:
        print("未找到加速度计数据文件")
        acc_data = None
    else:
        # 读取第一个加速度计数据文件
        acc_data = read_accelerometer_data(acce_files[0])
        print(f"读取加速度计数据: {len(acc_data)} 个样本")
    
    # 输出文件路径
    output_file = os.path.join(csv_folder, "平面坐标系.png")
    
    # 可视化，传入加速度计数据
    rotation_matrix, origin_offset, transformed_endpoints = visualize_plane_based_coordinates(
        csv_folder, 
        acc_data=acc_data,
        output_file=output_file
    )
    
    # 输出转换矩阵和原点偏移
    if rotation_matrix is not None:
        print("\n坐标系转换信息:")
        print("旋转矩阵:")
        print(rotation_matrix)
        print("\n原点偏移:")
        print(origin_offset)
