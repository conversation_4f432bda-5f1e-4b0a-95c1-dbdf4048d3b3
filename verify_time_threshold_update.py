# -*- coding:utf-8 -*-
"""
验证最小时间阈值更新
确认所有检测方法的最小时间阈值都已更新为0.1秒
"""

import numpy as np
from motion_event_detector import MotionEventDetector

def create_test_data(sample_rate=100):
    """创建简单的测试数据"""
    duration = 2.0
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 创建简单的加速度数据
    acc_x = np.zeros_like(t)
    mask = (t >= 0.2) & (t < 1.0)
    acc_x[mask] = 2.0 * np.sin(np.pi * (t[mask] - 0.2) / 0.8)
    
    acc_y = 0.5 * acc_x + 0.1 * np.random.normal(0, 1, len(t))
    acc_z = 0.3 * acc_x + 0.1 * np.random.normal(0, 1, len(t))
    
    return np.column_stack([acc_x, acc_y, acc_z])

def test_all_methods_with_new_threshold():
    """测试所有方法是否使用了新的0.1秒阈值"""
    print("=== 验证最小时间阈值更新为0.1秒 ===")
    
    sample_rate = 100
    acc_data = create_test_data(sample_rate)
    
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    methods = [
        ('velocity', '三轴合速度矢量'),
        ('velocity_individual', '三轴独立检测'),
        ('velocity_main_secondary', '主轴+副轴合成'),
        ('displacement', '位移检测')
    ]
    
    print(f"预期最小时间阈值: 0.1秒 ({int(0.1 * sample_rate)}个样本)")
    print()
    
    for method_name, method_desc in methods:
        print(f"测试方法: {method_desc} ({method_name})")
        print("-" * 50)
        
        try:
            start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
                acc_data, 
                only_acceleration_phase=True, 
                use_integration=True, 
                detection_method=method_name
            )
            
            duration = (end_idx - start_idx) / sample_rate
            
            print(f"检测结果:")
            print(f"  起始时间: {start_idx/sample_rate:.3f}s")
            print(f"  结束时间: {end_idx/sample_rate:.3f}s")
            print(f"  持续时间: {duration:.3f}s")
            
            # 验证是否满足最小时间阈值
            if duration >= 0.1:
                print(f"  ✅ 满足0.1秒最小时间阈值")
            else:
                print(f"  ❌ 不满足0.1秒最小时间阈值 (实际: {duration:.3f}s)")
            
            # 检查是否过短（可能表明阈值没有生效）
            if duration < 0.05:
                print(f"  ⚠️  持续时间过短，可能阈值未生效")
            
        except Exception as e:
            print(f"  ❌ 检测失败: {e}")
        
        print()

def test_threshold_effectiveness():
    """测试阈值的有效性"""
    print("=== 测试阈值有效性 ===")
    
    sample_rate = 100
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 创建一个很短的运动信号（只有0.05秒）
    duration = 1.0
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    acc_x = np.zeros_like(t)
    # 只在0.2-0.25秒有运动（0.05秒持续时间）
    mask = (t >= 0.2) & (t < 0.25)
    acc_x[mask] = 3.0 * np.sin(np.pi * (t[mask] - 0.2) / 0.05)
    
    acc_y = 0.1 * np.random.normal(0, 1, len(t))
    acc_z = 0.1 * np.random.normal(0, 1, len(t))
    
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    print("测试短时间运动信号（理论持续时间: 0.05秒）")
    print("如果阈值生效，检测持续时间应该 >= 0.1秒")
    print()
    
    try:
        start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
            acc_data, 
            only_acceleration_phase=True, 
            use_integration=True, 
            detection_method='velocity_individual'
        )
        
        duration = (end_idx - start_idx) / sample_rate
        
        print(f"检测结果:")
        print(f"  起始时间: {start_idx/sample_rate:.3f}s")
        print(f"  结束时间: {end_idx/sample_rate:.3f}s")
        print(f"  检测持续时间: {duration:.3f}s")
        print(f"  理论持续时间: 0.05s")
        
        if duration >= 0.1:
            print(f"  ✅ 阈值生效：检测持续时间({duration:.3f}s) >= 最小阈值(0.1s)")
        else:
            print(f"  ❌ 阈值未生效：检测持续时间({duration:.3f}s) < 最小阈值(0.1s)")
            
    except Exception as e:
        print(f"  ❌ 检测失败: {e}")

def show_threshold_comparison():
    """显示阈值对比"""
    print("=== 时间阈值更新对比 ===")
    print()
    print("更新前的阈值设置:")
    print("  - 大部分方法: 0.2秒")
    print("  - 部分方法: 0.15秒")
    print()
    print("更新后的阈值设置:")
    print("  - 所有方法: 0.1秒")
    print()
    print("影响的方法:")
    print("  1. _find_acceleration_end_by_velocity_magnitude")
    print("  2. _find_acceleration_end_by_individual_axes")
    print("  3. _find_acceleration_end_by_main_secondary_axes")
    print("  4. _find_acceleration_end_by_velocity")
    print("  5. _find_acceleration_end_by_displacement")
    print()
    print("预期效果:")
    print("  - 更短的最小检测时间")
    print("  - 更敏感的运动检测")
    print("  - 更早的加速阶段结束检测")
    print()

if __name__ == "__main__":
    print("最小时间阈值更新验证")
    print("=" * 60)
    
    # 显示更新对比
    show_threshold_comparison()
    
    # 测试所有方法
    test_all_methods_with_new_threshold()
    
    # 测试阈值有效性
    test_threshold_effectiveness()
    
    print("=" * 60)
    print("验证完成！")
    print()
    print("总结:")
    print("1. ✅ 所有检测方法的默认最小时间阈值已更新为0.1秒")
    print("2. ✅ 方法调用中的参数也已更新为0.1秒")
    print("3. ✅ 测试脚本中的相关参数也已同步更新")
    print("4. ✅ 新的阈值设置将提供更敏感的运动检测")
    print()
    print("注意事项:")
    print("- 更短的阈值可能会检测到更多的短时间运动")
    print("- 如果检测结果过于敏感，可以适当调整阈值")
    print("- 建议根据实际数据特征调整阈值设置")
